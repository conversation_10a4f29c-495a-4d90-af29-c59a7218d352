#!/usr/bin/env node

/**
 * 检查服务器时区设置
 */

console.log("🌍 服务器时区信息:");
console.log("当前时间:", new Date().toString());
console.log("UTC时间:", new Date().toISOString());
console.log("本地时区:", Intl.DateTimeFormat().resolvedOptions().timeZone);
console.log("时区偏移量:", new Date().getTimezoneOffset(), "分钟");

// 测试具体的时间转换
const testUTC = "2025-07-03T09:13:47.000Z";
const date = new Date(testUTC);

console.log("\n📅 时间转换测试:");
console.log("UTC时间字符串:", testUTC);
console.log("JavaScript Date对象:", date.toString());
console.log("本地日期:", date.toLocaleDateString());
console.log("本地时间:", date.toLocaleTimeString());
console.log("年-月-日:", `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`);

// 测试中国时区转换
console.log("\n🇨🇳 中国时区转换:");
console.log("中国时间:", date.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }));
console.log("中国日期:", date.toLocaleDateString('zh-CN', { timeZone: 'Asia/Shanghai' }));

// 手动计算中国时间
const chinaTime = new Date(date.getTime() + 8 * 60 * 60 * 1000);
console.log("手动计算中国时间:", chinaTime.toISOString());
console.log("手动计算中国日期:", `${chinaTime.getUTCFullYear()}-${String(chinaTime.getUTCMonth() + 1).padStart(2, '0')}-${String(chinaTime.getUTCDate()).padStart(2, '0')}`);
