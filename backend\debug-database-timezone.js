#!/usr/bin/env node

/**
 * 调试数据库时区问题
 */

import mysql from 'mysql2/promise';

const connection = await mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'kaoqin_system',
});

console.log("🔍 数据库时区调试\n");

try {
  // 检查数据库时区设置
  const [timezoneRows] = await connection.execute('SELECT @@global.time_zone, @@session.time_zone, NOW(), UTC_TIMESTAMP()');
  console.log("📅 数据库时区设置:");
  console.log(timezoneRows[0]);

  // 查询最近的几条打卡记录
  const [records] = await connection.execute(`
    SELECT 
      id,
      user_id,
      check_type,
      created_at,
      DATE(created_at) as date_utc,
      DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) as date_china
    FROM check_records 
    ORDER BY created_at DESC 
    LIMIT 5
  `);

  console.log("\n📊 最近的打卡记录:");
  records.forEach((record, index) => {
    console.log(`记录 ${index + 1}:`);
    console.log(`  ID: ${record.id}`);
    console.log(`  用户: ${record.user_id}`);
    console.log(`  类型: ${record.check_type}`);
    console.log(`  原始时间: ${record.created_at}`);
    console.log(`  UTC日期: ${record.date_utc}`);
    console.log(`  中国日期: ${record.date_china}`);
    console.log('---');
  });

  // 测试特定日期的查询
  const testDate = '2025-07-03';
  console.log(`\n🔍 测试查询日期 ${testDate} 的记录:`);
  
  const [utcQuery] = await connection.execute(`
    SELECT COUNT(*) as count, 'UTC查询' as method
    FROM check_records 
    WHERE DATE(created_at) = ?
  `, [testDate]);

  const [chinaQuery] = await connection.execute(`
    SELECT COUNT(*) as count, 'China查询' as method
    FROM check_records 
    WHERE DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) = ?
  `, [testDate]);

  console.log("UTC查询结果:", utcQuery[0]);
  console.log("China查询结果:", chinaQuery[0]);

} catch (error) {
  console.error("数据库查询错误:", error);
} finally {
  await connection.end();
}
