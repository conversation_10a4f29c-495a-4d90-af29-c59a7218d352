#!/usr/bin/env node

/**
 * 修复7月份考勤统计数据
 */

import { recalculateAttendanceRange } from './src/services/attendanceCalculator.js';

const fixJulyAttendance = async () => {
  try {
    console.log('🔄 开始重新计算7月份考勤统计...');
    
    // 重新计算整个7月的考勤统计
    await recalculateAttendanceRange('2025-07-01', '2025-07-31');
    
    console.log('✅ 7月份考勤统计重新计算完成！');
    console.log('💡 现在可以检查月统计报表，7月1日的签到次数应该显示正确了');
    
  } catch (error) {
    console.error('❌ 重新计算失败:', error);
  } finally {
    process.exit(0);
  }
};

fixJulyAttendance();
