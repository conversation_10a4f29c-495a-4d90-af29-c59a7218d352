import { Context } from 'elysia';
import { eq, and, ne, sql, count, inArray } from 'drizzle-orm';
import { db, locations, projects, projectAssignments } from '../models/db';
import { AuthContext } from '../middleware/auth';
import { logDataChange } from '../utils/logger';
import { getClientIP } from '../utils/ip';

export interface CreateLocationRequest {
  name: string;
  address?: string;
  latitude: number;
  longitude: number;
  radius?: number;
  geofence?: any[];
  wifiSsids?: string[];
  workTimeStart?: string;
  workTimeEnd?: string;
  projectId: number;
  isMainLocation?: boolean;
}

export interface UpdateLocationRequest {
  name?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
  geofence?: any[];
  wifiSsids?: string[];
  workTimeStart?: string;
  workTimeEnd?: string;
  projectId?: number;
  isMainLocation?: boolean;
  active?: boolean;
}

/**
 * 获取所有地点（管理员）
 */
export async function getAllLocations(context: AuthContext) {
  try {
    const { page = 1, limit = 50, projectId, active } = context.query as any;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const conditions = [];
    if (projectId) {
      conditions.push(eq(locations.projectId, parseInt(projectId)));
    }
    if (active !== undefined) {
      conditions.push(eq(locations.active, active === 'true'));
    }

    const locationList = await db
      .select({
        id: locations.id,
        name: locations.name,
        address: locations.address,
        latitude: locations.latitude,
        longitude: locations.longitude,
        radius: locations.radius,
        geofence: locations.geofence,
        wifiSsids: locations.wifiSsids,
        workTimeStart: locations.workTimeStart,
        workTimeEnd: locations.workTimeEnd,
        projectId: locations.projectId,
        projectName: projects.name,
        isMainLocation: locations.isMainLocation,
        active: locations.active,
        createdAt: locations.createdAt
      })
      .from(locations)
      .leftJoin(projects, eq(locations.projectId, projects.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .limit(limit)
      .offset(offset)
      .orderBy(locations.createdAt);

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(locations)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const total = totalResult[0]?.count || 0;

    return {
      success: true,
      message: '获取地点列表成功',
      data: {
        locations: locationList,
        pagination: {
          page,
          limit,
          total
        }
      }
    };
  } catch (error) {
    console.error('Get all locations error:', error);
    return {
      success: false,
      message: '获取地点列表失败'
    };
  }
}

/**
 * 获取用户可用地点
 */
export async function getUserLocations(context: AuthContext) {
  try {
    const userId = context.user.userId;
    //console.log(`🔍 获取用户地点 - 用户ID: ${userId}`);

    // 获取用户当前活跃的工程分配
    const activeAssignments = await db
      .select({
        projectId: projectAssignments.projectId,
        role: projectAssignments.role,
        startDate: projectAssignments.startDate,
        endDate: projectAssignments.endDate,
        isActive: projectAssignments.isActive
      })
      .from(projectAssignments)
      .where(and(
        eq(projectAssignments.userId, userId),
        eq(projectAssignments.isActive, true)
      ));

    //console.log(`📋 用户活跃项目分配:`, activeAssignments);

    if (activeAssignments.length === 0) {
      //console.log(`⚠️ 用户 ${userId} 没有活跃的项目分配`);
      return {
        success: true,
        message: '获取用户可用地点成功',
        data: { locations: [] }
      };
    }

    // 获取这些工程关联的所有活跃地点
    const projectIds = activeAssignments.map(a => a.projectId);
    //console.log(`📍 查询项目ID列表:`, projectIds);

    // 先查询所有相关地点（不管active状态）
    const allLocations = await db
      .select({
        id: locations.id,
        name: locations.name,
        address: locations.address,
        latitude: locations.latitude,
        longitude: locations.longitude,
        radius: locations.radius,
        wifiSsids: locations.wifiSsids,
        workTimeStart: locations.workTimeStart,
        workTimeEnd: locations.workTimeEnd,
        projectId: locations.projectId,
        projectName: projects.name,
        isMainLocation: locations.isMainLocation,
        active: locations.active
      })
      .from(locations)
      .leftJoin(projects, eq(locations.projectId, projects.id))
      .where(inArray(locations.projectId, projectIds));

    //console.log(`🗺️ 所有相关地点 (包括非激活):`, allLocations);

    // 过滤激活的地点
    const userLocations = allLocations.filter(loc => loc.active);
    //console.log(`✅ 激活的地点:`, userLocations);

    return {
      success: true,
      message: '获取用户可用地点成功',
      data: { locations: userLocations }
    };
  } catch (error) {
    console.error('Get user locations error:', error);
    return {
      success: false,
      message: '获取用户可用地点失败'
    };
  }
}

/**
 * 获取单个地点详情
 */
export async function getLocationById(context: AuthContext) {
  try {
    const { id } = context.params as { id: string };

    if (!id || isNaN(parseInt(id))) {
      return {
        success: false,
        message: '无效的地点ID'
      };
    }

    const locationList = await db
      .select({
        id: locations.id,
        name: locations.name,
        address: locations.address,
        latitude: locations.latitude,
        longitude: locations.longitude,
        radius: locations.radius,
        geofence: locations.geofence,
        wifiSsids: locations.wifiSsids,
        workTimeStart: locations.workTimeStart,
        workTimeEnd: locations.workTimeEnd,
        projectId: locations.projectId,
        isMainLocation: locations.isMainLocation,
        active: locations.active,
        createdAt: locations.createdAt,
        updatedAt: locations.updatedAt
      })
      .from(locations)
      .where(eq(locations.id, parseInt(id)))
      .limit(1);

    if (locationList.length === 0) {
      return {
        success: false,
        message: '地点不存在'
      };
    }

    return {
      success: true,
      message: '获取地点详情成功',
      data: locationList[0]
    };
  } catch (error) {
    console.error('Get location by id error:', error);
    return {
      success: false,
      message: '获取地点详情失败'
    };
  }
}

/**
 * 创建地点
 */
export async function createLocation(context: AuthContext) {
  try {
    const locationData = context.body as CreateLocationRequest;

    // 检查工程是否存在
    const existingProject = await db
      .select()
      .from(projects)
      .where(eq(projects.id, locationData.projectId))
      .limit(1);

    if (existingProject.length === 0) {
      return {
        success: false,
        message: '指定的工程不存在'
      };
    }

    // 如果设置为主要地点，需要将该工程的其他地点设为非主要
    if (locationData.isMainLocation) {
      await db
        .update(locations)
        .set({ isMainLocation: false })
        .where(eq(locations.projectId, locationData.projectId));
    }

    // 创建地点
    const result = await db
      .insert(locations)
      .values({
        name: locationData.name,
        address: locationData.address,
        latitude: locationData.latitude.toString(),
        longitude: locationData.longitude.toString(),
        radius: locationData.radius || 100,
        geofence: locationData.geofence,
        wifiSsids: locationData.wifiSsids,
        workTimeStart: locationData.workTimeStart,
        workTimeEnd: locationData.workTimeEnd,
        projectId: locationData.projectId,
        isMainLocation: locationData.isMainLocation || false,
        active: true  // 新创建的地点默认为激活状态
      });

    // 记录地点创建日志
    await logDataChange(
      context.user.userId,
      'create_location',
      'location',
      `创建地点: ${locationData.name}`,
      null,
      {
        name: locationData.name,
        address: locationData.address,
        projectId: locationData.projectId,
        projectName: existingProject[0].name,
        isMainLocation: locationData.isMainLocation
      },
      getClientIP(context)
    );

    return {
      success: true,
      message: '创建地点成功',
      data: { id: result[0].insertId }
    };
  } catch (error) {
    console.error('Create location error:', error);
    return {
      success: false,
      message: '创建地点失败'
    };
  }
}

/**
 * 更新地点
 */
export async function updateLocation(context: AuthContext) {
  try {
    const { id } = context.params as { id: string };
    const updateData = context.body as UpdateLocationRequest;

    console.log(`🔄 更新地点请求 - ID: ${id}`);
    console.log(`📝 更新数据:`, JSON.stringify(updateData, null, 2));

    // 检查地点是否存在
    const existingLocation = await db
      .select()
      .from(locations)
      .where(eq(locations.id, parseInt(id)))
      .limit(1);

    if (existingLocation.length === 0) {
      return {
        success: false,
        message: '地点不存在'
      };
    }

    const location = existingLocation[0];

    // 如果要更新工程ID，需要验证新工程是否存在
    if (updateData.projectId && updateData.projectId !== location.projectId) {
      const newProject = await db
        .select()
        .from(projects)
        .where(eq(projects.id, updateData.projectId))
        .limit(1);

      if (newProject.length === 0) {
        return {
          success: false,
          message: '指定的工程不存在'
        };
      }
    }

    // 确定最终的工程ID（用于主要地点逻辑）
    const finalProjectId = updateData.projectId || location.projectId;

    // 如果设置为主要地点，需要将该工程的其他地点设为非主要
    if (updateData.isMainLocation && !location.isMainLocation) {
      await db
        .update(locations)
        .set({ isMainLocation: false })
        .where(and(
          eq(locations.projectId, finalProjectId),
          ne(locations.id, parseInt(id))
        ));
    }

    // 处理数值类型转换
    const processedUpdateData = {
      ...updateData,
      latitude: updateData.latitude?.toString(),
      longitude: updateData.longitude?.toString()
    };

    // 更新地点
    await db
      .update(locations)
      .set(processedUpdateData)
      .where(eq(locations.id, parseInt(id)));

    // 记录地点更新日志
    await logDataChange(
      context.user.userId,
      'update_location',
      'location',
      `更新地点: ${location.name}`,
      {
        name: location.name,
        address: location.address,
        projectId: location.projectId,
        active: location.active,
        isMainLocation: location.isMainLocation
      },
      processedUpdateData,
      getClientIP(context)
    );

    return {
      success: true,
      message: '更新地点成功'
    };
  } catch (error) {
    console.error('Update location error:', error);
    return {
      success: false,
      message: '更新地点失败'
    };
  }
}

/**
 * 删除地点
 */
export async function deleteLocation(context: AuthContext) {
  try {
    const { id } = context.params as { id: string };
    console.log(`🗑️ 删除地点请求 - ID: ${id}`);
    console.log(`👤 用户信息:`, {
      userId: context.user.userId,
      role: context.user.role,
      username: context.user.username
    });

    // 权限检查：删除地点只能是管理员
    if (context.user.role !== 'admin') {
      console.log(`❌ 权限不足 - 非管理员尝试删除地点`);
      return {
        success: false,
        message: '需要管理员权限才能删除地点'
      };
    }

    if (!id || isNaN(parseInt(id))) {
      console.error('❌ 无效的地点ID:', id);
      return {
        success: false,
        message: '无效的地点ID'
      };
    }

    const locationId = parseInt(id);

    // 检查地点是否存在
    const existingLocation = await db
      .select()
      .from(locations)
      .where(eq(locations.id, locationId))
      .limit(1);

    console.log(`📍 查找地点结果:`, existingLocation);

    if (existingLocation.length === 0) {
      console.warn(`⚠️ 地点不存在 - ID: ${locationId}`);
      return {
        success: false,
        message: '地点不存在'
      };
    }

    // 删除地点
    const deleteResult = await db
      .delete(locations)
      .where(eq(locations.id, locationId));

    console.log(`✅ 删除地点结果:`, deleteResult);

    // 记录地点删除日志
    await logDataChange(
      context.user.userId,
      'delete_location',
      'location',
      `删除地点: ${existingLocation[0].name}`,
      {
        name: existingLocation[0].name,
        address: existingLocation[0].address,
        projectId: existingLocation[0].projectId,
        active: existingLocation[0].active
      },
      null,
      getClientIP(context)
    );

    return {
      success: true,
      message: '删除地点成功'
    };
  } catch (error) {
    console.error('❌ Delete location error:', error);
    return {
      success: false,
      message: `删除地点失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 获取附近地点
 */
export async function getNearbyLocations(context: AuthContext) {
  try {
    const { latitude, longitude, radius = 1000 } = context.query as any;
    const userId = context.user.userId;

    if (!latitude || !longitude) {
      return {
        success: false,
        message: '缺少位置参数'
      };
    }

    // 获取用户当前活跃的工程分配
    const activeAssignments = await db
      .select({
        projectId: projectAssignments.projectId
      })
      .from(projectAssignments)
      .where(and(
        eq(projectAssignments.userId, userId),
        eq(projectAssignments.isActive, true)
      ));

    if (activeAssignments.length === 0) {
      return {
        success: true,
        message: '获取附近地点成功',
        data: { locations: [] }
      };
    }

    // 获取用户可访问的地点
    const projectIds = activeAssignments.map(a => a.projectId);
    const userLocations = await db
      .select({
        id: locations.id,
        name: locations.name,
        address: locations.address,
        latitude: locations.latitude,
        longitude: locations.longitude,
        radius: locations.radius,
        projectId: locations.projectId,
        projectName: projects.name
      })
      .from(locations)
      .leftJoin(projects, eq(locations.projectId, projects.id))
      .where(and(
        inArray(locations.projectId, projectIds),
        eq(locations.active, true)
      ));

    // 计算距离并筛选附近地点
    const nearbyLocations = userLocations
      .map(location => {
        const distance = calculateDistance(
          parseFloat(latitude),
          parseFloat(longitude),
          parseFloat(location.latitude),
          parseFloat(location.longitude)
        );
        return { ...location, distance };
      })
      .filter(location => location.distance <= radius)
      .sort((a, b) => a.distance - b.distance);

    return {
      success: true,
      message: '获取附近地点成功',
      data: { locations: nearbyLocations }
    };
  } catch (error) {
    console.error('Get nearby locations error:', error);
    return {
      success: false,
      message: '获取附近地点失败'
    };
  }
}

/**
 * 计算两点间距离（米）
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
