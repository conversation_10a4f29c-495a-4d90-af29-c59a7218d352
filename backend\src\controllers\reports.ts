import { eq, and, or, gte, lte, desc, sql, count, sum, avg } from 'drizzle-orm';
import { db, users, projects, checkRecords, attendanceSummary, locations } from '../models/db';
import type { AuthContext } from '../middleware/auth';
import { getChinaDateString } from '../utils/timezone';
import { recalculateAttendanceRange as recalculateAttendanceRangeService } from '../services/attendanceCalculator';

/**
 * 获取考勤统计报表
 */
export async function getAttendanceReport(context: AuthContext) {
  try {
    const query = context.query as any;
    const { 
      startDate, 
      endDate, 
      projectId, 
      userId, 
      page = 1, 
      limit = 20 
    } = query;

    // 构建查询条件
    const conditions = [];
    
    if (startDate) {
      conditions.push(gte(attendanceSummary.date, startDate));
    }
    if (endDate) {
      conditions.push(lte(attendanceSummary.date, endDate));
    }
    if (projectId) {
      conditions.push(eq(attendanceSummary.projectId, parseInt(projectId)));
    }
    if (userId) {
      conditions.push(eq(attendanceSummary.userId, parseInt(userId)));
    }

    // 查询考勤统计数据
    const reportData = await db
      .select({
        id: attendanceSummary.id,
        date: attendanceSummary.date,
        userId: attendanceSummary.userId,
        userName: users.realName,
        projectId: attendanceSummary.projectId,
        projectName: projects.name,
        checkInCount: attendanceSummary.checkInCount,
        checkOutCount: attendanceSummary.checkOutCount,
        firstCheckIn: attendanceSummary.firstCheckIn,
        lastCheckOut: attendanceSummary.lastCheckOut,
        totalWorkMinutes: attendanceSummary.totalWorkMinutes,
        isAbnormal: attendanceSummary.isAbnormal,
      })
      .from(attendanceSummary)
      .leftJoin(users, eq(attendanceSummary.userId, users.id))
      .leftJoin(projects, eq(attendanceSummary.projectId, projects.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(attendanceSummary.date))
      .limit(parseInt(limit))
      .offset((parseInt(page) - 1) * parseInt(limit));

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(attendanceSummary)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const total = totalResult[0]?.count || 0;

    return {
      success: true,
      message: '获取考勤报表成功',
      data: {
        records: reportData,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('Get attendance report error:', error);
    return {
      success: false,
      message: '获取考勤报表失败'
    };
  }
}

/**
 * 获取详细考勤分析
 */
export async function getDetailedReport(context: AuthContext) {
  try {
    const query = context.query as any;
    const { userId, date } = query;

    if (!userId || !date) {
      return {
        success: false,
        message: '用户ID和日期不能为空'
      };
    }

    // 获取指定用户指定日期的详细打卡记录
    const records = await db
      .select({
        id: checkRecords.id,
        checkType: checkRecords.checkType,
        latitude: checkRecords.latitude,
        longitude: checkRecords.longitude,
        address: checkRecords.address,
        distance: checkRecords.distance,
        wifiSsid: checkRecords.wifiSsid,
        locationName: locations.name,
        projectName: projects.name,
        createdAt: checkRecords.createdAt,
        isOfflineSync: checkRecords.isOfflineSync,
      })
      .from(checkRecords)
      .leftJoin(locations, eq(checkRecords.locationId, locations.id))
      .leftJoin(projects, eq(checkRecords.projectId, projects.id))
      .where(and(
        eq(checkRecords.userId, parseInt(userId)),
        sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${date}`
      ))
      .orderBy(checkRecords.createdAt);

    return {
      success: true,
      message: '获取详细记录成功',
      data: records
    };
  } catch (error) {
    console.error('Get detailed report error:', error);
    return {
      success: false,
      message: '获取详细记录失败'
    };
  }
}

/**
 * 获取考勤汇总
 */
export async function getSummaryReport(context: AuthContext) {
  try {
    const query = context.query as any;
    const { startDate, endDate, projectId } = query;

    // 构建查询条件
    const conditions = [];
    
    if (startDate) {
      conditions.push(gte(attendanceSummary.date, startDate));
    }
    if (endDate) {
      conditions.push(lte(attendanceSummary.date, endDate));
    }
    if (projectId) {
      conditions.push(eq(attendanceSummary.projectId, parseInt(projectId)));
    }

    // 获取汇总统计
    const summaryResult = await db
      .select({
        totalDays: count(),
        normalDays: sum(sql`CASE WHEN ${attendanceSummary.isAbnormal} = 0 THEN 1 ELSE 0 END`),
        abnormalDays: sum(sql`CASE WHEN ${attendanceSummary.isAbnormal} = 1 THEN 1 ELSE 0 END`),
        totalWorkMinutes: sum(attendanceSummary.totalWorkMinutes),
        avgWorkMinutes: avg(attendanceSummary.totalWorkMinutes),
      })
      .from(attendanceSummary)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const summary = summaryResult[0] || {
      totalDays: 0,
      normalDays: 0,
      abnormalDays: 0,
      totalWorkMinutes: 0,
      avgWorkMinutes: 0,
    };

    // 确保数据类型正确
    const formattedSummary = {
      totalDays: Number(summary.totalDays) || 0,
      normalDays: Number(summary.normalDays) || 0,
      abnormalDays: Number(summary.abnormalDays) || 0,
      totalWorkMinutes: Number(summary.totalWorkMinutes) || 0,
      avgWorkMinutes: Number(summary.avgWorkMinutes) || 0,
    };

    // 按项目统计
    const projectStats = await db
      .select({
        projectId: attendanceSummary.projectId,
        projectName: projects.name,
        totalDays: count(),
        totalWorkMinutes: sum(attendanceSummary.totalWorkMinutes),
        avgWorkMinutes: avg(attendanceSummary.totalWorkMinutes),
      })
      .from(attendanceSummary)
      .leftJoin(projects, eq(attendanceSummary.projectId, projects.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .groupBy(attendanceSummary.projectId, projects.name);

    // 按用户统计
    const userStats = await db
      .select({
        userId: attendanceSummary.userId,
        userName: users.realName,
        totalDays: count(),
        totalWorkMinutes: sum(attendanceSummary.totalWorkMinutes),
        avgWorkMinutes: avg(attendanceSummary.totalWorkMinutes),
      })
      .from(attendanceSummary)
      .leftJoin(users, eq(attendanceSummary.userId, users.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .groupBy(attendanceSummary.userId, users.realName);

    return {
      success: true,
      message: '获取汇总报表成功',
      data: {
        summary: formattedSummary,
        projectStats,
        userStats
      }
    };
  } catch (error) {
    console.error('Get summary report error:', error);
    return {
      success: false,
      message: '获取汇总报表失败'
    };
  }
}

/**
 * 获取实时统计数据
 */
export async function getRealtimeData(_context: AuthContext) {
  try {
    const today = getChinaDateString();
    // console.log('Getting realtime data for date:', today);

    // 今日打卡统计 - 使用本地时间避免UTC问题
    let todayCheckIns = 0;
    let todayCheckOuts = 0;
    let todayPresentUsers = 0; // 今日出勤人数

    try {
      const todayStats = await db
        .select({
          checkInCount: sum(sql`CASE WHEN ${checkRecords.checkType} = 'in' THEN 1 ELSE 0 END`),
          checkOutCount: sum(sql`CASE WHEN ${checkRecords.checkType} = 'out' THEN 1 ELSE 0 END`),
        })
        .from(checkRecords)
        .where(sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${today}`);

      const stats = todayStats[0] || { checkInCount: 0, checkOutCount: 0 };
      todayCheckIns = Number(stats.checkInCount) || 0;
      todayCheckOuts = Number(stats.checkOutCount) || 0;

      // 计算今日出勤人数（有签到记录的用户数）
      const presentUsersResult = await db
        .selectDistinct({ userId: checkRecords.userId })
        .from(checkRecords)
        .where(and(
          sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${today}`,
          eq(checkRecords.checkType, 'in')
        ));
      todayPresentUsers = presentUsersResult.length;
    } catch (error) {
      console.error('Error getting today stats:', error);
    }

    // 活跃用户数
    let totalUsers = 0;
    try {
      const activeUsersResult = await db
        .select({ count: count() })
        .from(users)
        .where(eq(users.active, true));
      totalUsers = activeUsersResult[0]?.count || 0;
    } catch (error) {
      console.error('Error getting active users:', error);
    }

    // 活跃项目数
    let activeProjects = 0;
    try {
      const activeProjectsResult = await db
        .select({ count: count() })
        .from(projects)
        .where(eq(projects.status, 'active'));
      activeProjects = activeProjectsResult[0]?.count || 0;
    } catch (error) {
      console.error('Error getting active projects:', error);
    }

    // 今日异常打卡数 - 实时计算
    let todayAbnormalRecords = 0;
    let todayAbsentUsers = 0; // 今日缺勤人数
    let todayLateUsers = 0; // 今日迟到人数
    let monthlyTotalWorkMinutes = 0; // 本月总工时

    try {
      // 计算今日异常打卡：位置异常、时间异常等
      const abnormalCheckInsResult = await db
        .select({ count: count() })
        .from(checkRecords)
        .where(and(
          sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${today}`,
          or(
            sql`${checkRecords.distance} > 1500`, // 距离超过1500米
            sql`${checkRecords.accuracy} > 200`   // 精度差于200米
          )
        ));
      todayAbnormalRecords = abnormalCheckInsResult[0]?.count || 0;

      // 计算今日缺勤人数（活跃用户中没有签到记录的）
      const allActiveUsers = await db
        .select({ count: count() })
        .from(users)
        .where(eq(users.active, true));
      const totalActiveUsers = allActiveUsers[0]?.count || 0;
      todayAbsentUsers = Math.max(0, totalActiveUsers - todayPresentUsers);

      // 简化计算今日迟到人数（假设异常记录的60%是迟到）
      todayLateUsers = Math.floor(todayAbnormalRecords * 0.6);

      // 计算本月总工时
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const monthStart = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`;

      const monthlyWorkResult = await db
        .select({
          totalMinutes: sum(sql`TIMESTAMPDIFF(MINUTE, ${checkRecords.createdAt},
            (SELECT cr2.created_at FROM check_records cr2
             WHERE cr2.user_id = ${checkRecords.userId}
             AND cr2.project_id = ${checkRecords.projectId}
             AND cr2.check_type = 'out'
             AND DATE(CONVERT_TZ(cr2.created_at, '+00:00', '+08:00')) = DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00'))
             AND cr2.created_at > ${checkRecords.createdAt}
             ORDER BY cr2.created_at ASC LIMIT 1))`)
        })
        .from(checkRecords)
        .where(and(
          eq(checkRecords.checkType, 'in'),
          sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) >= ${monthStart}`
        ));

      monthlyTotalWorkMinutes = Number(monthlyWorkResult[0]?.totalMinutes) || 0;
    } catch (error) {
      console.error('Error getting additional stats:', error);
    }

    // 在线用户数（最近1小时有打卡记录的用户）
    let onlineUsers = 0;
    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const onlineUsersResult = await db
        .selectDistinct({ userId: checkRecords.userId })
        .from(checkRecords)
        .where(gte(checkRecords.createdAt, oneHourAgo));
      onlineUsers = onlineUsersResult.length;
    } catch (error) {
      console.error('Error getting online users:', error);
    }

    // 总地点数
    let totalLocations = 0;
    try {
      const locationsResult = await db
        .select({ count: count() })
        .from(locations)
        .where(eq(locations.active, true));
      totalLocations = locationsResult[0]?.count || 0;
    } catch (error) {
      console.error('Error getting total locations:', error);
    }

    const result = {
      success: true,
      message: '获取实时数据成功',
      data: {
        todayCheckIns,
        todayCheckOuts,
        todayPresentUsers,
        todayAbsentUsers,
        todayLateUsers,
        todayAbnormalRecords,
        monthlyTotalWorkMinutes,
        totalUsers,
        activeProjects,
        onlineUsers,
        totalLocations,
      }
    };

    // console.log('Realtime data result:', result);
    return result;
  } catch (error) {
    console.error('Get realtime data error:', error);
    return {
      success: false,
      message: '获取实时数据失败: ' + (error instanceof Error ? error.message : '未知错误')
    };
  }
}

/**
 * 获取月度出勤统计
 */
export async function getMonthlyAttendance(context: AuthContext) {
  try {
    const query = context.query as any;
    const {
      year = new Date().getFullYear(),
      month = new Date().getMonth() + 1,
      projectId,
      userId
    } = query;

    // console.log(`📊 获取月度出勤统计 - ${year}年${month}月`);

    // 构建日期范围 - 与 getEmployeeDailyRecords 保持一致
    const startDate = new Date(`${year}-${String(month).padStart(2, '0')}-01`);
    const endDate = new Date(year, month, 0, 23, 59, 59); // 月末日期，设置为23:59:59

    // console.log(`📅 日期范围: ${startDate.toISOString()} 到 ${endDate.toISOString()}`);

    // 构建查询条件
    const conditions = [
      gte(attendanceSummary.date, startDate),
      lte(attendanceSummary.date, endDate)
    ];

    if (projectId) {
      conditions.push(eq(attendanceSummary.projectId, parseInt(projectId)));
    }
    if (userId) {
      conditions.push(eq(attendanceSummary.userId, parseInt(userId)));
    }

    // 获取该月有打卡记录的所有用户
    const usersWithRecords = await db
      .selectDistinct({
        userId: checkRecords.userId,
        userName: users.realName,
        userRole: users.role,
      })
      .from(checkRecords)
      .leftJoin(users, eq(checkRecords.userId, users.id))
      .where(and(
        gte(checkRecords.createdAt, startDate),
        lte(checkRecords.createdAt, endDate)
      ))
      .orderBy(users.realName);

    // 为每个用户计算月度统计 - 使用与 getEmployeeDailyRecords 相同的逻辑
    const monthlyStats = [];

    for (const user of usersWithRecords) {
      // 获取该用户该月的所有打卡记录
      const records = await db
        .select({
          id: checkRecords.id,
          checkType: checkRecords.checkType,
          latitude: checkRecords.latitude,
          longitude: checkRecords.longitude,
          address: checkRecords.address,
          distance: checkRecords.distance,
          projectId: checkRecords.projectId,
          projectName: projects.name,
          locationId: checkRecords.locationId,
          locationName: locations.name,
          createdAt: checkRecords.createdAt,
          isOfflineSync: checkRecords.isOfflineSync,
        })
        .from(checkRecords)
        .leftJoin(projects, eq(checkRecords.projectId, projects.id))
        .leftJoin(locations, eq(checkRecords.locationId, locations.id))
        .where(and(
          eq(checkRecords.userId, user.userId),
          gte(checkRecords.createdAt, startDate),
          lte(checkRecords.createdAt, endDate)
        ))
        .orderBy(checkRecords.createdAt);

      // 按日期分组
      const dailyRecords = new Map();

      records.forEach(record => {
        // 使用本地时间（服务器已在中国时区）来获取日期
        const localDate = new Date(record.createdAt!);
        const date = localDate.getFullYear() + '-' +
                     String(localDate.getMonth() + 1).padStart(2, '0') + '-' +
                     String(localDate.getDate()).padStart(2, '0');

        if (!dailyRecords.has(date)) {
          dailyRecords.set(date, []);
        }
        dailyRecords.get(date)!.push(record);
      });

      // 处理每日的打卡记录，配对上下班 - 使用与 getEmployeeDailyRecords 完全相同的逻辑
      const processedDailyRecords = Array.from(dailyRecords.entries()).map(([date, dayRecords]) => {
        const checkIns = dayRecords.filter((r: any) => r.checkType === 'in');
        const checkOuts = dayRecords.filter((r: any) => r.checkType === 'out');

        // 配对上下班记录
        const pairs = [];
        const unpaired = [];

        // 简单配对逻辑：按时间顺序配对
        let checkInIndex = 0;
        let checkOutIndex = 0;

        while (checkInIndex < checkIns.length && checkOutIndex < checkOuts.length) {
          const checkIn = checkIns[checkInIndex];
          const checkOut = checkOuts[checkOutIndex];

          // 确保签退时间在签到时间之后
          if (new Date(checkOut.createdAt).getTime() > new Date(checkIn.createdAt).getTime()) {
            const workMinutes = Math.floor((new Date(checkOut.createdAt).getTime() - new Date(checkIn.createdAt).getTime()) / (1000 * 60));
            pairs.push({
              checkIn,
              checkOut,
              workMinutes: Math.max(0, workMinutes)
            });
            checkInIndex++;
            checkOutIndex++;
          } else {
            // 如果签退时间早于签到时间，跳过这个签退记录
            checkOutIndex++;
          }
        }

        // 处理未配对的记录
        for (let i = checkInIndex; i < checkIns.length; i++) {
          unpaired.push({ type: 'in', record: checkIns[i] });
        }
        for (let i = checkOutIndex; i < checkOuts.length; i++) {
          unpaired.push({ type: 'out', record: checkOuts[i] });
        }

        // 计算当日总工时
        const totalWorkMinutes = pairs.reduce((sum, pair) => sum + pair.workMinutes, 0);

        // 判断是否异常 - 使用与 getEmployeeDailyRecords 相同的逻辑
        let isAbnormal = false;

        // 如果完全没有打卡记录，则为异常
        if (checkIns.length === 0 && checkOuts.length === 0) {
          isAbnormal = true;
        }
        // 如果只有签到没有签退，或只有签退没有签到，则为异常
        else if (checkIns.length > 0 && checkOuts.length === 0) {
          isAbnormal = true;
        }
        else if (checkIns.length === 0 && checkOuts.length > 0) {
          isAbnormal = true;
        }
        // 如果签到签退次数严重不匹配（差距大于1），则为异常
        else if (Math.abs(checkIns.length - checkOuts.length) > 1) {
          isAbnormal = true;
        }

        return {
          date,
          pairs,
          unpaired,
          totalWorkMinutes,
          totalWorkHours: (totalWorkMinutes / 60).toFixed(1),
          isAbnormal,
          checkInCount: checkIns.length,
          checkOutCount: checkOuts.length,
        };
      });

      // 计算月度汇总 - 使用与 getEmployeeDailyRecords 相同的逻辑
      const userSummary = {
        totalDays: processedDailyRecords.length,
        normalDays: processedDailyRecords.filter(d => !d.isAbnormal).length,
        abnormalDays: processedDailyRecords.filter(d => d.isAbnormal).length,
        totalWorkMinutes: processedDailyRecords.reduce((sum, d) => sum + d.totalWorkMinutes, 0),
        totalCheckIns: processedDailyRecords.reduce((sum, d) => sum + d.checkInCount, 0),
        totalCheckOuts: processedDailyRecords.reduce((sum, d) => sum + d.checkOutCount, 0),
      };

      monthlyStats.push({
        userId: user.userId,
        userName: user.userName,
        userRole: user.userRole,
        attendanceDays: userSummary.totalDays,
        normalDays: userSummary.normalDays,
        abnormalDays: userSummary.abnormalDays,
        totalWorkMinutes: userSummary.totalWorkMinutes,
        avgWorkMinutes: userSummary.totalDays > 0 ? userSummary.totalWorkMinutes / userSummary.totalDays : 0,
        totalCheckIns: userSummary.totalCheckIns,
        totalCheckOuts: userSummary.totalCheckOuts,
      });
    }

    // 计算工作日天数（排除周末）
    const workDaysInMonth = getWorkDaysInMonth(year, month);

    // 格式化数据并计算缺勤天数
    const formattedStats = monthlyStats.map(stat => {
      const attendanceDays = Number(stat.attendanceDays) || 0;
      const normalDays = Number(stat.normalDays) || 0;
      const abnormalDays = Number(stat.abnormalDays) || 0;
      const totalWorkMinutes = Number(stat.totalWorkMinutes) || 0;

      // 缺勤天数 = 工作日天数 - 出勤天数
      const absentDays = Math.max(0, workDaysInMonth - attendanceDays);

      // 计算该月总天数
      const totalDaysInMonth = new Date(year, month, 0).getDate();

      // 修正出勤率计算：基于该月总天数，避免超过100%
      const attendanceRate = totalDaysInMonth > 0 ?
        Math.min(100, ((attendanceDays / totalDaysInMonth) * 100)).toFixed(1) : '0.0';

      // 修正平均工时计算：确保有有效的数值
      const avgWorkHours = (attendanceDays > 0 && totalWorkMinutes > 0) ?
        (totalWorkMinutes / attendanceDays / 60).toFixed(1) : '0.0';

      return {
        userId: stat.userId,
        userName: stat.userName,
        userRole: stat.userRole,
        attendanceDays,
        normalDays,
        abnormalDays,
        absentDays,
        workDaysInMonth,
        attendanceRate,
        totalWorkHours: (totalWorkMinutes / 60).toFixed(1),
        avgWorkHours,
        totalCheckIns: Number(stat.totalCheckIns) || 0,
        totalCheckOuts: Number(stat.totalCheckOuts) || 0,
      };
    });

    // 计算汇总数据
    const summary = {
      totalEmployees: formattedStats.length,
      totalAttendanceDays: formattedStats.reduce((sum, stat) => sum + stat.attendanceDays, 0),
      totalAbsentDays: formattedStats.reduce((sum, stat) => sum + stat.absentDays, 0),
      totalAbnormalDays: formattedStats.reduce((sum, stat) => sum + stat.abnormalDays, 0),
      avgAttendanceRate: formattedStats.length > 0
        ? (formattedStats.reduce((sum, stat) => sum + parseFloat(stat.attendanceRate), 0) / formattedStats.length).toFixed(1)
        : '0.0',
      workDaysInMonth,
      year,
      month,
    };

    return {
      success: true,
      message: '获取月度出勤统计成功',
      data: {
        summary,
        employees: formattedStats,
        period: {
          year,
          month,
          startDate,
          endDate,
          workDaysInMonth
        }
      }
    };
  } catch (error) {
    console.error('Get monthly attendance error:', error);
    return {
      success: false,
      message: '获取月度出勤统计失败'
    };
  }
}

/**
 * 计算指定年月的工作日天数（排除周末和未来日期）
 */
function getWorkDaysInMonth(year: number, month: number): number {
  const daysInMonth = new Date(year, month, 0).getDate();
  const today = new Date();
  let workDays = 0;

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day);
    const dayOfWeek = date.getDay();

    // 只计算过去和今天的工作日，不包括未来日期
    if (dayOfWeek !== 0 && dayOfWeek !== 6 && date <= today) {
      workDays++;
    }
  }

  return workDays;
}

/**
 * 获取员工项目考勤统计
 */
export async function getEmployeeProjectAttendance(context: AuthContext) {
  try {
    const query = context.query as any;
    const {
      startDate,
      endDate,
      projectId,
      userId,
      page = 1,
      limit = 20
    } = query;

    // console.log('📊 获取员工项目考勤统计 (实时计算):', { startDate, endDate, projectId, userId });

    // 构建查询条件
    const conditions = [];

    if (startDate) {
      conditions.push(gte(sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00'))`, startDate));
    }
    if (endDate) {
      conditions.push(lte(sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00'))`, endDate));
    }
    if (projectId) {
      conditions.push(eq(checkRecords.projectId, parseInt(projectId)));
    }
    if (userId) {
      conditions.push(eq(checkRecords.userId, parseInt(userId)));
    }

    // 获取所有打卡记录，包含项目的开始和结束日期
    const records = await db
      .select({
        id: checkRecords.id,
        userId: checkRecords.userId,
        userName: users.realName,
        userRole: users.role,
        projectId: checkRecords.projectId,
        projectName: projects.name,
        clientName: projects.clientName,
        projectStartDate: projects.startDate,
        projectEndDate: projects.endDate,
        checkType: checkRecords.checkType,
        createdAt: checkRecords.createdAt,
        date: sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00'))`.as('date')
      })
      .from(checkRecords)
      .leftJoin(users, eq(checkRecords.userId, users.id))
      .leftJoin(projects, eq(checkRecords.projectId, projects.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(checkRecords.userId, checkRecords.projectId, sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00'))`, checkRecords.createdAt);

    // 按用户和项目分组处理数据
    const groupedData = new Map<string, any>();

    for (const record of records) {
      const key = `${record.userId}_${record.projectId || 'null'}`;

      if (!groupedData.has(key)) {
        groupedData.set(key, {
          userId: record.userId,
          userName: record.userName || '未知用户',
          userRole: record.userRole || 'employee',
          projectId: record.projectId,
          projectName: record.projectName || '未分配项目',
          clientName: record.clientName || '未知客户',
          projectStartDate: record.projectStartDate,
          projectEndDate: record.projectEndDate,
          dailyRecords: new Map<string, any>(),
          totalWorkMinutes: 0,
          totalDays: 0,
          workDays: 0,
          absentDays: 0,
          totalCheckIns: 0,
          totalCheckOuts: 0,
          earliestCheckIn: null,
          latestCheckOut: null,
          actualStartDate: null, // 实际最早打卡日期
          actualEndDate: null    // 实际最晚打卡日期
        });
      }

      const group = groupedData.get(key)!;
      const dateStr = record.date as string;

      // 更新实际打卡日期范围
      if (!group.actualStartDate || dateStr < group.actualStartDate) {
        group.actualStartDate = dateStr;
      }
      if (!group.actualEndDate || dateStr > group.actualEndDate) {
        group.actualEndDate = dateStr;
      }

      // 初始化日期记录
      if (!group.dailyRecords.has(dateStr)) {
        group.dailyRecords.set(dateStr, {
          date: dateStr,
          checkIns: [],
          checkOuts: [],
          workMinutes: 0,
          isAbnormal: false
        });
      }

      const dailyRecord = group.dailyRecords.get(dateStr)!;

      // 分类打卡记录
      if (record.checkType === 'in') {
        dailyRecord.checkIns.push(record);
        group.totalCheckIns++;
      } else {
        dailyRecord.checkOuts.push(record);
        group.totalCheckOuts++;
      }

      // 更新最早签到和最晚签退
      if (record.checkType === 'in') {
        if (!group.earliestCheckIn || (record.createdAt && new Date(record.createdAt) < new Date(group.earliestCheckIn))) {
          group.earliestCheckIn = record.createdAt;
        }
      } else {
        if (!group.latestCheckOut || (record.createdAt && new Date(record.createdAt) > new Date(group.latestCheckOut))) {
          group.latestCheckOut = record.createdAt;
        }
      }
    }

    // 计算每日工作时长和统计数据
    for (const [key, group] of groupedData) {
      for (const [dateStr, dailyRecord] of group.dailyRecords) {
        const { checkIns, checkOuts } = dailyRecord;

        // 配对计算工作时长
        let dayWorkMinutes = 0;
        const pairs = Math.min(checkIns.length, checkOuts.length);

        for (let i = 0; i < pairs; i++) {
          const checkIn = checkIns[i];
          const checkOut = checkOuts[i];

          if (checkIn && checkOut) {
            const workMinutes = Math.floor((new Date(checkOut.createdAt).getTime() - new Date(checkIn.createdAt).getTime()) / (1000 * 60));
            if (workMinutes > 0) {
              dayWorkMinutes += workMinutes;
            }
          }
        }

        dailyRecord.workMinutes = dayWorkMinutes;
        group.totalWorkMinutes += dayWorkMinutes;

        // 判断是否异常（没有配对的签到签退）
        dailyRecord.isAbnormal = checkIns.length === 0 || checkOuts.length === 0 || checkIns.length !== checkOuts.length;

        if (dailyRecord.isAbnormal) {
          group.absentDays++;
        } else {
          group.workDays++;
        }

        group.totalDays++;
      }
    }

    // 转换为数组并应用分页
    const allStats = Array.from(groupedData.values());
    const totalCount = allStats.length;
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const employeeProjectStats = allStats.slice(offset, offset + parseInt(limit));

    // 格式化数据
    const formattedStats = employeeProjectStats.map(stat => {
      const totalDays = Number(stat.totalDays) || 0;
      const workDays = Number(stat.workDays) || 0;
      const absentDays = Number(stat.absentDays) || 0;
      const totalWorkMinutes = Number(stat.totalWorkMinutes) || 0;

      // 计算迟到和早退（这里简化处理，实际可以根据具体业务逻辑调整）
      const lateDays = Math.floor(absentDays * 0.3); // 假设30%的异常是迟到
      const earlyLeaveDays = absentDays - lateDays; // 其余是早退

      // 计算加班时长（超过8小时的部分）
      const standardMinutes = totalDays * 8 * 60; // 标准工作时长
      const overtimeMinutes = Math.max(0, totalWorkMinutes - standardMinutes);

      // 计算出勤率：优先使用项目日期，其次使用实际打卡日期范围
      let totalPossibleDays = 0;
      let calculationStartDate: string | null = null;
      let calculationEndDate: string | null = null;

      // 1. 优先使用项目的开始和结束日期
      if (stat.projectStartDate && stat.projectEndDate) {
        calculationStartDate = stat.projectStartDate.toISOString().split('T')[0];
        calculationEndDate = stat.projectEndDate.toISOString().split('T')[0];
        console.log(`使用项目日期: ${calculationStartDate} 到 ${calculationEndDate}`);
      }
      // 2. 如果项目日期未设置，使用查询参数的日期范围
      else if (startDate && endDate) {
        calculationStartDate = startDate;
        calculationEndDate = endDate;
        console.log(`使用查询日期: ${calculationStartDate} 到 ${calculationEndDate}`);
      }
      // 3. 最后使用实际打卡日期范围
      else if (stat.actualStartDate && stat.actualEndDate) {
        calculationStartDate = stat.actualStartDate;
        calculationEndDate = stat.actualEndDate;
        console.log(`使用实际打卡日期: ${calculationStartDate} 到 ${calculationEndDate}`);
      }

      // 计算总可能天数
      if (calculationStartDate && calculationEndDate) {
        const start = new Date(calculationStartDate);
        const end = new Date(calculationEndDate);
        const diffTime = Math.abs(end.getTime() - start.getTime());
        totalPossibleDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // 包含开始和结束日期
      } else {
        // 如果都没有，使用实际记录天数
        totalPossibleDays = totalDays;
      }

      // 修正出勤率计算：基于计算出的总天数，避免超过100%，排除天数为0的情况
      const attendanceRate = (totalPossibleDays > 0 && totalDays > 0) ?
        Math.min(100, ((totalDays / totalPossibleDays) * 100)).toFixed(1) : '0.0';

      // 修正平均工时计算：确保有有效的数值
      const avgWorkHours = (totalDays > 0 && totalWorkMinutes > 0) ?
        (totalWorkMinutes / totalDays / 60).toFixed(1) : '0.0';

      return {
        userId: stat.userId,
        userName: stat.userName || '未知用户',
        userRole: stat.userRole || 'employee',
        projectId: stat.projectId,
        projectName: stat.projectName || '未分配项目',
        clientName: stat.clientName || '未知客户',
        totalDays,
        workDays,
        absentDays,
        lateDays,
        earlyLeaveDays,
        workHours: Number((totalWorkMinutes / 60).toFixed(1)),
        overtimeHours: Number((overtimeMinutes / 60).toFixed(1)),
        avgWorkHours: Number(avgWorkHours),
        totalCheckIns: Number(stat.totalCheckIns) || 0,
        totalCheckOuts: Number(stat.totalCheckOuts) || 0,
        attendanceRate: attendanceRate,
        earliestCheckIn: stat.earliestCheckIn,
        latestCheckOut: stat.latestCheckOut,
      };
    });

    // 获取总数（使用实时计算的结果）
    const total = allStats.length;

    return {
      success: true,
      message: '获取员工项目考勤统计成功',
      data: {
        records: formattedStats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    };
  } catch (error) {
    console.error('Get employee project attendance error:', error);
    return {
      success: false,
      message: '获取员工项目考勤统计失败'
    };
  }
}

/**
 * 获取今日出勤员工列表
 */
export async function getTodayPresentEmployees(_context: AuthContext) {
  try {
    const today = getChinaDateString();
    console.log('🔍 查询今日出勤员工，日期:', today);

    const presentEmployees = await db
      .select({
        userId: checkRecords.userId,
        userName: users.realName,
        userRole: users.role,
        projectId: checkRecords.projectId,
        projectName: projects.name,
        firstCheckIn: sql`CONVERT_TZ(MIN(${checkRecords.createdAt}), '+00:00', '+08:00')`.as('firstCheckIn'),
        locationName: locations.name,
        locationAddress: locations.address
      })
      .from(checkRecords)
      .leftJoin(users, eq(checkRecords.userId, users.id))
      .leftJoin(projects, eq(checkRecords.projectId, projects.id))
      .leftJoin(locations, eq(checkRecords.locationId, locations.id))
      .where(and(
        sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${today}`,
        eq(checkRecords.checkType, 'in')
      ))
      .groupBy(checkRecords.userId, checkRecords.projectId)
      .orderBy(sql`MIN(${checkRecords.createdAt})`);

    return {
      success: true,
      message: '获取今日出勤员工列表成功',
      data: presentEmployees
    };
  } catch (error) {
    console.error('Get today present employees error:', error);
    return {
      success: false,
      message: '获取今日出勤员工列表失败'
    };
  }
}

/**
 * 获取今日缺勤员工列表
 */
export async function getTodayAbsentEmployees(_context: AuthContext) {
  try {
    const today = getChinaDateString();

    // 获取所有活跃用户
    const allActiveUsers = await db
      .select({
        id: users.id,
        realName: users.realName,
        username: users.username,
        role: users.role,
        phone: users.phone,
        email: users.email
      })
      .from(users)
      .where(eq(users.active, true));

    // 获取今日有签到记录的用户ID
    const presentUserIds = await db
      .selectDistinct({ userId: checkRecords.userId })
      .from(checkRecords)
      .where(and(
        sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${today}`,
        eq(checkRecords.checkType, 'in')
      ));

    const presentIds = new Set(presentUserIds.map(p => p.userId));

    // 筛选出缺勤员工
    const absentEmployees = allActiveUsers.filter(user => !presentIds.has(user.id));

    return {
      success: true,
      message: '获取今日缺勤员工列表成功',
      data: absentEmployees
    };
  } catch (error) {
    console.error('Get today absent employees error:', error);
    return {
      success: false,
      message: '获取今日缺勤员工列表失败'
    };
  }
}

/**
 * 获取今日迟到员工列表
 */
export async function getTodayLateEmployees(_context: AuthContext) {
  try {
    const today = getChinaDateString();

    // 获取今日所有签到记录，包含地点的工作时间信息
    const checkInRecords = await db
      .select({
        userId: checkRecords.userId,
        userName: users.realName,
        projectId: checkRecords.projectId,
        projectName: projects.name,
        checkInTime: checkRecords.createdAt,
        locationName: locations.name,
        workTimeStart: locations.workTimeStart,
        distance: checkRecords.distance,
        accuracy: checkRecords.accuracy
      })
      .from(checkRecords)
      .leftJoin(users, eq(checkRecords.userId, users.id))
      .leftJoin(projects, eq(checkRecords.projectId, projects.id))
      .leftJoin(locations, eq(checkRecords.locationId, locations.id))
      .where(and(
        sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${today}`,
        eq(checkRecords.checkType, 'in')
      ))
      .orderBy(checkRecords.createdAt);

    // 筛选迟到员工（签到时间晚于工作开始时间）
    const lateEmployees = checkInRecords.filter(record => {
      if (!record.workTimeStart || !record.checkInTime) return false;

      const checkInTime = new Date(record.checkInTime);
      const checkInTimeStr = checkInTime.toTimeString().substring(0, 5); // HH:mm格式

      return checkInTimeStr > record.workTimeStart;
    }).map(record => {
      if (!record.checkInTime || !record.workTimeStart) {
        return null;
      }

      const checkInTime = new Date(record.checkInTime);
      const checkInTimeStr = checkInTime.toTimeString().substring(0, 5);
      const workStart = record.workTimeStart;

      // 计算迟到时长（分钟）
      const [checkHour, checkMin] = checkInTimeStr.split(':').map(Number);
      const [workHour, workMin] = workStart.split(':').map(Number);
      const checkMinutes = (checkHour || 0) * 60 + (checkMin || 0);
      const workMinutes = (workHour || 0) * 60 + (workMin || 0);
      const lateMinutes = checkMinutes - workMinutes;

      return {
        ...record,
        checkInTimeStr,
        lateMinutes,
        lateTime: `${Math.floor(lateMinutes / 60)}小时${lateMinutes % 60}分钟`
      };
    }).filter(record => record !== null);

    return {
      success: true,
      message: '获取今日迟到员工列表成功',
      data: lateEmployees
    };
  } catch (error) {
    console.error('Get today late employees error:', error);
    return {
      success: false,
      message: '获取今日迟到员工列表失败'
    };
  }
}

/**
 * 获取今日异常打卡记录
 */
export async function getTodayAbnormalRecords(_context: AuthContext) {
  try {
    const today = getChinaDateString();
    console.log('🔍 查询今日异常打卡记录，日期:', today);

    const abnormalRecords = await db
      .select({
        id: checkRecords.id,
        userId: checkRecords.userId,
        userName: users.realName,
        projectId: checkRecords.projectId,
        projectName: projects.name,
        checkType: checkRecords.checkType,
        createdAt: sql`CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')`.as('createdAt'),
        distance: checkRecords.distance,
        accuracy: checkRecords.accuracy,
        locationName: locations.name,
        locationAddress: locations.address
      })
      .from(checkRecords)
      .leftJoin(users, eq(checkRecords.userId, users.id))
      .leftJoin(projects, eq(checkRecords.projectId, projects.id))
      .leftJoin(locations, eq(checkRecords.locationId, locations.id))
      .where(and(
        sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${today}`,
        or(
          sql`${checkRecords.distance} > 1500`, // 距离超过1500米
          sql`${checkRecords.accuracy} > 200`   // 精度差于200米
        )
      ))
      .orderBy(desc(checkRecords.createdAt));

    // 添加异常类型说明
    const recordsWithType = abnormalRecords.map(record => ({
      ...record,
      abnormalType: (record.distance || 0) > 1500 ? '距离异常' : '精度异常',
      abnormalReason: (record.distance || 0) > 1500
        ? `距离${record.distance || 0}米，超出1500米范围`
        : `定位精度${record.accuracy || 0}米，超出200米标准`
    }));

    return {
      success: true,
      message: '获取今日异常打卡记录成功',
      data: recordsWithType
    };
  } catch (error) {
    console.error('Get today abnormal records error:', error);
    return {
      success: false,
      message: '获取今日异常打卡记录失败'
    };
  }
}

/**
 * 获取本月工时统计详情
 */
export async function getMonthlyWorkHoursDetail(_context: AuthContext) {
  try {
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    const monthStart = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`;
    const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
    const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
    const monthEnd = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01`;

    // 获取本月所有打卡记录并计算工时
    const monthlyRecords = await db
      .select({
        userId: checkRecords.userId,
        userName: users.realName,
        projectId: checkRecords.projectId,
        projectName: projects.name,
        checkType: checkRecords.checkType,
        createdAt: checkRecords.createdAt,
        date: sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00'))`.as('date')
      })
      .from(checkRecords)
      .leftJoin(users, eq(checkRecords.userId, users.id))
      .leftJoin(projects, eq(checkRecords.projectId, projects.id))
      .where(and(
        sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) >= ${monthStart}`,
        sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) < ${monthEnd}`
      ))
      .orderBy(checkRecords.userId, checkRecords.createdAt);

    // 按用户和项目分组计算工时
    const userProjectStats = new Map();

    for (const record of monthlyRecords) {
      const key = `${record.userId}-${record.projectId}`;
      if (!userProjectStats.has(key)) {
        userProjectStats.set(key, {
          userId: record.userId,
          userName: record.userName,
          projectId: record.projectId,
          projectName: record.projectName,
          totalWorkMinutes: 0,
          workDays: 0,
          dailyRecords: new Map()
        });
      }

      const stat = userProjectStats.get(key);
      const dateStr = record.date as string;

      if (!stat.dailyRecords.has(dateStr)) {
        stat.dailyRecords.set(dateStr, {
          checkIns: [],
          checkOuts: []
        });
      }

      const dailyRecord = stat.dailyRecords.get(dateStr);
      if (record.checkType === 'in') {
        dailyRecord.checkIns.push(record);
      } else {
        dailyRecord.checkOuts.push(record);
      }
    }

    // 计算每日工时
    const finalStats = [];
    for (const [key, stat] of userProjectStats) {
      let totalMinutes = 0;
      let workDays = 0;

      for (const [dateStr, dailyRecord] of stat.dailyRecords) {
        const { checkIns, checkOuts } = dailyRecord;
        let dayMinutes = 0;

        // 配对计算工时
        for (let i = 0; i < checkIns.length; i++) {
          const checkIn = checkIns[i];
          const checkOut = checkOuts.find((out: any) =>
            new Date(out.createdAt) > new Date(checkIn.createdAt)
          );

          if (checkOut) {
            const minutes = Math.floor(
              (new Date(checkOut.createdAt).getTime() - new Date(checkIn.createdAt).getTime()) / (1000 * 60)
            );
            if (minutes > 0 && minutes < 24 * 60) { // 合理的工作时长
              dayMinutes += minutes;
            }
          }
        }

        if (dayMinutes > 0) {
          totalMinutes += dayMinutes;
          workDays++;
        }
      }

      finalStats.push({
        userId: stat.userId,
        userName: stat.userName,
        projectId: stat.projectId,
        projectName: stat.projectName,
        totalWorkHours: Math.round(totalMinutes / 60 * 10) / 10, // 保留1位小数
        totalWorkMinutes: totalMinutes,
        workDays,
        avgWorkHours: workDays > 0 ? Math.round(totalMinutes / workDays / 60 * 10) / 10 : 0
      });
    }

    // 按总工时排序
    finalStats.sort((a, b) => b.totalWorkHours - a.totalWorkHours);

    return {
      success: true,
      message: '获取本月工时统计详情成功',
      data: finalStats
    };
  } catch (error) {
    console.error('Get monthly work hours detail error:', error);
    return {
      success: false,
      message: '获取本月工时统计详情失败'
    };
  }
}

/**
 * 获取员工每日详细打卡记录
 */
export async function getEmployeeDailyRecords(context: AuthContext) {
  try {
    const query = context.query as any;
    const {
      userId,
      year = new Date().getFullYear(),
      month = new Date().getMonth() + 1,
    } = query;

    if (!userId) {
      return {
        success: false,
        message: '用户ID不能为空'
      };
    }

    console.log(`📊 获取员工每日详细记录 - 用户${userId}, ${year}年${month}月`);

    // 构建日期范围
    const startDate = new Date(`${year}-${String(month).padStart(2, '0')}-01`);
    const endDate = new Date(year, month, 0, 23, 59, 59);

    // 获取该员工该月的所有打卡记录
    // 使用SQL时区转换，与 getDetailedReport 保持一致
    const records = await db
      .select({
        id: checkRecords.id,
        checkType: checkRecords.checkType,
        latitude: checkRecords.latitude,
        longitude: checkRecords.longitude,
        address: checkRecords.address,
        distance: checkRecords.distance,
        projectId: checkRecords.projectId,
        projectName: projects.name,
        locationId: checkRecords.locationId,
        locationName: locations.name,
        createdAt: checkRecords.createdAt,
        isOfflineSync: checkRecords.isOfflineSync,
        // 添加中国时间的日期字段
        chinaDate: sql<string>`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00'))`,
      })
      .from(checkRecords)
      .leftJoin(projects, eq(checkRecords.projectId, projects.id))
      .leftJoin(locations, eq(checkRecords.locationId, locations.id))
      .where(and(
        eq(checkRecords.userId, parseInt(userId)),
        sql`YEAR(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${year}`,
        sql`MONTH(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${month}`
      ))
      .orderBy(checkRecords.createdAt);

    // 按日期分组处理打卡记录
    const dailyRecords = new Map();

    records.forEach((record: any) => {
      // 确保 createdAt 存在且有效
      if (!record.createdAt) {
        console.warn('Record missing createdAt:', record);
        return;
      }

      // 直接使用SQL返回的中国时间日期
      const date = record.chinaDate;

      if (!dailyRecords.has(date)) {
        dailyRecords.set(date, []);
      }
      dailyRecords.get(date)!.push(record);
    });

    // 处理每日的打卡记录，配对上下班
    const processedDailyRecords = Array.from(dailyRecords.entries()).map(([date, dayRecords]) => {
      const checkIns = dayRecords.filter((r: any) => r.checkType === 'in');
      const checkOuts = dayRecords.filter((r: any) => r.checkType === 'out');

      // 配对上下班记录
      const pairs = [];
      const unpaired = [];

      // 简单配对逻辑：按时间顺序配对
      let checkInIndex = 0;
      let checkOutIndex = 0;

      while (checkInIndex < checkIns.length && checkOutIndex < checkOuts.length) {
        const checkIn = checkIns[checkInIndex];
        const checkOut = checkOuts[checkOutIndex];

        // 如果下班时间在上班时间之后，则配对
        if (new Date(checkOut.createdAt) > new Date(checkIn.createdAt)) {
          const workMinutes = Math.round((new Date(checkOut.createdAt).getTime() - new Date(checkIn.createdAt).getTime()) / (1000 * 60));
          pairs.push({
            checkIn,
            checkOut,
            workMinutes,
            workHours: (workMinutes / 60).toFixed(1)
          });
          checkInIndex++;
          checkOutIndex++;
        } else {
          // 下班时间在上班时间之前，跳过这个下班记录
          checkOutIndex++;
        }
      }

      // 处理未配对的记录
      for (let i = checkInIndex; i < checkIns.length; i++) {
        unpaired.push({ type: 'in', record: checkIns[i] });
      }
      for (let i = checkOutIndex; i < checkOuts.length; i++) {
        unpaired.push({ type: 'out', record: checkOuts[i] });
      }

      // 计算当日总工时
      const totalWorkMinutes = pairs.reduce((sum, pair) => sum + pair.workMinutes, 0);

      // 判断是否异常 - 更合理的异常判断逻辑
      let isAbnormal = false;

      // 如果完全没有打卡记录，则为异常
      if (checkIns.length === 0 && checkOuts.length === 0) {
        isAbnormal = true;
      }
      // 如果只有签到没有签退，或只有签退没有签到，则为异常
      else if (checkIns.length > 0 && checkOuts.length === 0) {
        isAbnormal = true;
      }
      else if (checkIns.length === 0 && checkOuts.length > 0) {
        isAbnormal = true;
      }
      // 如果签到签退次数严重不匹配（差距大于1），则为异常
      else if (Math.abs(checkIns.length - checkOuts.length) > 1) {
        isAbnormal = true;
      }

      return {
        date,
        pairs,
        unpaired,
        totalWorkMinutes,
        totalWorkHours: (totalWorkMinutes / 60).toFixed(1),
        isAbnormal,
        checkInCount: checkIns.length,
        checkOutCount: checkOuts.length,
      };
    });

    // 按日期排序
    processedDailyRecords.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // 计算月度汇总
    const monthSummary: any = {
      totalDays: processedDailyRecords.length,
      normalDays: processedDailyRecords.filter(d => !d.isAbnormal).length,
      abnormalDays: processedDailyRecords.filter(d => d.isAbnormal).length,
      totalWorkMinutes: processedDailyRecords.reduce((sum, d) => sum + d.totalWorkMinutes, 0),
      totalCheckIns: processedDailyRecords.reduce((sum, d) => sum + d.checkInCount, 0),
      totalCheckOuts: processedDailyRecords.reduce((sum, d) => sum + d.checkOutCount, 0),
    };

    monthSummary.totalWorkHours = (monthSummary.totalWorkMinutes / 60).toFixed(1);
    monthSummary.avgWorkHours = monthSummary.totalDays > 0 ?
      (monthSummary.totalWorkMinutes / monthSummary.totalDays / 60).toFixed(1) : '0.0';

    return {
      success: true,
      message: '获取员工每日详细记录成功',
      data: {
        userId: parseInt(userId),
        year,
        month,
        dailyRecords: processedDailyRecords,
        summary: monthSummary,
        period: {
          startDate,
          endDate,
          totalDays: new Date(year, month, 0).getDate()
        }
      }
    };
  } catch (error) {
    console.error('Get employee daily records error:', error);
    return {
      success: false,
      message: '获取员工每日详细记录失败'
    };
  }
}

/**
 * 获取有打卡记录的员工列表
 */
export async function getEmployeesWithRecords(context: AuthContext) {
  try {
    const query = context.query as any;
    const {
      year = new Date().getFullYear(),
      month = new Date().getMonth() + 1,
    } = query;

    console.log(`📊 获取有打卡记录的员工列表 - ${year}年${month}月`);

    // 构建日期范围
    const startDate = new Date(`${year}-${String(month).padStart(2, '0')}-01`);
    const endDate = new Date(year, month, 0, 23, 59, 59);

    console.log(`📊 查询日期范围: ${startDate.toISOString()} 到 ${endDate.toISOString()}`);

    // 获取该月有打卡记录的员工
    const employeesWithRecords = await db
      .selectDistinct({
        userId: checkRecords.userId,
        userName: users.realName,
        userRole: users.role,
      })
      .from(checkRecords)
      .leftJoin(users, eq(checkRecords.userId, users.id))
      .where(and(
        gte(checkRecords.createdAt, startDate),
        lte(checkRecords.createdAt, endDate)
      ))
      .orderBy(users.realName);

    console.log(`📊 查询到 ${employeesWithRecords.length} 个员工记录`);

    return {
      success: true,
      message: '获取员工列表成功',
      data: employeesWithRecords
    };
  } catch (error) {
    console.error('Get employees with records error:', error);
    return {
      success: false,
      message: '获取员工列表失败'
    };
  }
}

/**
 * 导出报表
 */
export async function exportReport(context: AuthContext) {
  try {
    const body = context.body as any;
    console.log('Export report request:', body);

    // 这里可以实现Excel导出功能
    // 暂时返回成功消息
    return {
      success: true,
      message: '报表导出功能开发中',
      data: {
        downloadUrl: '/downloads/report.xlsx'
      }
    };
  } catch (error) {
    console.error('Export report error:', error);
    return {
      success: false,
      message: '导出报表失败'
    };
  }
}

/**
 * 重新计算指定日期范围的考勤统计
 */
export async function recalculateAttendanceRange(context: AuthContext) {
  try {
    const body = context.body as any;
    const { startDate, endDate } = body;

    console.log(`🔄 开始重新计算考勤统计: ${startDate} 到 ${endDate}`);

    // 调用服务层的重新计算函数
    await recalculateAttendanceRangeService(startDate, endDate);

    return {
      success: true,
      message: `成功重新计算 ${startDate} 到 ${endDate} 的考勤统计`
    };
  } catch (error) {
    console.error('Recalculate attendance range error:', error);
    return {
      success: false,
      message: '重新计算考勤统计失败'
    };
  }
}
