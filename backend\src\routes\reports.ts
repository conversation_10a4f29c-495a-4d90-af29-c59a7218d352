import { Elysia, t } from 'elysia';
import {
  getAttendanceReport,
  getDetailedReport,
  getSummaryReport,
  getRealtimeData,
  getMonthlyAttendance,
  getEmployeeProjectAttendance,
  getEmployeeDailyRecords,
  getEmployeesWithRecords,
  exportReport,
  recalculateAttendanceRange,
  getTodayPresentEmployees,
  getTodayAbsentEmployees,
  getTodayLateEmployees,
  getTodayAbnormalRecords,
  getMonthlyWorkHoursDetail
} from '../controllers/reports';
import { authMiddleware, adminMiddleware } from '../middleware/auth';

export const reportsRoutes = new Elysia({ prefix: '/reports' })
  .get('/attendance', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getAttendanceReport(authContext);
  })
  .get('/detailed', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getDetailedReport(authContext);
  })
  .get('/summary', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getSummaryReport(authContext);
  })
  .get('/realtime', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getRealtimeData(authContext);
  })
  .get('/monthly', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getMonthlyAttendance(authContext);
  }, {
    query: t.Object({
      year: t.Optional(t.String()),
      month: t.Optional(t.String()),
      projectId: t.Optional(t.String()),
      userId: t.Optional(t.String())
    })
  })
  .get('/employee-project', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getEmployeeProjectAttendance(authContext);
  }, {
    query: t.Object({
      startDate: t.Optional(t.String()),
      endDate: t.Optional(t.String()),
      projectId: t.Optional(t.String()),
      userId: t.Optional(t.String()),
      page: t.Optional(t.String()),
      limit: t.Optional(t.String())
    })
  })
  .get('/employee-daily', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getEmployeeDailyRecords(authContext);
  }, {
    query: t.Object({
      userId: t.String(),
      year: t.Optional(t.String()),
      month: t.Optional(t.String())
    })
  })
  .get('/employees-with-records', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getEmployeesWithRecords(authContext);
  }, {
    query: t.Object({
      year: t.Optional(t.String()),
      month: t.Optional(t.String())
    })
  })
  .post('/export', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return exportReport(authContext);
  }, {
    body: t.Object({
      type: t.String(),
      startDate: t.Optional(t.String()),
      endDate: t.Optional(t.String()),
      projectId: t.Optional(t.Number()),
      userId: t.Optional(t.Number())
    })
  })
  .post('/recalculate', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return recalculateAttendanceRange(authContext);
  }, {
    body: t.Object({
      startDate: t.String(),
      endDate: t.String()
    })
  })
  .get('/today/present', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getTodayPresentEmployees(authContext);
  })
  .get('/today/absent', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getTodayAbsentEmployees(authContext);
  })
  .get('/today/late', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getTodayLateEmployees(authContext);
  })
  .get('/today/abnormal', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getTodayAbnormalRecords(authContext);
  })
  .get('/monthly/workhours', (context) => {
    const authContext = authMiddleware(context);
    adminMiddleware(authContext);
    return getMonthlyWorkHoursDetail(authContext);
  });
