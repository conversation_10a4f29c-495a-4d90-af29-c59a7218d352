import { eq, and, sql, desc, asc } from 'drizzle-orm';
import { db, checkRecords, attendanceSummary, users } from '../models/db';
import { getChinaDateString, getChinaYesterdayString, getChinaDayStart, getChinaDayEnd, isChinaHour } from '../utils/timezone';

// 定义打卡记录类型（与数据库查询结果匹配）
interface CheckRecord {
  id: number;
  userId: number;
  locationId: number | null;
  projectId: number | null;
  checkType: 'in' | 'out';
  latitude: string | null;
  longitude: string | null;
  accuracy: number | null;
  address: string | null;
  distance: number | null;
  wifiSsid: string | null;
  deviceInfo: any;
  photoUrl: string | null;
  isOfflineSync: boolean | null;
  createdAt: Date | null;
}

/**
 * 计算指定日期的考勤统计
 */
export async function calculateAttendanceForDate(date: string) {
  try {
    console.log(`开始计算 ${date} 的考勤统计...`);

    // 获取指定日期的所有打卡记录，按用户和项目分组
    // 使用本地时间进行日期分组，避免UTC时区问题
    const records = await db
      .select()
      .from(checkRecords)
      .where(sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = ${date}`)
      .orderBy(checkRecords.userId, checkRecords.projectId, checkRecords.createdAt);

    // 按用户和项目分组处理
    const groupedRecords = new Map<string, CheckRecord[]>();
    
    for (const record of records) {
      const key = `${record.userId}_${record.projectId || 'null'}`;
      if (!groupedRecords.has(key)) {
        groupedRecords.set(key, []);
      }
      groupedRecords.get(key)!.push(record);
    }

    // 处理每个分组
    for (const [key, userRecords] of groupedRecords) {
      const parts = key.split('_');
      if (parts.length !== 2) {
        console.error(`无效的分组键格式: ${key}`);
        continue;
      }

      const userIdStr = parts[0];
      const projectIdStr = parts[1];

      if (!userIdStr || !projectIdStr) {
        console.error(`分组键格式错误: ${key}`);
        continue;
      }

      const userIdNum = parseInt(userIdStr);
      const projectIdNum = projectIdStr === 'null' ? null : parseInt(projectIdStr);

      if (isNaN(userIdNum)) {
        console.error(`无效的用户ID: ${userIdStr}`);
        continue;
      }

      await calculateUserDayAttendance(userIdNum, projectIdNum, date, userRecords);
    }

    console.log(`完成 ${date} 的考勤统计计算`);
  } catch (error) {
    console.error(`计算 ${date} 考勤统计失败:`, error);
  }
}

/**
 * 计算单个用户单日考勤统计
 */
async function calculateUserDayAttendance(
  userId: number,
  projectId: number | null,
  date: string,
  records: CheckRecord[]
) {
  try {
    // 分离签到和签退记录
    const checkInRecords = records.filter(r => r.checkType === 'in');
    const checkOutRecords = records.filter(r => r.checkType === 'out');

    // 计算基本统计
    const checkInCount = checkInRecords.length;
    const checkOutCount = checkOutRecords.length;
    
    const firstCheckInRecord = checkInRecords.length > 0 ? checkInRecords[0] : null;
    const firstCheckIn = firstCheckInRecord?.createdAt || null;

    const lastCheckOutRecord = checkOutRecords.length > 0 ? checkOutRecords[checkOutRecords.length - 1] : null;
    const lastCheckOut = lastCheckOutRecord?.createdAt || null;

    // 按时间排序所有记录
    const sortedRecords = records.sort((a, b) => {
      const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return timeA - timeB;
    });

    // 将签到签退记录配对
    const checkIns = sortedRecords.filter(r => r.checkType === 'in');
    const checkOuts = sortedRecords.filter(r => r.checkType === 'out');

    // 配对签到签退记录并计算工作时长
    const pairs = [];
    let totalWorkMinutes = 0;

    for (let i = 0; i < Math.min(checkIns.length, checkOuts.length); i++) {
      const checkIn = checkIns[i];
      const checkOut = checkOuts[i];

      if (checkIn && checkOut && checkIn.createdAt && checkOut.createdAt) {
        const workMinutes = Math.floor((new Date(checkOut.createdAt).getTime() - new Date(checkIn.createdAt).getTime()) / (1000 * 60));
        if (workMinutes > 0) {
          pairs.push({
            checkIn,
            checkOut,
            workMinutes
          });
          totalWorkMinutes += workMinutes;
        }
      }
    }

    // 判断是否异常 - 使用与 getEmployeeDailyRecords 相同的逻辑
    let isAbnormal = false;

    // 如果完全没有打卡记录，则为异常
    if (checkIns.length === 0 && checkOuts.length === 0) {
      isAbnormal = true;
    }
    // 如果只有签到没有签退，或只有签退没有签到，则为异常
    else if (checkIns.length > 0 && checkOuts.length === 0) {
      isAbnormal = true;
    }
    else if (checkIns.length === 0 && checkOuts.length > 0) {
      isAbnormal = true;
    }
    // 如果签到签退次数严重不匹配（差距大于1），则为异常
    else if (Math.abs(checkIns.length - checkOuts.length) > 1) {
      isAbnormal = true;
    }

    // 将字符串日期转换为 Date 对象
    const dateObj = new Date(date);

    const summaryData = {
      userId,
      projectId,
      date: dateObj,
      checkInCount,
      checkOutCount,
      firstCheckIn,
      lastCheckOut,
      totalWorkMinutes,
      isAbnormal,
    };

    // 使用 onDuplicateKeyUpdate 实现 upsert 操作，避免并发问题
    try {
      await db
        .insert(attendanceSummary)
        .values(summaryData)
        .onDuplicateKeyUpdate({
          set: {
            checkInCount: summaryData.checkInCount,
            checkOutCount: summaryData.checkOutCount,
            firstCheckIn: summaryData.firstCheckIn,
            lastCheckOut: summaryData.lastCheckOut,
            totalWorkMinutes: summaryData.totalWorkMinutes,
            isAbnormal: summaryData.isAbnormal,
            updatedAt: sql`NOW()`
          }
        });

      console.log(`更新考勤统计成功: 用户${userId}, 项目${projectId}, 日期${date}`);
    } catch (error: unknown) {
      console.error('更新考勤统计记录失败:', error);
      throw error;
    }

    console.log(`用户 ${userId} 项目 ${projectId} ${date} 考勤统计完成`);
  } catch (error) {
    console.error(`计算用户 ${userId} 考勤统计失败:`, error);
  }
}

/**
 * 计算昨天的考勤统计
 */
export async function calculateYesterdayAttendance() {
  const dateStr = getChinaYesterdayString();
  await calculateAttendanceForDate(dateStr);
}

/**
 * 计算今天的考勤统计
 */
export async function calculateTodayAttendance() {
  const dateStr = getChinaDateString();
  await calculateAttendanceForDate(dateStr);
}

/**
 * 重新计算指定日期范围的考勤统计
 */
export async function recalculateAttendanceRange(startDate: string, endDate: string) {
  try {
    console.log(`开始重新计算 ${startDate} 到 ${endDate} 的考勤统计...`);

    const start = new Date(startDate);
    const end = new Date(endDate);
    
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];
      if (dateStr) {
        await calculateAttendanceForDate(dateStr);
      }
    }

    console.log(`完成 ${startDate} 到 ${endDate} 的考勤统计重新计算`);
  } catch (error) {
    console.error(`重新计算考勤统计失败:`, error);
  }
}

/**
 * 启动定时任务
 */
export function startAttendanceCalculatorSchedule() {
  // 每天凌晨1点(中国时间)计算昨天的考勤统计
  setInterval(() => {
    if (isChinaHour(1)) {
      const now = new Date();
      if (now.getMinutes() === 0) {
        calculateYesterdayAttendance();
      }
    }
  }, 60 * 1000); // 每分钟检查一次

  // 每小时计算今天的考勤统计（实时更新）
  setInterval(() => {
    calculateTodayAttendance();
  }, 60 * 60 * 1000); // 每小时执行一次

  console.log('考勤统计定时任务已启动(使用中国标准时间)');
}
