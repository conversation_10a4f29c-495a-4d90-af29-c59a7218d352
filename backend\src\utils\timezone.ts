/**
 * 时区工具类 - 统一处理中国标准时间(CST)
 */

/**
 * 获取中国当前时间
 */
export function getChinaTime(): Date {
  // 修复：不能假设服务器在中国时区，需要明确转换到中国时间
  const now = new Date();
  return new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
}

/**
 * 获取中国当前日期字符串 (YYYY-MM-DD)
 */
export function getChinaDateString(): string {
  const now = new Date();
  // 修复：使用中国时区而不是服务器本地时间
  const chinaTime = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
  const year = chinaTime.getFullYear();
  const month = String(chinaTime.getMonth() + 1).padStart(2, '0');
  const day = String(chinaTime.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 获取中国时间的一天开始时间 (00:00:00)
 */
export function getChinaDayStart(date?: string): Date {
  const dateStr = date || getChinaDateString();
  const chinaTime = new Date(dateStr + 'T00:00:00');
  return chinaTime;
}

/**
 * 获取中国时间的一天结束时间 (23:59:59)
 */
export function getChinaDayEnd(date?: string): Date {
  const dateStr = date || getChinaDateString();
  const chinaTime = new Date(dateStr + 'T23:59:59');
  return chinaTime;
}

/**
 * 获取昨天的中国日期字符串
 */
export function getChinaYesterdayString(): string {
  const now = new Date();
  // 修复：使用中国时区计算昨天的日期
  const chinaTime = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
  chinaTime.setDate(chinaTime.getDate() - 1);
  const year = chinaTime.getFullYear();
  const month = String(chinaTime.getMonth() + 1).padStart(2, '0');
  const day = String(chinaTime.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 将UTC时间转换为中国时间（如果需要的话）
 */
export function utcToChinaTime(utcDate: Date): Date {
  // 如果服务器已经在中国时区，直接返回
  return utcDate;
}

/**
 * 将中国时间转换为UTC时间（如果需要的话）
 */
export function chinaTimeToUtc(chinaDate: Date): Date {
  // 如果服务器已经在中国时区，直接返回
  return chinaDate;
}

/**
 * 检查是否是中国时间的指定小时
 */
export function isChinaHour(hour: number): boolean {
  const now = new Date();
  // 修复：使用中国时区的小时而不是服务器本地时区
  const chinaTime = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
  return chinaTime.getHours() === hour;
}

/**
 * 格式化中国时间为字符串
 */
export function formatChinaTime(date: Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}
