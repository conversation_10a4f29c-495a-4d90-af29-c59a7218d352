/**
 * 时区工具类 - 统一处理中国标准时间(CST)
 */

/**
 * 获取中国当前时间
 */
export function getChinaTime(): Date {
  // 更安全的时区处理：使用UTC偏移量计算，避免toLocaleString的精度问题
  const now = new Date();

  // 检查服务器是否已经在中国时区（UTC+8）
  const serverOffset = now.getTimezoneOffset(); // 分钟，UTC+8 = -480
  const chinaOffset = -480; // 中国时区相对UTC的偏移量（分钟）

  if (serverOffset === chinaOffset) {
    // 服务器已经在中国时区，直接返回
    return now;
  } else {
    // 服务器不在中国时区，需要转换
    // 计算中国时间：UTC时间 + 8小时
    const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
    const chinaTime = new Date(utcTime + (8 * 3600000)); // +8小时
    return chinaTime;
  }
}

/**
 * 获取中国当前日期字符串 (YYYY-MM-DD)
 */
export function getChinaDateString(): string {
  const chinaTime = getChinaTime(); // 使用统一的时区处理函数
  const year = chinaTime.getFullYear();
  const month = String(chinaTime.getMonth() + 1).padStart(2, '0');
  const day = String(chinaTime.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 获取中国时间的一天开始时间 (00:00:00)
 */
export function getChinaDayStart(date?: string): Date {
  const dateStr = date || getChinaDateString();
  const chinaTime = new Date(dateStr + 'T00:00:00');
  return chinaTime;
}

/**
 * 获取中国时间的一天结束时间 (23:59:59)
 */
export function getChinaDayEnd(date?: string): Date {
  const dateStr = date || getChinaDateString();
  const chinaTime = new Date(dateStr + 'T23:59:59');
  return chinaTime;
}

/**
 * 获取昨天的中国日期字符串
 */
export function getChinaYesterdayString(): string {
  const chinaTime = getChinaTime(); // 使用统一的时区处理函数
  chinaTime.setDate(chinaTime.getDate() - 1);
  const year = chinaTime.getFullYear();
  const month = String(chinaTime.getMonth() + 1).padStart(2, '0');
  const day = String(chinaTime.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 将UTC时间转换为中国时间（如果需要的话）
 */
export function utcToChinaTime(utcDate: Date): Date {
  // 如果服务器已经在中国时区，直接返回
  return utcDate;
}

/**
 * 将中国时间转换为UTC时间（如果需要的话）
 */
export function chinaTimeToUtc(chinaDate: Date): Date {
  // 如果服务器已经在中国时区，直接返回
  return chinaDate;
}

/**
 * 检查是否是中国时间的指定小时
 */
export function isChinaHour(hour: number): boolean {
  const chinaTime = getChinaTime(); // 使用统一的时区处理函数
  return chinaTime.getHours() === hour;
}

/**
 * 格式化中国时间为字符串
 */
export function formatChinaTime(date: Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}
