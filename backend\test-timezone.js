#!/usr/bin/env node

/**
 * 时区处理测试脚本
 * 用于验证时区转换是否正确
 */

// 模拟数据库中的UTC时间戳
const testCases = [
  {
    name: "中国时间 2025-07-03 17:13:47",
    utcTime: "2025-07-03T09:13:47.000Z", // 对应中国时间 17:13:47
    expectedChinaDate: "2025-07-03"
  },
  {
    name: "中国时间 2025-07-04 06:46:43",
    utcTime: "2025-07-03T22:46:43.000Z", // 对应中国时间第二天 06:46:43
    expectedChinaDate: "2025-07-04"
  },
  {
    name: "跨天边界测试 - 中国时间 2025-07-04 00:30:00",
    utcTime: "2025-07-03T16:30:00.000Z", // 对应中国时间第二天 00:30:00
    expectedChinaDate: "2025-07-04"
  },
  {
    name: "跨天边界测试 - 中国时间 2025-07-03 23:30:00",
    utcTime: "2025-07-03T15:30:00.000Z", // 对应中国时间 23:30:00
    expectedChinaDate: "2025-07-03"
  }
];

console.log("🕐 时区处理测试开始...\n");

testCases.forEach((testCase, index) => {
  console.log(`测试 ${index + 1}: ${testCase.name}`);
  console.log(`UTC时间: ${testCase.utcTime}`);
  
  // 原来的错误方法（直接使用本地时间）
  const wrongDate = new Date(testCase.utcTime);
  const wrongDateStr = wrongDate.getFullYear() + '-' +
                      String(wrongDate.getMonth() + 1).padStart(2, '0') + '-' +
                      String(wrongDate.getDate()).padStart(2, '0');
  
  // 修复后的正确方法（转换为中国时间）
  const utcDate = new Date(testCase.utcTime);
  const chinaDate = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000); // UTC+8
  const correctDateStr = chinaDate.getFullYear() + '-' +
                        String(chinaDate.getMonth() + 1).padStart(2, '0') + '-' +
                        String(chinaDate.getDate()).padStart(2, '0');
  
  // 使用SQL CONVERT_TZ的方法验证
  const chinaTimeStr = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000).toISOString().slice(0, 10);
  
  console.log(`错误方法结果: ${wrongDateStr}`);
  console.log(`正确方法结果: ${correctDateStr}`);
  console.log(`SQL方法验证: ${chinaTimeStr}`);
  console.log(`期望结果: ${testCase.expectedChinaDate}`);
  
  const isCorrect = correctDateStr === testCase.expectedChinaDate;
  console.log(`✅ 测试结果: ${isCorrect ? '通过' : '失败'}`);
  
  if (!isCorrect) {
    console.log(`❌ 期望: ${testCase.expectedChinaDate}, 实际: ${correctDateStr}`);
  }
  
  console.log('---\n');
});

console.log("🎯 测试总结:");
console.log("修复前的问题: 直接使用 new Date(utcTime) 获取日期会导致时区偏差");
console.log("修复后的方案: 将UTC时间加上8小时偏移量，再获取日期");
console.log("这样可以确保按中国时间正确分组打卡记录");
