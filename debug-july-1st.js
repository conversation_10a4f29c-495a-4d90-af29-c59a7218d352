#!/usr/bin/env node

/**
 * 调试7月1日考勤数据
 */

import { db, checkRecords, attendanceSummary } from './src/models/db.js';
import { sql } from 'drizzle-orm';

const debugJuly1st = async () => {
  try {
    console.log('🔍 调试7月1日考勤数据...\n');
    
    // 1. 查看7月1日的原始打卡记录
    console.log('📋 7月1日原始打卡记录:');
    const rawRecords = await db
      .select()
      .from(checkRecords)
      .where(sql`DATE(CONVERT_TZ(${checkRecords.createdAt}, '+00:00', '+08:00')) = '2025-07-01'`)
      .orderBy(checkRecords.createdAt);
    
    console.log(`找到 ${rawRecords.length} 条记录:`);
    rawRecords.forEach(record => {
      const localTime = new Date(record.createdAt).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
      console.log(`  - 用户${record.userId}, 类型: ${record.checkType}, 时间: ${localTime} (UTC: ${record.createdAt})`);
    });
    
    // 2. 查看attendance_summary表中7月1日的统计数据
    console.log('\n📊 attendance_summary表中7月1日的统计数据:');
    const summaryRecords = await db
      .select()
      .from(attendanceSummary)
      .where(sql`date = '2025-07-01'`);
    
    console.log(`找到 ${summaryRecords.length} 条统计记录:`);
    summaryRecords.forEach(record => {
      console.log(`  - 用户${record.userId}, 项目${record.projectId}, 签到次数: ${record.checkInCount}, 签退次数: ${record.checkOutCount}`);
      console.log(`    首次签到: ${record.firstCheckIn}, 最后签退: ${record.lastCheckOut}`);
      console.log(`    工作时长: ${record.totalWorkMinutes}分钟, 异常: ${record.isAbnormal}`);
    });
    
    // 3. 手动计算7月1日的统计数据
    console.log('\n🧮 手动计算7月1日统计数据:');
    const checkIns = rawRecords.filter(r => r.checkType === 'in');
    const checkOuts = rawRecords.filter(r => r.checkType === 'out');
    
    console.log(`  签到记录: ${checkIns.length} 条`);
    console.log(`  签退记录: ${checkOuts.length} 条`);
    
    if (checkIns.length > 0) {
      console.log(`  首次签到: ${new Date(checkIns[0].createdAt).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`);
    }
    if (checkOuts.length > 0) {
      console.log(`  最后签退: ${new Date(checkOuts[checkOuts.length - 1].createdAt).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`);
    }
    
    // 4. 检查时区转换
    console.log('\n🌍 时区转换检查:');
    if (rawRecords.length > 0) {
      const firstRecord = rawRecords[0];
      console.log(`  原始UTC时间: ${firstRecord.createdAt}`);
      console.log(`  转换为中国时间: ${new Date(firstRecord.createdAt).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`);
      
      // 使用SQL查询验证
      const sqlResult = await db.execute(sql`
        SELECT 
          created_at,
          CONVERT_TZ(created_at, '+00:00', '+08:00') as china_time,
          DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) as china_date
        FROM check_records 
        WHERE id = ${firstRecord.id}
      `);
      
      console.log(`  SQL转换结果: ${JSON.stringify(sqlResult[0], null, 2)}`);
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    process.exit(0);
  }
};

debugJuly1st();
