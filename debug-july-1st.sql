-- 调试7月1日考勤数据的SQL查询

-- 1. 查看7月1日的原始打卡记录
SELECT 
    '=== 7月1日原始打卡记录 ===' as info;

SELECT 
    id,
    user_id,
    check_type,
    created_at as utc_time,
    CONVERT_TZ(created_at, '+00:00', '+08:00') as china_time,
    DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) as china_date,
    project_id,
    address
FROM check_records 
WHERE DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) = '2025-07-01'
ORDER BY created_at;

-- 2. 查看attendance_summary表中7月1日的统计数据
SELECT 
    '=== attendance_summary表中7月1日统计数据 ===' as info;

SELECT 
    id,
    user_id,
    project_id,
    date,
    check_in_count,
    check_out_count,
    first_check_in,
    last_check_out,
    total_work_minutes,
    is_abnormal,
    created_at,
    updated_at
FROM attendance_summary 
WHERE date = '2025-07-01';

-- 3. 手动统计7月1日的数据（按用户和项目分组）
SELECT 
    '=== 手动统计7月1日数据 ===' as info;

SELECT 
    user_id,
    project_id,
    SUM(CASE WHEN check_type = 'in' THEN 1 ELSE 0 END) as manual_check_in_count,
    SUM(CASE WHEN check_type = 'out' THEN 1 ELSE 0 END) as manual_check_out_count,
    MIN(CASE WHEN check_type = 'in' THEN CONVERT_TZ(created_at, '+00:00', '+08:00') END) as manual_first_check_in,
    MAX(CASE WHEN check_type = 'out' THEN CONVERT_TZ(created_at, '+00:00', '+08:00') END) as manual_last_check_out,
    COUNT(*) as total_records
FROM check_records 
WHERE DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) = '2025-07-01'
GROUP BY user_id, project_id
ORDER BY user_id, project_id;

-- 4. 对比统计数据和实际数据
SELECT 
    '=== 对比统计数据和实际数据 ===' as info;

SELECT 
    cr.user_id,
    cr.project_id,
    -- 实际统计
    SUM(CASE WHEN cr.check_type = 'in' THEN 1 ELSE 0 END) as actual_check_in_count,
    SUM(CASE WHEN cr.check_type = 'out' THEN 1 ELSE 0 END) as actual_check_out_count,
    -- 表中记录
    COALESCE(ats.check_in_count, 0) as summary_check_in_count,
    COALESCE(ats.check_out_count, 0) as summary_check_out_count,
    -- 是否匹配
    CASE 
        WHEN SUM(CASE WHEN cr.check_type = 'in' THEN 1 ELSE 0 END) = COALESCE(ats.check_in_count, 0) 
        THEN 'MATCH' 
        ELSE 'MISMATCH' 
    END as check_in_match,
    CASE 
        WHEN SUM(CASE WHEN cr.check_type = 'out' THEN 1 ELSE 0 END) = COALESCE(ats.check_out_count, 0) 
        THEN 'MATCH' 
        ELSE 'MISMATCH' 
    END as check_out_match
FROM check_records cr
LEFT JOIN attendance_summary ats ON (
    cr.user_id = ats.user_id 
    AND (cr.project_id = ats.project_id OR (cr.project_id IS NULL AND ats.project_id IS NULL))
    AND ats.date = '2025-07-01'
)
WHERE DATE(CONVERT_TZ(cr.created_at, '+00:00', '+08:00')) = '2025-07-01'
GROUP BY cr.user_id, cr.project_id, ats.check_in_count, ats.check_out_count
ORDER BY cr.user_id, cr.project_id;

-- 5. 检查是否有重复的attendance_summary记录
SELECT 
    '=== 检查重复的attendance_summary记录 ===' as info;

SELECT 
    user_id,
    project_id,
    date,
    COUNT(*) as record_count
FROM attendance_summary 
WHERE date = '2025-07-01'
GROUP BY user_id, project_id, date
HAVING COUNT(*) > 1;
