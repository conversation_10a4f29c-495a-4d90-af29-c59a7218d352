-- 查找7月1日相关的打卡记录（扩大搜索范围）

-- 1. 查找UTC时间在7月1日的记录
SELECT 
    '=== UTC时间在7月1日的记录 ===' as info;

SELECT
    id,
    user_id,
    check_type,
    created_at utc_time,
    CONVERT_TZ(created_at, '+00:00', '+08:00') china_time,
    DATE(created_at) utc_date,
    DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) china_date,
    project_id,
    address
FROM check_records
WHERE DATE(created_at) = '2025-07-01'
ORDER BY created_at;

-- 2. 查找中国时间在6月30日到7月2日之间的所有记录
SELECT 
    '=== 6月30日到7月2日之间的所有记录 ===' as info;

SELECT
    id,
    user_id,
    check_type,
    created_at utc_time,
    CONVERT_TZ(created_at, '+00:00', '+08:00') china_time,
    DATE(created_at) utc_date,
    DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) china_date,
    project_id,
    address
FROM check_records
WHERE DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) BETWEEN '2025-06-30' AND '2025-07-02'
ORDER BY created_at;

-- 3. 查找时间包含 06:41 或 17:02 的记录（根据弹窗显示的时间）
SELECT 
    '=== 包含06:41或17:02时间的记录 ===' as info;

SELECT
    id,
    user_id,
    check_type,
    created_at utc_time,
    CONVERT_TZ(created_at, '+00:00', '+08:00') china_time,
    DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) china_date,
    TIME(CONVERT_TZ(created_at, '+00:00', '+08:00')) china_time_only,
    project_id,
    address
FROM check_records
WHERE TIME(CONVERT_TZ(created_at, '+00:00', '+08:00')) LIKE '06:41%'
   OR TIME(CONVERT_TZ(created_at, '+00:00', '+08:00')) LIKE '17:02%'
ORDER BY created_at;

-- 4. 查找7月份的所有记录（看看数据分布）
SELECT 
    '=== 7月份所有记录的日期分布 ===' as info;

SELECT 
    DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) as china_date,
    COUNT(*) as record_count,
    SUM(CASE WHEN check_type = 'in' THEN 1 ELSE 0 END) as check_in_count,
    SUM(CASE WHEN check_type = 'out' THEN 1 ELSE 0 END) as check_out_count
FROM check_records 
WHERE DATE(CONVERT_TZ(created_at, '+00:00', '+08:00')) BETWEEN '2025-07-01' AND '2025-07-31'
GROUP BY DATE(CONVERT_TZ(created_at, '+00:00', '+08:00'))
ORDER BY china_date;

-- 5. 检查attendance_summary表中7月份的数据
SELECT 
    '=== attendance_summary表中7月份的数据 ===' as info;

SELECT 
    date,
    user_id,
    project_id,
    check_in_count,
    check_out_count,
    first_check_in,
    last_check_out
FROM attendance_summary 
WHERE date BETWEEN '2025-07-01' AND '2025-07-31'
ORDER BY date, user_id;
