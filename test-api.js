#!/usr/bin/env node

/**
 * 测试修复后的 API
 */

const testAPI = async () => {
  try {
    // 测试获取详细报告的 API
    const response = await fetch('http://localhost:3000/api/reports/detailed?userId=1&date=2025-07-03', {
      headers: {
        'Authorization': 'Bearer your-token-here', // 需要替换为实际的token
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API 调用成功');
      console.log('📊 返回的记录数量:', data.data?.records?.length || 0);
      
      if (data.data?.records?.length > 0) {
        console.log('📅 记录详情:');
        data.data.records.forEach((record, index) => {
          const time = new Date(record.createdAt).toLocaleString('zh-CN');
          console.log(`  ${index + 1}. ${record.checkType} - ${time}`);
        });
      }
    } else {
      console.log('❌ API 调用失败:', response.status, response.statusText);
    }
  } catch (error) {
    console.log('❌ 网络错误:', error.message);
    console.log('💡 请确保后端服务正在运行在 http://localhost:3000');
  }
};

testAPI();
