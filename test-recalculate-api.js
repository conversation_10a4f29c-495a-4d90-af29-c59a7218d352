#!/usr/bin/env node

/**
 * 测试重新计算考勤统计API
 */

const testRecalculateAPI = async () => {
  try {
    console.log('🔄 调用重新计算API...');
    
    const response = await fetch('http://localhost:3000/api/reports/recalculate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-admin-token-here' // 需要管理员权限
      },
      body: JSON.stringify({
        startDate: '2025-07-01',
        endDate: '2025-07-31'
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API调用成功:', data);
    } else {
      console.log('❌ API调用失败:', response.status, response.statusText);
      const errorData = await response.text();
      console.log('错误详情:', errorData);
    }
  } catch (error) {
    console.log('❌ 网络错误:', error.message);
    console.log('💡 请确保:');
    console.log('  1. 后端服务正在运行在 http://localhost:3000');
    console.log('  2. 使用有效的管理员token');
    console.log('  3. 或者直接在前端管理界面点击"重新计算当前月数据"按钮');
  }
};

testRecalculateAPI();
