import{d as oe,r as c,p as ae,b as d,M as te,o as _,c as C,e as o,f as a,w as n,g as R,i as r,s as ne,q as A,y as g,x as w,aI as se,E as p,aJ as q,z as ie,aK as re,aL as ue,aM as de,T as pe}from"./index-4256ff3d.js";import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";const ce={class:"app-versions"},me={class:"header"},fe={class:"header-left"},_e={class:"download-urls"},ge={key:0,class:"url-item"},Ve={key:1,class:"url-item"},Ue={key:2},be={class:"pagination"},we=oe({__name:"AppVersions",setup(ye){const x=c(!1),B=c(!1),h=c([]),P=c(0),y=c(1),S=c(20),V=c(!1),z=c(!1),v=c(null),k=c(),t=c({version:"",versionCode:1,title:"",description:"",downloadUrl:"",downloadUrlBackup:"",fileSize:void 0,platform:"android",isForceUpdate:!1,minSupportVersion:""}),O={version:[{required:!0,message:"请输入版本号",trigger:"blur"},{pattern:/^\d+\.\d+\.\d+$/,message:"版本号格式应为 x.y.z",trigger:"blur"}],versionCode:[{required:!0,message:"请输入版本代码",trigger:"blur"}],title:[{required:!0,message:"请输入版本标题",trigger:"blur"}],downloadUrl:[{type:"url",message:"请输入有效的URL地址",trigger:"blur"}],downloadUrlBackup:[{type:"url",message:"请输入有效的备用URL地址",trigger:"blur"}]},U=async()=>{x.value=!0;try{const i=await se({page:y.value,limit:S.value});i.success&&(h.value=i.data.versions,P.value=i.data.pagination.total)}catch{p.error("获取版本列表失败")}finally{x.value=!1}},I=i=>{S.value=i,y.value=1,U()},L=i=>{y.value=i,U()},K=i=>{v.value=i,t.value={...i,fileSize:i.fileSize||void 0},V.value=!0},j=async i=>{var e,u;try{const s=await q(i.id,{isActive:!i.isActive});s.success?(p.success(i.isActive?"版本已禁用":"版本已激活"),U()):p.error(s.message||"操作失败")}catch(s){(u=(e=s.response)==null?void 0:e.data)!=null&&u.message?p.error(s.response.data.message):p.error("操作失败")}},T=async i=>{var e,u;try{await ie.confirm(`确定要删除版本 ${i.version} 吗？`,"确认删除",{type:"warning"});const s=await re(i.id);s.success?(p.success("删除成功"),U()):p.error(s.message||"删除失败")}catch(s){s!=="cancel"&&((u=(e=s.response)==null?void 0:e.data)!=null&&u.message?p.error(s.response.data.message):p.error("删除失败"))}},G=async()=>{var i,e;if(k.value)try{await k.value.validate(),B.value=!0;let u;if(v.value){const s={title:t.value.title,description:t.value.description,downloadUrl:t.value.downloadUrl,downloadUrlBackup:t.value.downloadUrlBackup,isForceUpdate:t.value.isForceUpdate,minSupportVersion:t.value.minSupportVersion};t.value.fileSize!==null&&t.value.fileSize!==void 0&&(s.fileSize=t.value.fileSize),u=await q(v.value.id,s)}else{const s={version:t.value.version,versionCode:t.value.versionCode,title:t.value.title,description:t.value.description,downloadUrl:t.value.downloadUrl,downloadUrlBackup:t.value.downloadUrlBackup,platform:t.value.platform,isForceUpdate:t.value.isForceUpdate,minSupportVersion:t.value.minSupportVersion};t.value.fileSize!==null&&t.value.fileSize!==void 0&&(s.fileSize=t.value.fileSize),u=await ue(s)}u.success?(p.success(v.value?"更新成功":"创建成功"),V.value=!1,U()):p.error(u.message||(v.value?"更新失败":"创建失败"))}catch(u){console.error("Submit error:",u),(e=(i=u.response)==null?void 0:i.data)!=null&&e.message?p.error(u.response.data.message):p.error(v.value?"更新失败":"创建失败")}finally{B.value=!1}},H=()=>{v.value=null,t.value={version:"",versionCode:1,title:"",description:"",downloadUrl:"",downloadUrlBackup:"",fileSize:void 0,platform:"android",isForceUpdate:!1,minSupportVersion:""},k.value&&k.value.resetFields()},J=i=>new Date(i).toLocaleString("zh-CN");return ae(()=>{U()}),(i,e)=>{const u=d("el-icon"),s=d("el-button"),Y=d("el-tooltip"),m=d("el-table-column"),F=d("el-tag"),D=d("el-link"),Q=d("el-table"),W=d("el-pagination"),b=d("el-input"),f=d("el-form-item"),$=d("el-input-number"),M=d("el-option"),X=d("el-select"),Z=d("el-switch"),ee=d("el-form"),N=d("el-dialog"),le=te("loading");return _(),C("div",ce,[o("div",me,[o("div",fe,[e[18]||(e[18]=o("h2",null,"应用版本管理",-1)),a(Y,{content:"点击查看移动端版本号设置说明",placement:"bottom"},{default:n(()=>[a(s,{type:"info",size:"small",circle:"",onClick:e[0]||(e[0]=l=>z.value=!0),class:"help-button"},{default:n(()=>[a(u,null,{default:n(()=>[a(R(de))]),_:1})]),_:1})]),_:1})]),a(s,{type:"primary",onClick:e[1]||(e[1]=l=>V.value=!0)},{default:n(()=>[a(u,null,{default:n(()=>[a(R(pe))]),_:1}),e[19]||(e[19]=r(" 新增版本 "))]),_:1,__:[19]})]),ne((_(),A(Q,{data:h.value,stripe:""},{default:n(()=>[a(m,{prop:"version",label:"版本号",width:"120"}),a(m,{prop:"versionCode",label:"版本代码",width:"100"}),a(m,{prop:"title",label:"版本标题","min-width":"200"}),a(m,{prop:"platform",label:"平台",width:"80"},{default:n(({row:l})=>[a(F,{type:l.platform==="android"?"success":"primary"},{default:n(()=>[r(g(l.platform==="android"?"Android":"iOS"),1)]),_:2},1032,["type"])]),_:1}),a(m,{prop:"isForceUpdate",label:"强制更新",width:"100"},{default:n(({row:l})=>[a(F,{type:l.isForceUpdate?"danger":"info"},{default:n(()=>[r(g(l.isForceUpdate?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),a(m,{prop:"isActive",label:"状态",width:"80"},{default:n(({row:l})=>[a(F,{type:l.isActive?"success":"danger"},{default:n(()=>[r(g(l.isActive?"激活":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(m,{label:"下载地址",width:"200"},{default:n(({row:l})=>[o("div",_e,[l.downloadUrl?(_(),C("div",ge,[a(D,{href:l.downloadUrl,target:"_blank",type:"primary",size:"small"},{default:n(()=>e[20]||(e[20]=[r(" 主地址 ")])),_:2,__:[20]},1032,["href"])])):w("",!0),l.downloadUrlBackup?(_(),C("div",Ve,[a(D,{href:l.downloadUrlBackup,target:"_blank",type:"success",size:"small"},{default:n(()=>e[21]||(e[21]=[r(" 备用地址 ")])),_:2,__:[21]},1032,["href"])])):w("",!0),!l.downloadUrl&&!l.downloadUrlBackup?(_(),C("span",Ue,"-")):w("",!0)])]),_:1}),a(m,{prop:"fileSize",label:"文件大小",width:"100"},{default:n(({row:l})=>[r(g(l.fileSize?`${(l.fileSize/1024/1024).toFixed(1)}MB`:"-"),1)]),_:1}),a(m,{prop:"createdAt",label:"创建时间",width:"180"},{default:n(({row:l})=>[r(g(J(l.createdAt)),1)]),_:1}),a(m,{label:"操作",width:"200",fixed:"right"},{default:n(({row:l})=>[a(s,{size:"small",onClick:E=>K(l)},{default:n(()=>e[22]||(e[22]=[r("编辑")])),_:2,__:[22]},1032,["onClick"]),a(s,{size:"small",type:l.isActive?"warning":"success",onClick:E=>j(l)},{default:n(()=>[r(g(l.isActive?"禁用":"激活"),1)]),_:2},1032,["type","onClick"]),a(s,{size:"small",type:"danger",onClick:E=>T(l)},{default:n(()=>e[23]||(e[23]=[r("删除")])),_:2,__:[23]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[le,x.value]]),o("div",be,[a(W,{"current-page":y.value,"onUpdate:currentPage":e[2]||(e[2]=l=>y.value=l),"page-size":S.value,"onUpdate:pageSize":e[3]||(e[3]=l=>S.value=l),total:P.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:I,onCurrentChange:L},null,8,["current-page","page-size","total"])]),a(N,{modelValue:V.value,"onUpdate:modelValue":e[15]||(e[15]=l=>V.value=l),title:v.value?"编辑版本":"新增版本",width:"600px",onClose:H},{footer:n(()=>[a(s,{onClick:e[14]||(e[14]=l=>V.value=!1)},{default:n(()=>e[24]||(e[24]=[r("取消")])),_:1,__:[24]}),a(s,{type:"primary",onClick:G,loading:B.value},{default:n(()=>[r(g(v.value?"更新":"创建"),1)]),_:1},8,["loading"])]),default:n(()=>[a(ee,{model:t.value,rules:O,ref_key:"formRef",ref:k,"label-width":"120px"},{default:n(()=>[v.value?w("",!0):(_(),A(f,{key:0,label:"版本号",prop:"version"},{default:n(()=>[a(b,{modelValue:t.value.version,"onUpdate:modelValue":e[4]||(e[4]=l=>t.value.version=l),placeholder:"如: 1.0.0"},null,8,["modelValue"])]),_:1})),v.value?w("",!0):(_(),A(f,{key:1,label:"版本代码",prop:"versionCode"},{default:n(()=>[a($,{modelValue:t.value.versionCode,"onUpdate:modelValue":e[5]||(e[5]=l=>t.value.versionCode=l),min:1,placeholder:"用于版本比较的数字"},null,8,["modelValue"])]),_:1})),a(f,{label:"版本标题",prop:"title"},{default:n(()=>[a(b,{modelValue:t.value.title,"onUpdate:modelValue":e[6]||(e[6]=l=>t.value.title=l),placeholder:"版本标题"},null,8,["modelValue"])]),_:1}),a(f,{label:"更新描述",prop:"description"},{default:n(()=>[a(b,{modelValue:t.value.description,"onUpdate:modelValue":e[7]||(e[7]=l=>t.value.description=l),type:"textarea",rows:3,placeholder:"描述本次更新的内容"},null,8,["modelValue"])]),_:1}),a(f,{label:"下载地址",prop:"downloadUrl"},{default:n(()=>[a(b,{modelValue:t.value.downloadUrl,"onUpdate:modelValue":e[8]||(e[8]=l=>t.value.downloadUrl=l),placeholder:"APK下载地址"},null,8,["modelValue"])]),_:1}),a(f,{label:"备用下载地址",prop:"downloadUrlBackup"},{default:n(()=>[a(b,{modelValue:t.value.downloadUrlBackup,"onUpdate:modelValue":e[9]||(e[9]=l=>t.value.downloadUrlBackup=l),placeholder:"APK备用下载地址"},null,8,["modelValue"])]),_:1}),a(f,{label:"文件大小",prop:"fileSize"},{default:n(()=>[a($,{modelValue:t.value.fileSize,"onUpdate:modelValue":e[10]||(e[10]=l=>t.value.fileSize=l),min:0,placeholder:"文件大小(字节)"},null,8,["modelValue"])]),_:1}),v.value?w("",!0):(_(),A(f,{key:2,label:"平台",prop:"platform"},{default:n(()=>[a(X,{modelValue:t.value.platform,"onUpdate:modelValue":e[11]||(e[11]=l=>t.value.platform=l)},{default:n(()=>[a(M,{label:"Android",value:"android"}),a(M,{label:"iOS",value:"ios"})]),_:1},8,["modelValue"])]),_:1})),a(f,{label:"强制更新"},{default:n(()=>[a(Z,{modelValue:t.value.isForceUpdate,"onUpdate:modelValue":e[12]||(e[12]=l=>t.value.isForceUpdate=l)},null,8,["modelValue"])]),_:1}),a(f,{label:"最低支持版本",prop:"minSupportVersion"},{default:n(()=>[a(b,{modelValue:t.value.minSupportVersion,"onUpdate:modelValue":e[13]||(e[13]=l=>t.value.minSupportVersion=l),placeholder:"如: 1.0.0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),a(N,{modelValue:z.value,"onUpdate:modelValue":e[17]||(e[17]=l=>z.value=l),title:"移动端版本号设置说明",width:"700px"},{footer:n(()=>[a(s,{onClick:e[16]||(e[16]=l=>z.value=!1)},{default:n(()=>e[25]||(e[25]=[r("知道了")])),_:1,__:[25]})]),default:n(()=>[e[26]||(e[26]=o("div",{class:"help-content"},[o("h3",null,"📱 移动端版本号设置位置"),o("p",null,"移动端APP的当前版本号需要在以下文件中设定："),o("div",{class:"help-section"},[o("h4",null,"1. 应用配置文件（用于版本检查）"),o("p",null,[o("strong",null,"文件路径："),o("code",null,"app/src/constants/index.ts")]),o("div",{class:"code-block"},[o("pre",null,[o("code",null,`// 应用配置
export const APP_CONFIG = {
  NAME: '考勤打卡系统',
  VERSION: '1.0.0',  // ← 修改这里的版本号
  COMPANY: '华信联建筑装饰',
  SUPPORT_EMAIL: '<EMAIL>',
};`)])])]),o("div",{class:"help-section"},[o("h4",null,"2. Expo配置文件（用于应用构建）"),o("p",null,[o("strong",null,"文件路径："),o("code",null,"app/app.json")]),o("div",{class:"code-block"},[o("pre",null,[o("code",null,`{
  "expo": {
    "name": "考勤打卡系统",
    "slug": "kaoqin-app",
    "version": "1.0.0",  // ← 修改这里的版本号
    ...
  }
}`)])])]),o("div",{class:"help-section"},[o("h4",null,"🧪 测试版本更新功能"),o("ol",null,[o("li",null,[r("将移动端版本号设置为较低版本（如 "),o("code",null,"0.9.0"),r("）")]),o("li",null,[r("在此管理后台创建新版本记录（如 "),o("code",null,"1.1.0"),r("）")]),o("li",null,'重启移动端应用或在个人中心点击"检查更新"'),o("li",null,"应该会显示更新提示弹窗")])]),o("div",{class:"help-section"},[o("h4",null,"📋 版本号规则"),o("ul",null,[o("li",null,[o("strong",null,"格式："),r("使用语义化版本 "),o("code",null,"主版本.次版本.修订版本")]),o("li",null,[o("strong",null,"示例："),o("code",null,"1.0.0"),r(" → "),o("code",null,"1.0.1"),r(" → "),o("code",null,"1.1.0"),r(" → "),o("code",null,"2.0.0")]),o("li",null,[o("strong",null,"版本代码："),r("必须递增的整数，用于版本比较")]),o("li",null,[o("strong",null,"建议："),r("正式发布时两个文件的版本号保持一致")])])]),o("div",{class:"help-section warning"},[o("h4",null,"⚠️ 注意事项"),o("ul",null,[o("li",null,"版本号必须严格按照语义化版本格式"),o("li",null,"版本代码必须是唯一的递增整数"),o("li",null,"下载地址必须是可访问的APK文件链接"),o("li",null,"强制更新会阻止用户使用应用，请谨慎设置")])])],-1))]),_:1,__:[26]},8,["modelValue"])])}}});const ze=ve(we,[["__scopeId","data-v-5f306f79"]]);export{ze as default};
