import{d as P,r as M,a as D,p as J,b as d,M as K,o as g,c as S,e as f,f as e,w as t,i as o,s as O,q as k,y as s,x as C,aG as Q,E as V,aH as W}from"./index-4256ff3d.js";import{_ as X}from"./_plugin-vue_export-helper-c27b6911.js";const Z={class:"checkin-applications"},ee={class:"filters"},te={class:"table-container"},ae={class:"pagination"},le={key:0,class:"application-detail"},oe=P({__name:"CheckinApplications",setup(ie){const R=M(!1),x=M([]),r=D({status:"",userName:"",dateRange:null}),m=D({page:1,limit:20,total:0}),i=D({visible:!1,loading:!1,action:"",application:null,form:{reviewComment:""}}),n=D({visible:!1,application:null}),y=async()=>{var p;R.value=!0;try{const a={page:m.page,limit:m.limit};r.status&&(a.status=r.status),r.userName&&(a.userName=r.userName),r.dateRange&&(a.startDate=r.dateRange[0],a.endDate=r.dateRange[1]);const _=await Q(a);_.success?(x.value=_.data.applications||[],m.total=((p=_.data.pagination)==null?void 0:p.total)||0):V.error(_.message||"加载申请列表失败")}catch(a){console.error("Load applications error:",a),V.error("网络错误，请重试")}finally{R.value=!1}},$=()=>{r.status="",r.userName="",r.dateRange=null,m.page=1,y()},T=(p,a)=>{i.application=p,i.action=a,i.form.reviewComment="",i.visible=!0},q=async()=>{i.loading=!0;try{const p=await W(i.application.id,{status:i.action,reviewComment:i.form.reviewComment||void 0});p.success?(V.success(`申请已${i.action==="approved"?"通过":"拒绝"}`),i.visible=!1,y()):V.error(p.message||"审核失败")}catch(p){console.error("Review application error:",p),V.error("网络错误，请重试")}finally{i.loading=!1}},B=p=>{n.application=p,n.visible=!0},w=p=>p?new Date(p).toLocaleString("zh-CN"):"-",A=p=>{switch(p){case"pending":return"warning";case"approved":return"success";case"rejected":return"danger";default:return"info"}},z=p=>{switch(p){case"pending":return"待审核";case"approved":return"已通过";case"rejected":return"已拒绝";default:return"未知"}};return J(()=>{y()}),(p,a)=>{const _=d("el-option"),E=d("el-select"),v=d("el-form-item"),U=d("el-input"),I=d("el-date-picker"),b=d("el-button"),Y=d("el-form"),c=d("el-table-column"),h=d("el-tag"),G=d("el-table"),L=d("el-pagination"),j=d("el-dialog"),u=d("el-descriptions-item"),F=d("el-descriptions"),H=K("loading");return g(),S("div",Z,[a[16]||(a[16]=f("div",{class:"page-header"},[f("h1",null,"补卡申请管理"),f("p",null,"管理员工的补卡申请，审核通过后将自动生成打卡记录")],-1)),f("div",ee,[e(Y,{model:r,inline:""},{default:t(()=>[e(v,{label:"状态"},{default:t(()=>[e(E,{modelValue:r.status,"onUpdate:modelValue":a[0]||(a[0]=l=>r.status=l),placeholder:"选择状态",clearable:""},{default:t(()=>[e(_,{label:"全部",value:""}),e(_,{label:"待审核",value:"pending"}),e(_,{label:"已通过",value:"approved"}),e(_,{label:"已拒绝",value:"rejected"})]),_:1},8,["modelValue"])]),_:1}),e(v,{label:"申请人"},{default:t(()=>[e(U,{modelValue:r.userName,"onUpdate:modelValue":a[1]||(a[1]=l=>r.userName=l),placeholder:"输入用户名",clearable:""},null,8,["modelValue"])]),_:1}),e(v,{label:"申请时间"},{default:t(()=>[e(I,{modelValue:r.dateRange,"onUpdate:modelValue":a[2]||(a[2]=l=>r.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(b,{type:"primary",onClick:y},{default:t(()=>a[9]||(a[9]=[o("查询")])),_:1,__:[9]}),e(b,{onClick:$},{default:t(()=>a[10]||(a[10]=[o("重置")])),_:1,__:[10]})]),_:1})]),_:1},8,["model"])]),f("div",te,[O((g(),k(G,{data:x.value,stripe:"",border:"",style:{width:"100%"}},{default:t(()=>[e(c,{prop:"id",label:"申请ID",width:"80"}),e(c,{prop:"userRealName",label:"申请人",width:"100"},{default:t(({row:l})=>[o(s(l.userRealName||l.userName),1)]),_:1}),e(c,{prop:"projectName",label:"项目",width:"150"}),e(c,{prop:"locationName",label:"地点",width:"150"}),e(c,{prop:"checkType",label:"类型",width:"80"},{default:t(({row:l})=>[e(h,{type:l.checkType==="in"?"success":"warning"},{default:t(()=>[o(s(l.checkType==="in"?"上班":"下班"),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"requestedTime",label:"补卡时间",width:"160"},{default:t(({row:l})=>[o(s(w(l.requestedTime)),1)]),_:1}),e(c,{prop:"reason",label:"申请原因","min-width":"200","show-overflow-tooltip":""}),e(c,{prop:"status",label:"状态",width:"100"},{default:t(({row:l})=>[e(h,{type:A(l.status),effect:"dark"},{default:t(()=>[o(s(z(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"createdAt",label:"申请时间",width:"160"},{default:t(({row:l})=>[o(s(w(l.createdAt)),1)]),_:1}),e(c,{label:"操作",width:"200",fixed:"right"},{default:t(({row:l})=>[l.status==="pending"?(g(),k(b,{key:0,type:"success",size:"small",onClick:N=>T(l,"approved")},{default:t(()=>a[11]||(a[11]=[o(" 通过 ")])),_:2,__:[11]},1032,["onClick"])):C("",!0),l.status==="pending"?(g(),k(b,{key:1,type:"danger",size:"small",onClick:N=>T(l,"rejected")},{default:t(()=>a[12]||(a[12]=[o(" 拒绝 ")])),_:2,__:[12]},1032,["onClick"])):C("",!0),e(b,{type:"info",size:"small",onClick:N=>B(l)},{default:t(()=>a[13]||(a[13]=[o(" 详情 ")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[H,R.value]]),f("div",ae,[e(L,{"current-page":m.page,"onUpdate:currentPage":a[3]||(a[3]=l=>m.page=l),"page-size":m.limit,"onUpdate:pageSize":a[4]||(a[4]=l=>m.limit=l),"page-sizes":[10,20,50,100],total:m.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:y,onCurrentChange:y},null,8,["current-page","page-size","total"])])]),e(j,{modelValue:i.visible,"onUpdate:modelValue":a[7]||(a[7]=l=>i.visible=l),title:`${i.action==="approved"?"审核通过":"审核拒绝"}`,width:"500px"},{footer:t(()=>[e(b,{onClick:a[6]||(a[6]=l=>i.visible=!1)},{default:t(()=>a[14]||(a[14]=[o("取消")])),_:1,__:[14]}),e(b,{type:"primary",loading:i.loading,onClick:q},{default:t(()=>a[15]||(a[15]=[o(" 确定 ")])),_:1,__:[15]},8,["loading"])]),default:t(()=>[e(Y,{model:i.form,"label-width":"80px"},{default:t(()=>[e(v,{label:"申请人"},{default:t(()=>{var l,N;return[f("span",null,s(((l=i.application)==null?void 0:l.userRealName)||((N=i.application)==null?void 0:N.userName)),1)]}),_:1}),e(v,{label:"补卡时间"},{default:t(()=>{var l;return[f("span",null,s(w((l=i.application)==null?void 0:l.requestedTime)),1)]}),_:1}),e(v,{label:"申请原因"},{default:t(()=>{var l;return[f("span",null,s((l=i.application)==null?void 0:l.reason),1)]}),_:1}),e(v,{label:"审核意见"},{default:t(()=>[e(U,{modelValue:i.form.reviewComment,"onUpdate:modelValue":a[5]||(a[5]=l=>i.form.reviewComment=l),type:"textarea",rows:3,placeholder:"请输入审核意见（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(j,{modelValue:n.visible,"onUpdate:modelValue":a[8]||(a[8]=l=>n.visible=l),title:"申请详情",width:"600px"},{default:t(()=>[n.application?(g(),S("div",le,[e(F,{column:2,border:""},{default:t(()=>[e(u,{label:"申请ID"},{default:t(()=>[o(s(n.application.id),1)]),_:1}),e(u,{label:"申请人"},{default:t(()=>[o(s(n.application.userRealName||n.application.userName),1)]),_:1}),e(u,{label:"项目"},{default:t(()=>[o(s(n.application.projectName),1)]),_:1}),e(u,{label:"地点"},{default:t(()=>[o(s(n.application.locationName),1)]),_:1}),e(u,{label:"打卡类型"},{default:t(()=>[e(h,{type:n.application.checkType==="in"?"success":"warning"},{default:t(()=>[o(s(n.application.checkType==="in"?"上班打卡":"下班打卡"),1)]),_:1},8,["type"])]),_:1}),e(u,{label:"补卡时间"},{default:t(()=>[o(s(w(n.application.requestedTime)),1)]),_:1}),e(u,{label:"申请状态"},{default:t(()=>[e(h,{type:A(n.application.status)},{default:t(()=>[o(s(z(n.application.status)),1)]),_:1},8,["type"])]),_:1}),e(u,{label:"申请时间"},{default:t(()=>[o(s(w(n.application.createdAt)),1)]),_:1}),e(u,{label:"申请原因",span:2},{default:t(()=>[o(s(n.application.reason),1)]),_:1}),n.application.reviewerName?(g(),k(u,{key:0,label:"审核人"},{default:t(()=>[o(s(n.application.reviewerName),1)]),_:1})):C("",!0),n.application.reviewedAt?(g(),k(u,{key:1,label:"审核时间"},{default:t(()=>[o(s(w(n.application.reviewedAt)),1)]),_:1})):C("",!0),n.application.reviewComment?(g(),k(u,{key:2,label:"审核意见",span:2},{default:t(()=>[o(s(n.application.reviewComment),1)]),_:1})):C("",!0)]),_:1})])):C("",!0)]),_:1},8,["modelValue"])])}}});const pe=X(oe,[["__scopeId","data-v-1f9b0f84"]]);export{pe as default};
