import{d as R,u as B,r as m,K as V,p as F,L as G,b as h,M as q,o as D,c as I,s as K,q as j,w as s,f as a,e,g as _,y as v,i as p,x as Q,N as W,O as H,P as J,E as P,Q as X,R as Y,B as Z,l as tt,t as et,C as st,S as M,T as at,U as ot,V as nt,W as lt}from"./index-4256ff3d.js";import{u as rt,i as it,a as ct,b as dt,c as ut,d as _t,e as pt,E as ft}from"./index-850506a9.js";import{_ as vt}from"./_plugin-vue_export-helper-c27b6911.js";const mt={class:"dashboard"},gt={class:"stat-content"},yt={class:"stat-icon blue"},ht={class:"stat-info"},bt={class:"stat-value"},Dt={class:"stat-content"},Ct={class:"stat-icon green"},wt={class:"stat-info"},St={class:"stat-value"},kt={class:"stat-content"},At={class:"stat-icon orange"},$t={class:"stat-info"},xt={class:"stat-value"},It={class:"stat-content"},Pt={class:"stat-icon purple"},Lt={class:"stat-info"},Tt={class:"stat-value"},jt={class:"card-header"},Mt={class:"chart-container"},zt={class:"card-header"},Et={class:"activity-list"},Ot={class:"activity-content"},Ut={class:"activity-text"},Nt={class:"activity-time"},Rt={class:"card-header"},Bt={class:"quick-actions"},Vt=R({__name:"Dashboard",setup(Ft){rt([it,ct,dt,ut,_t,pt]);const C=B(),f=m({activeProjects:0,totalUsers:0,todayCheckins:0,totalLocations:0}),g=m([]),w=m(!1),S=m(!1),y=m(null),L=m(0),b=m([]),z=V(()=>{const n=b.value.map(o=>o.date),t=b.value.map(o=>o.attendanceCount),r=b.value.map(o=>o.abnormalCount);return{tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["出勤人数","异常记录"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:n},yAxis:{type:"value"},series:[{name:"出勤人数",type:"line",stack:"Total",smooth:!0,lineStyle:{color:"#409EFF"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}},data:t},{name:"异常记录",type:"line",stack:"Total",smooth:!0,lineStyle:{color:"#F56C6C"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(245, 108, 108, 0.3)"},{offset:1,color:"rgba(245, 108, 108, 0.1)"}]}},data:r}]}}),k=async()=>{try{w.value=!0,console.log("开始加载Dashboard统计数据...");const n=await J();if(console.log("实时数据API响应:",n),n.success){const t=n.data;console.log("实时数据详情:",t),f.value={activeProjects:t.activeProjects||0,totalUsers:t.totalUsers||0,todayCheckins:(t.todayCheckIns||0)+(t.todayCheckOuts||0),totalLocations:t.totalLocations||0},console.log("更新后的统计数据:",f.value)}else console.error("API返回失败:",n.message),f.value.totalUsers||P.error(n.message||"加载统计数据失败")}catch(n){console.error("Load stats error:",n),f.value.totalUsers||P.error("加载统计数据失败")}finally{w.value=!1}},A=async()=>{var n;try{S.value=!0,console.log("开始加载图表数据...");const t=new Date,r=new Date;r.setDate(t.getDate()-6);const o=await X({startDate:r.toISOString().split("T")[0],endDate:t.toISOString().split("T")[0],page:1,limit:1e3});if(console.log("图表数据API响应:",o),o.success){const c=((n=o.data)==null?void 0:n.records)||[];console.log("考勤记录:",c);const u={};for(let l=6;l>=0;l--){const i=new Date;i.setDate(i.getDate()-l);const x=i.toISOString().split("T")[0];u[x]={date:`${i.getMonth()+1}/${i.getDate()}`,attendanceCount:0,abnormalCount:0,userSet:new Set}}c.forEach(l=>{const i=new Date(l.date).toISOString().split("T")[0];console.log("处理记录日期:",i,"记录:",l),u[i]&&(u[i].userSet.add(l.userId),l.isAbnormal&&u[i].abnormalCount++)}),b.value=Object.keys(u).sort().map(l=>{const i=u[l];return{date:i.date,attendanceCount:i.userSet.size,abnormalCount:i.abnormalCount}}),console.log("处理后的图表数据:",b.value)}else console.error("图表数据API返回失败:",o.message)}catch(t){console.error("Load chart data error:",t)}finally{S.value=!1}},$=async()=>{var n;if(!C.isAdmin){console.log("用户权限不足，无法查看系统日志"),g.value=[{id:"no-permission",user:"系统提示",action:"您当前权限不足，无法查看系统日志记录",time:"权限限制"}];return}try{console.log("开始加载最近活动...");const t=await Y({page:1,limit:8});if(console.log("最近活动API响应:",t),t.success){const r=((n=t.data)==null?void 0:n.logs)||[];console.log("最近活动记录:",r),g.value=r.map(o=>({id:o.id,user:o.username||o.realName||"系统",action:E(o.action,o.module,o.description),time:O(o.createdAt)})).slice(0,4),console.log("处理后的最近活动:",g.value)}else console.error("最近活动API返回失败:",t.message),g.value=[{id:"error",user:"系统提示",action:"加载系统日志失败，请稍后重试",time:"加载失败"}]}catch(t){console.error("Load recent activities error:",t),g.value=[{id:"error",user:"系统提示",action:"网络错误，无法加载系统日志",time:"网络错误"}]}},E=(n,t,r)=>r||{login:"登录系统",logout:"退出系统",checkin:"打卡操作",create_user:"创建用户",update_user:"更新用户",delete_user:"删除用户",create_project:"创建项目",update_project:"更新项目",delete_project:"删除项目",create_location:"创建地点",update_location:"更新地点",delete_location:"删除地点",submit_application:"提交补卡申请",review_application:"审核补卡申请"}[n]||`${t}模块操作`,O=n=>{const t=new Date,r=new Date(n),o=t.getTime()-r.getTime(),c=Math.floor(o/(1e3*60)),u=Math.floor(o/(1e3*60*60)),l=Math.floor(o/(1e3*60*60*24));return c<1?"刚刚":c<60?`${c}分钟前`:u<24?`${u}小时前`:l<7?`${l}天前`:r.toLocaleDateString()},U=()=>{window.location.href="/reports"},N=async()=>{console.log("手动刷新所有数据..."),await Promise.all([k(),C.isAdmin?$():Promise.resolve(),A()]),T(),P.success("数据刷新完成")},T=()=>{y.value&&clearInterval(y.value),L.value=Date.now()+3e5,y.value=setInterval(()=>{k(),C.isAdmin&&$(),A(),L.value=Date.now()+3e5},3e5)};return F(()=>{k(),$(),A(),T()}),G(()=>{y.value&&(clearInterval(y.value),y.value=null)}),(n,t)=>{const r=h("el-icon"),o=h("el-card"),c=h("el-col"),u=h("el-row"),l=h("el-button"),i=h("el-avatar"),x=q("loading");return D(),I("div",mt,[K((D(),j(u,{gutter:20,class:"stats-row"},{default:s(()=>[a(c,{span:6},{default:s(()=>[a(o,{class:"stat-card"},{default:s(()=>[e("div",gt,[e("div",yt,[a(r,{size:"24"},{default:s(()=>[a(_(Z))]),_:1})]),e("div",ht,[e("div",bt,v(f.value.activeProjects),1),t[5]||(t[5]=e("div",{class:"stat-label"},"活跃工程",-1))])])]),_:1})]),_:1}),a(c,{span:6},{default:s(()=>[a(o,{class:"stat-card"},{default:s(()=>[e("div",Dt,[e("div",Ct,[a(r,{size:"24"},{default:s(()=>[a(_(tt))]),_:1})]),e("div",wt,[e("div",St,v(f.value.totalUsers),1),t[6]||(t[6]=e("div",{class:"stat-label"},"总用户数",-1))])])]),_:1})]),_:1}),a(c,{span:6},{default:s(()=>[a(o,{class:"stat-card"},{default:s(()=>[e("div",kt,[e("div",At,[a(r,{size:"24"},{default:s(()=>[a(_(et))]),_:1})]),e("div",$t,[e("div",xt,v(f.value.todayCheckins),1),t[7]||(t[7]=e("div",{class:"stat-label"},"今日打卡",-1))])])]),_:1})]),_:1}),a(c,{span:6},{default:s(()=>[a(o,{class:"stat-card"},{default:s(()=>[e("div",It,[e("div",Pt,[a(r,{size:"24"},{default:s(()=>[a(_(st))]),_:1})]),e("div",Lt,[e("div",Tt,v(f.value.totalLocations),1),t[8]||(t[8]=e("div",{class:"stat-label"},"打卡地点",-1))])])]),_:1})]),_:1})]),_:1})),[[x,w.value]]),a(u,{gutter:20,class:"content-row"},{default:s(()=>[a(c,{span:16},{default:s(()=>[a(o,null,{header:s(()=>[e("div",jt,[t[10]||(t[10]=e("span",null,"打卡趋势",-1)),a(l,{text:"",type:"primary",onClick:t[0]||(t[0]=d=>n.$router.push("/reports"))},{default:s(()=>t[9]||(t[9]=[p("查看详情")])),_:1,__:[9]})])]),default:s(()=>[e("div",Mt,[a(_(ft),{class:"chart",option:z.value,loading:S.value,autoresize:""},null,8,["option","loading"])])]),_:1})]),_:1}),a(c,{span:8},{default:s(()=>[a(o,null,{header:s(()=>[e("div",zt,[t[12]||(t[12]=e("span",null,"最近活动",-1)),_(C).isAdmin?(D(),j(l,{key:0,text:"",type:"primary",onClick:t[1]||(t[1]=d=>n.$router.push("/system-logs"))},{default:s(()=>t[11]||(t[11]=[p(" 查看更多 ")])),_:1,__:[11]})):Q("",!0)])]),default:s(()=>[e("div",Et,[(D(!0),I(W,null,H(g.value,d=>(D(),I("div",{key:d.id,class:M(["activity-item",{"activity-item-warning":d.id==="no-permission"||d.id==="error"}])},[a(i,{size:32,class:M(["activity-avatar",{"activity-avatar-warning":d.id==="no-permission"||d.id==="error"}])},{default:s(()=>[p(v(d.user[0]),1)]),_:2},1032,["class"]),e("div",Ot,[e("div",Ut,[e("strong",null,v(d.user),1),p(" "+v(d.action),1)]),e("div",Nt,v(d.time),1)])],2))),128))])]),_:1})]),_:1})]),_:1}),a(u,{gutter:20,class:"actions-row"},{default:s(()=>[a(c,{span:24},{default:s(()=>[a(o,null,{header:s(()=>[e("div",Rt,[t[14]||(t[14]=e("span",null,"快捷操作",-1)),a(l,{text:"",type:"primary",onClick:N,loading:w.value||S.value},{default:s(()=>t[13]||(t[13]=[p(" 刷新数据 ")])),_:1,__:[13]},8,["loading"])])]),default:s(()=>[e("div",Bt,[a(l,{type:"primary",icon:_(at),onClick:t[2]||(t[2]=d=>n.$router.push("/projects"))},{default:s(()=>t[15]||(t[15]=[p(" 新建工程 ")])),_:1,__:[15]},8,["icon"]),a(l,{type:"success",icon:_(ot),onClick:t[3]||(t[3]=d=>n.$router.push("/users"))},{default:s(()=>t[16]||(t[16]=[p(" 添加用户 ")])),_:1,__:[16]},8,["icon"]),a(l,{type:"warning",icon:_(nt),onClick:t[4]||(t[4]=d=>n.$router.push("/locations"))},{default:s(()=>t[17]||(t[17]=[p(" 管理地点 ")])),_:1,__:[17]},8,["icon"]),a(l,{type:"info",icon:_(lt),onClick:U},{default:s(()=>t[18]||(t[18]=[p(" 导出报表 ")])),_:1,__:[18]},8,["icon"])])]),_:1})]),_:1})]),_:1})])}}});const Qt=vt(Vt,[["__scopeId","data-v-4d5afab6"]]);export{Qt as default};
