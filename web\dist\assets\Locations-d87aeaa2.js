import{a9 as Le,aa as ke,d as Ue,r as h,a as Se,ab as N,ac as J,p as Ce,E as w,b as I,M as Ve,o as P,c as W,e as S,f as l,w as i,i as L,g as ee,s as xe,q as oe,y as G,N as te,O as ae,x as le,ad as Te,Y as Ee,ae as ne,z as Pe,af as Fe,ag as je,T as Be,ah as De}from"./index-4256ff3d.js";import{_ as Oe}from"./_plugin-vue_export-helper-c27b6911.js";var se={exports:{}};(function($,F){(function(C,D){$.exports=D()})(Le,function(){function C(a){var v=[];return a.AMapUI&&v.push(D(a.AMapUI)),a.Loca&&v.push(V(a.Loca)),Promise.all(v)}function D(a){return new Promise(function(v,p){var n=[];if(a.plugins)for(var r=0;r<a.plugins.length;r+=1)u.AMapUI.plugins.indexOf(a.plugins[r])==-1&&n.push(a.plugins[r]);if(t.AMapUI===c.failed)p("前次请求 AMapUI 失败");else if(t.AMapUI===c.notload){t.AMapUI=c.loading,u.AMapUI.version=a.version||u.AMapUI.version,r=u.AMapUI.version;var _=document.body||document.head,A=document.createElement("script");A.type="text/javascript",A.src="https://webapi.amap.com/ui/"+r+"/main.js",A.onerror=function(m){t.AMapUI=c.failed,p("请求 AMapUI 失败")},A.onload=function(){if(t.AMapUI=c.loaded,n.length)window.AMapUI.loadUI(n,function(){for(var m=0,M=n.length;m<M;m++){var T=n[m].split("/").slice(-1)[0];window.AMapUI[T]=arguments[m]}for(v();k.AMapUI.length;)k.AMapUI.splice(0,1)[0]()});else for(v();k.AMapUI.length;)k.AMapUI.splice(0,1)[0]()},_.appendChild(A)}else t.AMapUI===c.loaded?a.version&&a.version!==u.AMapUI.version?p("不允许多个版本 AMapUI 混用"):n.length?window.AMapUI.loadUI(n,function(){for(var m=0,M=n.length;m<M;m++){var T=n[m].split("/").slice(-1)[0];window.AMapUI[T]=arguments[m]}v()}):v():a.version&&a.version!==u.AMapUI.version?p("不允许多个版本 AMapUI 混用"):k.AMapUI.push(function(m){m?p(m):n.length?window.AMapUI.loadUI(n,function(){for(var M=0,T=n.length;M<T;M++){var z=n[M].split("/").slice(-1)[0];window.AMapUI[z]=arguments[M]}v()}):v()})})}function V(a){return new Promise(function(v,p){if(t.Loca===c.failed)p("前次请求 Loca 失败");else if(t.Loca===c.notload){t.Loca=c.loading,u.Loca.version=a.version||u.Loca.version;var n=u.Loca.version,r=u.AMap.version.startsWith("2"),_=n.startsWith("2");if(r&&!_||!r&&_)p("JSAPI 与 Loca 版本不对应！！");else{r=u.key,_=document.body||document.head;var A=document.createElement("script");A.type="text/javascript",A.src="https://webapi.amap.com/loca?v="+n+"&key="+r,A.onerror=function(m){t.Loca=c.failed,p("请求 AMapUI 失败")},A.onload=function(){for(t.Loca=c.loaded,v();k.Loca.length;)k.Loca.splice(0,1)[0]()},_.appendChild(A)}}else t.Loca===c.loaded?a.version&&a.version!==u.Loca.version?p("不允许多个版本 Loca 混用"):v():a.version&&a.version!==u.Loca.version?p("不允许多个版本 Loca 混用"):k.Loca.push(function(m){m?p(m):p()})})}if(!window)throw Error("AMap JSAPI can only be used in Browser.");var c;(function(a){a.notload="notload",a.loading="loading",a.loaded="loaded",a.failed="failed"})(c||(c={}));var u={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},t={AMap:c.notload,AMapUI:c.notload,Loca:c.notload},k={AMap:[],AMapUI:[],Loca:[]},x=[],j=function(a){typeof a=="function"&&(t.AMap===c.loaded?a(window.AMap):x.push(a))};return{load:function(a){return new Promise(function(v,p){if(t.AMap==c.failed)p("");else if(t.AMap==c.notload){var n=a.key,r=a.version,_=a.plugins;n?(window.AMap&&location.host!=="lbs.amap.com"&&p("禁止多种API加载方式混用"),u.key=n,u.AMap.version=r||u.AMap.version,u.AMap.plugins=_||u.AMap.plugins,t.AMap=c.loading,r=document.body||document.head,window.___onAPILoaded=function(m){if(delete window.___onAPILoaded,m)t.AMap=c.failed,p(m);else for(t.AMap=c.loaded,C(a).then(function(){v(window.AMap)}).catch(p);x.length;)x.splice(0,1)[0]()},_=document.createElement("script"),_.type="text/javascript",_.src="https://webapi.amap.com/maps?callback=___onAPILoaded&v="+u.AMap.version+"&key="+n+"&plugin="+u.AMap.plugins.join(","),_.onerror=function(m){t.AMap=c.failed,p(m)},r.appendChild(_)):p("请填写key")}else if(t.AMap==c.loaded)if(a.key&&a.key!==u.key)p("多个不一致的 key");else if(a.version&&a.version!==u.AMap.version)p("不允许多个版本 JSAPI 混用");else{if(n=[],a.plugins)for(r=0;r<a.plugins.length;r+=1)u.AMap.plugins.indexOf(a.plugins[r])==-1&&n.push(a.plugins[r]);n.length?window.AMap.plugin(n,function(){C(a).then(function(){v(window.AMap)}).catch(p)}):C(a).then(function(){v(window.AMap)}).catch(p)}else if(a.key&&a.key!==u.key)p("多个不一致的 key");else if(a.version&&a.version!==u.AMap.version)p("不允许多个版本 JSAPI 混用");else{var A=[];if(a.plugins)for(r=0;r<a.plugins.length;r+=1)u.AMap.plugins.indexOf(a.plugins[r])==-1&&A.push(a.plugins[r]);j(function(){A.length?window.AMap.plugin(A,function(){C(a).then(function(){v(window.AMap)}).catch(p)}):C(a).then(function(){v(window.AMap)}).catch(p)})}})},reset:function(){delete window.AMap,delete window.AMapUI,delete window.Loca,u={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},t={AMap:c.notload,AMapUI:c.notload,Loca:c.notload},k={AMap:[],AMapUI:[],Loca:[]}}}})})(se);var We=se.exports;const q=ke(We),Ge={webKey:"ce5fad118b8766d89a6602b6f1afe914",securityKey:"7142ad20f8f925bc9cb4fa0844f75c31"},Y=Ge,Q=()=>{const{webKey:$,securityKey:F}=Y;return!$||$==="your_web_api_key_here"?(console.warn("⚠️ 高德地图Web API Key未配置"),!1):!F||F==="your_security_key_here"?(console.warn("⚠️ 高德地图安全密钥未配置"),!1):!0},R=()=>{if(!Q())throw new Error("高德地图配置无效");return Y.webKey},$e=()=>{if(!Q())throw new Error("高德地图配置无效");return Y.securityKey},Ke=()=>{typeof window<"u"&&(window._AMapSecurityConfig={securityJsCode:$e()})},Ne={class:"locations-page"},qe={class:"page-header"},ze={class:"search-container"},He={key:0,class:"suggestions-dropdown"},Je=["onClick"],Re={class:"suggestion-name"},Ye={class:"suggestion-address"},Qe={key:1,class:"search-loading"},Xe=Ue({__name:"Locations",setup($){const F=h(!1),C=h([]),D=h([]),V=h(!1),c=h(!1),u=h(null),t=Se({name:"",address:"",latitude:"",longitude:"",radius:100,projectId:null,isMainLocation:!1,active:!0,workTimeStart:"",workTimeEnd:""}),k={name:[{required:!0,message:"请输入地点名称",trigger:"blur"}],projectId:[{required:!0,message:"请选择关联工程",trigger:"change"}],latitude:[{required:!0,message:"请输入纬度",trigger:"blur"}],longitude:[{required:!0,message:"请输入经度",trigger:"blur"}]},x=h([]),j=h(""),a=h([]),v=h(!1),p=h(!1),n=N(null),r=N(null),_=N(null),A=N(null),m=h(null),M=N(null);J(x,o=>{o&&o.length===2&&(t.workTimeStart=o[0],t.workTimeEnd=o[1])}),J(V,o=>{o&&setTimeout(()=>{X()},500)}),J(c,o=>{o&&setTimeout(()=>{pe()},300)});const T=async()=>{var o;F.value=!0;try{const e=await Te();e.success?C.value=((o=e.data)==null?void 0:o.locations)||[]:w.error(e.message||"加载地点列表失败")}catch(e){console.error("Load locations error:",e),w.error("加载地点列表失败")}finally{F.value=!1}},z=async()=>{var o;try{const e=await Ee();e.success?D.value=((o=e.data)==null?void 0:o.projects)||[]:w.error(e.message||"加载工程列表失败")}catch(e){console.error("Load projects error:",e),w.error("加载工程列表失败")}},re=o=>{c.value=!0,m.value=o},ie=o=>{var e,d;u.value=o,Object.assign(t,{...o,latitude:((e=o.latitude)==null?void 0:e.toString())||"",longitude:((d=o.longitude)==null?void 0:d.toString())||""}),o.workTimeStart&&o.workTimeEnd&&(x.value=[o.workTimeStart,o.workTimeEnd]),V.value=!0},ue=async o=>{try{const e=await ne(o.id,{active:o.active});e.success?w.success(`地点已${o.active?"启用":"禁用"}`):(o.active=!o.active,w.error(e.message||"状态更新失败"))}catch(e){o.active=!o.active,console.error("Toggle location status error:",e),w.error("状态更新失败")}},de=async o=>{try{await Pe.confirm(`确定要删除地点"${o.name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await Fe(o.id);e.success?(w.success("删除成功"),T()):w.error(e.message||"删除失败")}catch{}},ce=async()=>{try{const o=!!u.value,e=o?ne:je,d={...t,latitude:parseFloat(t.latitude)||0,longitude:parseFloat(t.longitude)||0,radius:parseInt(t.radius)||100,wifiSsids:j.value?j.value.split(",").map(b=>b.trim()).filter(b=>b):[]},g=o?[u.value.id,d]:[d],f=await e(...g);f.success?(w.success(o?"更新成功":"创建成功"),V.value=!1,T(),Object.assign(t,{name:"",address:"",latitude:"",longitude:"",radius:100,projectId:null,isMainLocation:!1,active:!0,workTimeStart:"09:00",workTimeEnd:"18:00"}),j.value="",x.value=[],u.value=null,n.value&&(n.value.destroy(),n.value=null),r.value&&(r.value=null)):w.error(f.message||(o?"更新失败":"创建失败"))}catch(o){console.error("Submit location error:",o),w.error(u.value?"更新失败":"创建失败")}},X=async()=>{console.log("开始初始化地图...");try{const o=document.getElementById("location-map");if(!o){console.error("地图容器未找到"),w.error("地图容器未找到");return}if(console.log("地图容器信息:",{width:o.offsetWidth,height:o.offsetHeight,display:getComputedStyle(o).display,visibility:getComputedStyle(o).visibility}),o.offsetWidth===0||o.offsetHeight===0){console.log("容器尺寸为0，等待500ms后重试..."),setTimeout(()=>X(),500);return}let e=M.value;if(e||(e=await q.load({key:R(),version:"2.0",plugins:["AMap.Scale","AMap.ToolBar","AMap.Geocoder","AMap.AutoComplete"]}),M.value=e),console.log("AMap加载成功"),n.value&&(n.value.destroy(),n.value=null),n.value=new e.Map(o,{zoom:15,center:[114.227773,23.158322],mapStyle:"amap://styles/normal",resizeEnable:!0}),console.log("地图实例创建成功",n.value),n.value.on("complete",()=>{console.log("地图加载完成"),setTimeout(()=>{n.value.getSize(),n.value.setFitView()},100),w.success("地图加载成功")}),n.value.on("error",d=>{console.error("地图加载错误:",d),w.error("地图加载失败")}),setTimeout(()=>{n.value&&(n.value.getSize(),console.log("强制刷新地图尺寸"))},200),n.value.addControl(new e.Scale),n.value.addControl(new e.ToolBar),t.latitude&&t.longitude){const d=[parseFloat(t.longitude),parseFloat(t.latitude)];n.value.setCenter(d),r.value=new e.Marker({position:d,draggable:!0}),n.value.add(r.value),r.value.on("dragend",g=>{const f=g.target.getPosition();t.longitude=f.lng.toString(),t.latitude=f.lat.toString(),K(f.lng,f.lat,e)})}n.value.on("click",d=>{const{lng:g,lat:f}=d.lnglat;t.longitude=g.toString(),t.latitude=f.toString(),r.value&&n.value.remove(r.value),r.value=new e.Marker({position:[g,f],draggable:!0}),n.value.add(r.value),r.value.on("dragend",b=>{const y=b.target.getPosition();t.longitude=y.lng.toString(),t.latitude=y.lat.toString(),K(y.lng,y.lat,e)}),K(g,f,e)})}catch(o){console.error("地图加载失败:",o),w.error("地图加载失败: "+o.message)}},pe=async()=>{if(m.value)try{const o=await q.load({key:R(),version:"2.0",plugins:["AMap.Scale","AMap.ToolBar"]});_.value&&(_.value.destroy(),_.value=null);const e=m.value,d=[parseFloat(e.longitude),parseFloat(e.latitude)];_.value=new o.Map("view-map",{zoom:16,center:d,mapStyle:"amap://styles/normal"}),_.value.addControl(new o.Scale),_.value.addControl(new o.ToolBar),A.value=new o.Marker({position:d,title:e.name}),_.value.add(A.value);const g=new o.Circle({center:d,radius:e.radius||100,strokeColor:"#409EFF",strokeWeight:2,fillColor:"#409EFF",fillOpacity:.2});_.value.add(g)}catch(o){console.error("查看地图加载失败:",o),w.error("地图加载失败")}},K=(o,e,d)=>{if(!d)return;new d.Geocoder().getAddress([o,e],(f,b)=>{f==="complete"&&b.regeocode&&(t.address=b.regeocode.formattedAddress)})},fe=async o=>{if(!o||o.length<2){a.value=[],v.value=!1;return}p.value=!0;try{const e=await me(o);a.value=e,v.value=e.length>0}catch(e){console.error("搜索建议失败:",e)}finally{p.value=!1}},ge=()=>{setTimeout(()=>{v.value=!1},200)},ve=async o=>{t.name=o.name,t.address=o.address,v.value=!1;try{const e=parseFloat(o.lng),d=parseFloat(o.lat);t.longitude=e.toString(),t.latitude=d.toString(),n.value&&(n.value.setCenter([e,d]),r.value&&n.value.remove(r.value),r.value=new M.value.Marker({position:[e,d],draggable:!0}),n.value.add(r.value),r.value.on("dragend",g=>{const f=g.target.getPosition();t.longitude=f.lng.toString(),t.latitude=f.lat.toString(),K(f.lng,f.lat,M.value)})),w.success("位置信息已自动填入")}catch(e){console.error("获取位置信息失败:",e),w.error("获取位置信息失败")}},me=async o=>{try{return M.value?new Promise(e=>{new M.value.AutoComplete({city:"全国",citylimit:!1}).search(o,(g,f)=>{if(g==="complete"&&f.tips){const b=f.tips.filter(y=>y.location).slice(0,8).map(y=>({name:y.name,address:y.district+y.address,lng:y.location.lng,lat:y.location.lat,adcode:y.adcode}));e(b)}else console.error("高德搜索失败:",g,f),e([])})}):(console.error("高德地图未初始化"),[])}catch(e){return console.error("高德搜索建议失败:",e),[]}},we=async()=>{if(!t.address){w.warning("请输入地址");return}try{let o=M.value;o||(o=await q.load({key:"ce5fad118b8766d89a6602b6f1afe914",version:"2.0",plugins:["AMap.Geocoder"]}),M.value=o),new o.Geocoder().getLocation(t.address,(d,g)=>{if(d==="complete"&&g.geocodes&&g.geocodes.length>0){const f=g.geocodes[0].location;t.longitude=f.lng.toString(),t.latitude=f.lat.toString(),n.value&&(n.value.setCenter([f.lng,f.lat]),r.value&&n.value.remove(r.value),r.value=new o.Marker({position:[f.lng,f.lat],draggable:!0}),n.value.add(r.value),r.value.on("dragend",b=>{const y=b.target.getPosition();t.longitude=y.lng.toString(),t.latitude=y.lat.toString(),K(y.lng,y.lat,o)})),w.success("地址定位成功")}else w.error("地址搜索失败，请检查地址是否正确")})}catch(o){console.error("地址搜索失败:",o),w.error("地址搜索失败")}};return Ce(async()=>{T(),z(),Ke();try{if(!Q())throw new Error("高德地图配置无效");const o=await q.load({key:R(),version:"2.0",plugins:["AMap.Scale","AMap.ToolBar","AMap.Geocoder","AMap.AutoComplete"]});M.value=o,console.log("高德地图API预加载成功")}catch(o){console.error("高德地图API预加载失败:",o),w.error("地图服务加载失败，地图功能可能无法使用")}}),(o,e)=>{const d=I("el-button"),g=I("el-table-column"),f=I("el-tag"),b=I("el-switch"),y=I("el-table"),_e=I("el-card"),O=I("el-input"),ye=I("el-icon"),U=I("el-form-item"),E=I("el-col"),Ae=I("el-option"),Me=I("el-select"),H=I("el-row"),be=I("el-time-picker"),Ie=I("el-form"),Z=I("el-dialog"),he=Ve("loading");return P(),W("div",Ne,[S("div",qe,[e[16]||(e[16]=S("h2",null,"地点管理",-1)),l(d,{type:"primary",icon:ee(Be),onClick:e[0]||(e[0]=s=>V.value=!0)},{default:i(()=>e[15]||(e[15]=[L(" 添加地点 ")])),_:1,__:[15]},8,["icon"])]),l(_e,null,{default:i(()=>[xe((P(),oe(y,{data:C.value,style:{width:"100%"}},{default:i(()=>[l(g,{prop:"name",label:"地点名称","min-width":"150"}),l(g,{prop:"address",label:"地址","min-width":"200"}),l(g,{prop:"projectName",label:"关联工程",width:"150"}),l(g,{label:"坐标",width:"200"},{default:i(({row:s})=>[L(G(s.latitude)+", "+G(s.longitude),1)]),_:1}),l(g,{prop:"radius",label:"打卡半径",width:"100"},{default:i(({row:s})=>[L(G(s.radius)+"米 ",1)]),_:1}),l(g,{prop:"isMainLocation",label:"主要地点",width:"100"},{default:i(({row:s})=>[l(f,{type:s.isMainLocation?"success":"info"},{default:i(()=>[L(G(s.isMainLocation?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),l(g,{prop:"active",label:"状态",width:"100"},{default:i(({row:s})=>[l(b,{modelValue:s.active,"onUpdate:modelValue":B=>s.active=B,onChange:B=>ue(s),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(g,{label:"操作",width:"200",fixed:"right"},{default:i(({row:s})=>[l(d,{text:"",type:"primary",onClick:B=>re(s)},{default:i(()=>e[17]||(e[17]=[L(" 地图 ")])),_:2,__:[17]},1032,["onClick"]),l(d,{text:"",type:"primary",onClick:B=>ie(s)},{default:i(()=>e[18]||(e[18]=[L(" 编辑 ")])),_:2,__:[18]},1032,["onClick"]),l(d,{text:"",type:"danger",onClick:B=>de(s)},{default:i(()=>e[19]||(e[19]=[L(" 删除 ")])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[he,F.value]])]),_:1}),l(Z,{modelValue:V.value,"onUpdate:modelValue":e[13]||(e[13]=s=>V.value=s),title:u.value?"编辑地点":"添加地点",width:"800px"},{footer:i(()=>[l(d,{onClick:e[12]||(e[12]=s=>V.value=!1)},{default:i(()=>e[25]||(e[25]=[L("取消")])),_:1,__:[25]}),l(d,{type:"primary",onClick:ce},{default:i(()=>e[26]||(e[26]=[L("确定")])),_:1,__:[26]})]),default:i(()=>[l(Ie,{ref:"locationFormRef",model:t,rules:k,"label-width":"100px"},{default:i(()=>[l(H,{gutter:20},{default:i(()=>[l(E,{span:12},{default:i(()=>[l(U,{label:"地点名称",prop:"name"},{default:i(()=>[S("div",ze,[l(O,{modelValue:t.name,"onUpdate:modelValue":e[1]||(e[1]=s=>t.name=s),placeholder:"请输入地点名称，支持智能搜索",onInput:fe,onFocus:e[2]||(e[2]=s=>v.value=!0),onBlur:ge},null,8,["modelValue"]),v.value&&a.value.length>0?(P(),W("div",He,[(P(!0),W(te,null,ae(a.value,(s,B)=>(P(),W("div",{key:B,class:"suggestion-item",onClick:Ze=>ve(s)},[S("div",Re,G(s.name),1),S("div",Ye,G(s.address),1)],8,Je))),128))])):le("",!0),p.value?(P(),W("div",Qe,[l(ye,{class:"is-loading"},{default:i(()=>[l(ee(De))]),_:1}),e[20]||(e[20]=L(" 搜索中... "))])):le("",!0)])]),_:1})]),_:1}),l(E,{span:12},{default:i(()=>[l(U,{label:"关联工程",prop:"projectId"},{default:i(()=>[l(Me,{modelValue:t.projectId,"onUpdate:modelValue":e[3]||(e[3]=s=>t.projectId=s),placeholder:"请选择工程"},{default:i(()=>[(P(!0),W(te,null,ae(D.value,s=>(P(),oe(Ae,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(U,{label:"详细地址"},{default:i(()=>[l(O,{modelValue:t.address,"onUpdate:modelValue":e[4]||(e[4]=s=>t.address=s),placeholder:"请输入详细地址，可点击搜索按钮定位"},{append:i(()=>[l(d,{onClick:we,disabled:!t.address},{default:i(()=>e[21]||(e[21]=[L(" 搜索定位 ")])),_:1,__:[21]},8,["disabled"])]),_:1},8,["modelValue"])]),_:1}),l(H,{gutter:20},{default:i(()=>[l(E,{span:8},{default:i(()=>[l(U,{label:"纬度",prop:"latitude"},{default:i(()=>[l(O,{modelValue:t.latitude,"onUpdate:modelValue":e[5]||(e[5]=s=>t.latitude=s),placeholder:"纬度",type:"number",step:"0.000001"},null,8,["modelValue"])]),_:1})]),_:1}),l(E,{span:8},{default:i(()=>[l(U,{label:"经度",prop:"longitude"},{default:i(()=>[l(O,{modelValue:t.longitude,"onUpdate:modelValue":e[6]||(e[6]=s=>t.longitude=s),placeholder:"经度",type:"number",step:"0.000001"},null,8,["modelValue"])]),_:1})]),_:1}),l(E,{span:8},{default:i(()=>[l(U,{label:"打卡半径"},{default:i(()=>[l(O,{modelValue:t.radius,"onUpdate:modelValue":e[7]||(e[7]=s=>t.radius=s),placeholder:"米",type:"number"},{append:i(()=>e[22]||(e[22]=[L("米")])),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(H,{gutter:20},{default:i(()=>[l(E,{span:12},{default:i(()=>[l(U,{label:"工作时间"},{default:i(()=>[l(be,{modelValue:x.value,"onUpdate:modelValue":e[8]||(e[8]=s=>x.value=s),"is-range":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue"])]),_:1})]),_:1}),l(E,{span:6},{default:i(()=>[l(U,{label:"主要地点"},{default:i(()=>[l(b,{modelValue:t.isMainLocation,"onUpdate:modelValue":e[9]||(e[9]=s=>t.isMainLocation=s)},null,8,["modelValue"])]),_:1})]),_:1}),l(E,{span:6},{default:i(()=>[l(U,{label:"启用状态"},{default:i(()=>[l(b,{modelValue:t.active,"onUpdate:modelValue":e[10]||(e[10]=s=>t.active=s)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(U,{label:"WiFi SSID"},{default:i(()=>[l(O,{modelValue:j.value,"onUpdate:modelValue":e[11]||(e[11]=s=>j.value=s),placeholder:"多个SSID用逗号分隔",type:"textarea",rows:2},null,8,["modelValue"]),e[23]||(e[23]=S("div",{class:"form-tip"},"可选：设置后只有连接指定WiFi才能打卡",-1))]),_:1,__:[23]}),l(U,{label:"地图选点"},{default:i(()=>e[24]||(e[24]=[S("div",{class:"map-container"},[S("div",{id:"location-map"})],-1),S("div",{class:"map-tips"}," 点击地图选择坐标 • 可拖拽标记调整位置 • 支持地址搜索快速定位 ",-1)])),_:1,__:[24]})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),l(Z,{modelValue:c.value,"onUpdate:modelValue":e[14]||(e[14]=s=>c.value=s),title:"地点位置",width:"800px"},{default:i(()=>e[27]||(e[27]=[S("div",{id:"view-map",style:{width:"100%",height:"400px"}},null,-1)])),_:1,__:[27]},8,["modelValue"])])}}});const to=Oe(Xe,[["__scopeId","data-v-829f745e"]]);export{to as default};
