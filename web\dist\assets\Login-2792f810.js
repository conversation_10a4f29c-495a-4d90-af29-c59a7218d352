import{d as y,u as V,r as k,a as C,b as t,o as z,c as F,e as s,f as o,w as l,g as a,h as A,i as E,j as L,k as R,E as _,t as B,l as K,m as N,n as P}from"./index-fea52ff4.js";import{_ as S}from"./_plugin-vue_export-helper-c27b6911.js";const U={class:"login-container"},q={class:"login-box"},M={class:"login-header"},j={class:"logo"},I={class:"app-download"},T={class:"download-content"},D=y({__name:"Login",setup(G){const g=R(),p=V(),d=k(),n=C({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]},i=async()=>{d.value&&await d.value.validate(async r=>{if(r){const e=await p.login(n.username,n.password);e.success?(_.success("登录成功"),g.push("/dashboard")):_.error(e.message||"登录失败")}})},v=()=>{const r="download.html";window.open(r,"_blank"),_.success("已经打开客户端下载页面")};return(r,e)=>{const m=t("el-icon"),f=t("el-input"),u=t("el-form-item"),h=t("el-button"),x=t("el-form"),b=t("el-alert");return z(),F("div",U,[s("div",q,[s("div",M,[s("div",j,[o(m,{size:"40",color:"#409EFF"},{default:l(()=>[o(a(B))]),_:1})]),e[2]||(e[2]=s("h1",null,"考勤打卡系统",-1)),e[3]||(e[3]=s("p",null,"管理后台",-1))]),o(x,{ref_key:"loginFormRef",ref:d,model:n,rules:w,class:"login-form",onSubmit:L(i,["prevent"])},{default:l(()=>[o(u,{prop:"username"},{default:l(()=>[o(f,{modelValue:n.username,"onUpdate:modelValue":e[0]||(e[0]=c=>n.username=c),placeholder:"请输入用户名",size:"large","prefix-icon":a(K)},null,8,["modelValue","prefix-icon"])]),_:1}),o(u,{prop:"password"},{default:l(()=>[o(f,{modelValue:n.password,"onUpdate:modelValue":e[1]||(e[1]=c=>n.password=c),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":a(N),"show-password":"",onKeyup:A(i,["enter"])},null,8,["modelValue","prefix-icon"])]),_:1}),o(u,null,{default:l(()=>[o(h,{type:"primary",size:"large",loading:a(p).loading,onClick:i,class:"login-button"},{default:l(()=>e[4]||(e[4]=[E(" 登录 ")])),_:1,__:[4]},8,["loading"])]),_:1})]),_:1},8,["model"]),s("div",I,[o(b,{title:"移动端应用",type:"success",closable:!1,"show-icon":""},{default:l(()=>[s("div",T,[s("div",{class:"download-item",onClick:v},[o(m,{size:"20",color:"#67C23A"},{default:l(()=>[o(a(P))]),_:1}),e[5]||(e[5]=s("span",{class:"download-text"},"点击下载APP",-1))])])]),_:1})])])])}}});const O=S(D,[["__scopeId","data-v-e531fd39"]]);export{O as default};
