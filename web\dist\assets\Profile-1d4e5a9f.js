import{d as O,u as S,r as m,a as V,K as z,p as K,b as u,o as P,c as x,f as a,w as o,e as F,q as L,i as c,N as Q,aQ as $,E as p,aR as G}from"./index-4256ff3d.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";const J={class:"profile-container"},W=O({__name:"Profile",setup(X){const y=S(),U=m("basic"),i=m(!1),g=m(!1),v=m(!1),w=m(),_=m(),s=V({username:"",realName:"",role:"",department:"",phone:"",email:"",createdAt:""}),b=V({phone:"",email:""}),n=V({oldPassword:"",newPassword:"",confirmPassword:""}),R=z(()=>({admin:"管理员",manager:"经理",employee:"员工"})[s.role]||"未知"),B={phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]},E={oldPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(l,e,t)=>{e!==n.newPassword?t(new Error("两次输入的密码不一致")):t()},trigger:"blur"}]},A=l=>l?new Date(l).toLocaleString("zh-CN"):"",k=()=>{const l=y.user;l&&(Object.assign(s,{username:l.username||"",realName:l.realName||"",role:l.role||"",department:l.department||"",phone:l.phone||"",email:l.email||"",createdAt:l.createdAt||""}),b.phone=l.phone||"",b.email=l.email||"")},M=()=>{i.value=!0},q=()=>{var l;i.value=!1,s.phone=b.phone,s.email=b.email,(l=w.value)==null||l.clearValidate()},I=async()=>{if(w.value)try{await w.value.validate(),g.value=!0;const l={phone:s.phone.trim()||null,email:s.email.trim()||null},e=await $(l);e.success?(p.success("个人信息更新成功"),i.value=!1,await y.refreshUserInfo(),k()):p.error(e.message||"更新失败")}catch(l){console.error("Update profile error:",l),p.error("更新失败")}finally{g.value=!1}},T=async()=>{if(_.value)try{await _.value.validate(),v.value=!0;const l=await G({oldPassword:n.oldPassword,newPassword:n.newPassword});l.success?(p.success("密码修改成功"),C()):p.error(l.message||"密码修改失败")}catch(l){console.error("Change password error:",l),p.error("密码修改失败")}finally{v.value=!1}},C=()=>{var l;Object.assign(n,{oldPassword:"",newPassword:"",confirmPassword:""}),(l=_.value)==null||l.clearValidate()};return K(()=>{k()}),(l,e)=>{const t=u("el-input"),d=u("el-form-item"),f=u("el-button"),N=u("el-form"),h=u("el-tab-pane"),D=u("el-tabs"),j=u("el-card");return P(),x("div",J,[a(j,{class:"profile-card"},{header:o(()=>e[9]||(e[9]=[F("div",{class:"card-header"},[F("span",null,"个人信息")],-1)])),default:o(()=>[a(D,{modelValue:U.value,"onUpdate:modelValue":e[8]||(e[8]=r=>U.value=r),class:"profile-tabs"},{default:o(()=>[a(h,{label:"基本信息",name:"basic"},{default:o(()=>[a(N,{ref_key:"basicFormRef",ref:w,model:s,rules:B,"label-width":"100px",class:"profile-form"},{default:o(()=>[a(d,{label:"用户名"},{default:o(()=>[a(t,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=r=>s.username=r),disabled:""},null,8,["modelValue"])]),_:1}),a(d,{label:"姓名"},{default:o(()=>[a(t,{modelValue:s.realName,"onUpdate:modelValue":e[1]||(e[1]=r=>s.realName=r),disabled:""},null,8,["modelValue"])]),_:1}),a(d,{label:"角色"},{default:o(()=>[a(t,{value:R.value,disabled:""},null,8,["value"])]),_:1}),a(d,{label:"部门"},{default:o(()=>[a(t,{modelValue:s.department,"onUpdate:modelValue":e[2]||(e[2]=r=>s.department=r),disabled:""},null,8,["modelValue"])]),_:1}),a(d,{label:"手机号",prop:"phone"},{default:o(()=>[a(t,{modelValue:s.phone,"onUpdate:modelValue":e[3]||(e[3]=r=>s.phone=r),placeholder:"请输入手机号",disabled:!i.value},null,8,["modelValue","disabled"])]),_:1}),a(d,{label:"邮箱",prop:"email"},{default:o(()=>[a(t,{modelValue:s.email,"onUpdate:modelValue":e[4]||(e[4]=r=>s.email=r),placeholder:"请输入邮箱",disabled:!i.value},null,8,["modelValue","disabled"])]),_:1}),a(d,{label:"创建时间"},{default:o(()=>[a(t,{value:A(s.createdAt),disabled:""},null,8,["value"])]),_:1}),a(d,null,{default:o(()=>[i.value?(P(),x(Q,{key:1},[a(f,{type:"primary",onClick:I,loading:g.value},{default:o(()=>e[11]||(e[11]=[c(" 保存 ")])),_:1,__:[11]},8,["loading"]),a(f,{onClick:q},{default:o(()=>e[12]||(e[12]=[c("取消")])),_:1,__:[12]})],64)):(P(),L(f,{key:0,type:"primary",onClick:M},{default:o(()=>e[10]||(e[10]=[c(" 编辑信息 ")])),_:1,__:[10]}))]),_:1})]),_:1},8,["model"])]),_:1}),a(h,{label:"修改密码",name:"password"},{default:o(()=>[a(N,{ref_key:"passwordFormRef",ref:_,model:n,rules:E,"label-width":"100px",class:"profile-form"},{default:o(()=>[a(d,{label:"当前密码",prop:"oldPassword"},{default:o(()=>[a(t,{modelValue:n.oldPassword,"onUpdate:modelValue":e[5]||(e[5]=r=>n.oldPassword=r),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1}),a(d,{label:"新密码",prop:"newPassword"},{default:o(()=>[a(t,{modelValue:n.newPassword,"onUpdate:modelValue":e[6]||(e[6]=r=>n.newPassword=r),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),a(d,{label:"确认密码",prop:"confirmPassword"},{default:o(()=>[a(t,{modelValue:n.confirmPassword,"onUpdate:modelValue":e[7]||(e[7]=r=>n.confirmPassword=r),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),a(d,null,{default:o(()=>[a(f,{type:"primary",onClick:T,loading:v.value},{default:o(()=>e[13]||(e[13]=[c(" 修改密码 ")])),_:1,__:[13]},8,["loading"]),a(f,{onClick:C},{default:o(()=>e[14]||(e[14]=[c("重置")])),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}});const ee=H(W,[["__scopeId","data-v-50e90515"]]);export{ee as default};
