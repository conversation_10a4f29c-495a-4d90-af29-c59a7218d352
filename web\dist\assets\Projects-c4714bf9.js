import{d as _e,r as b,a as M,p as De,b as v,M as Ve,o as D,c as T,e as j,f as a,w as s,i,g as we,h as je,s as H,q as k,y as U,N as J,O as Q,x as W,X as xe,E as u,Y as Ce,z as ee,Z as ke,_ as Ue,$ as Pe,a0 as Re,a1 as Ne,a2 as he,a3 as Ae,a4 as $e,T as Ie}from"./index-4256ff3d.js";import{_ as ze}from"./_plugin-vue_export-helper-c27b6911.js";const Me={class:"projects-page"},Be={class:"page-header"},Fe={class:"pagination"},Oe={class:"assignment-content"},Se={style:{display:"flex","justify-content":"space-between","align-items":"center"}},Te=_e({__name:"Projects",setup(Ee){const B=b(!1),E=b([]),P=b([]),x=b(!1),w=b(null),F=b(!1),f=b(null),O=b([]),L=b([]),V=b(!1),g=b(null),_=M({name:"",status:""}),y=M({page:1,size:20,total:0}),n=M({name:"",clientName:"",description:"",projectManager:null,startDate:"",endDate:"",status:"planning",budget:""}),r=M({userId:null,role:"",startDate:"",endDate:"",hourlyRate:""}),ae={name:[{required:!0,message:"请输入工程名称",trigger:"blur"}],clientName:[{required:!0,message:"请输入客户名称",trigger:"blur"}]},te=l=>({planning:"info",active:"success",paused:"warning",completed:"success",cancelled:"danger"})[l]||"info",le=l=>({planning:"规划中",active:"进行中",paused:"暂停",completed:"已完成",cancelled:"已取消"})[l]||"未知",N=()=>{y.page=1,C()},se=()=>{_.name="",_.status="",N()},oe=l=>{y.size=l,C()},ne=l=>{y.page=l,C()},re=async()=>{var l;try{console.log("🔄 开始加载项目经理列表...");const e=await xe();console.log("👥 项目经理API响应:",e),e.success?(P.value=((l=e.data)==null?void 0:l.managers)||[],console.log("✅ 项目经理列表加载成功:",P.value)):(P.value=[],console.warn("⚠️ 加载项目经理列表失败:",e.message),u.error(e.message||"加载项目经理列表失败"))}catch(e){console.error("❌ 加载项目经理列表错误:",e),P.value=[],u.error("加载项目经理列表失败")}},C=async()=>{var l,e,o;B.value=!0;try{const p={page:y.page,limit:y.size,search:_.name,status:_.status},d=await Ce(p);d.success?(E.value=((l=d.data)==null?void 0:l.projects)||[],y.total=((o=(e=d.data)==null?void 0:e.pagination)==null?void 0:o.total)||0):u.error(d.message||"加载工程列表失败")}catch(p){console.error("Load projects error:",p),u.error("加载工程列表失败")}finally{B.value=!1}},ue=l=>{u.info("查看工程详情功能开发中...")},de=()=>{Object.assign(n,{name:"",clientName:"",description:"",projectManager:null,startDate:"",endDate:"",status:"planning",budget:""}),w.value=null,x.value=!0},ie=l=>{w.value=l,Object.assign(n,l),x.value=!0},pe=async l=>{try{await ee.confirm(`确定要删除工程"${l.name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await ke(l.id);e.success?(u.success("删除成功"),C()):u.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("Delete project error:",e),u.error("删除失败"))}},ce=async()=>{try{const l=!!w.value,e=l?Ue:Pe,o={name:n.name,description:n.description||void 0,clientName:n.clientName||void 0,projectManager:n.projectManager||void 0,startDate:n.startDate||void 0,endDate:n.endDate||void 0,status:n.status||void 0,budget:n.budget?parseFloat(n.budget):void 0};Object.keys(o).forEach(c=>{o[c]===void 0&&delete o[c]}),console.log("提交数据:",o);const p=l?[w.value.id,o]:[o],d=await e(...p);d.success?(u.success(l?"更新成功":"创建成功"),x.value=!1,C(),Object.assign(n,{name:"",clientName:"",description:"",projectManager:null,startDate:"",endDate:"",status:"planning",budget:""}),w.value=null):u.error(d.message||(l?"更新失败":"创建失败"))}catch(l){console.error("Submit project error:",l),u.error(w.value?"更新失败":"创建失败")}},me=async l=>{f.value=l,g.value=null,Object.assign(r,{userId:null,role:"",startDate:l.startDate||"",endDate:l.endDate||"",hourlyRate:""}),F.value=!0,await h(),await A()},h=async()=>{if(f.value){V.value=!0;try{const l=await Re(f.value.id);l.success?O.value=l.data||[]:u.error(l.message||"加载人员分配失败")}catch(l){console.error("Load assignments error:",l),u.error("加载人员分配失败")}finally{V.value=!1}}},A=async()=>{var l;try{const e=await Ne();if(e.success){const o=O.value.map(p=>p.userId);L.value=(((l=e.data)==null?void 0:l.users)||[]).filter(p=>!o.includes(p.id))}else u.error(e.message||"加载用户列表失败")}catch(e){console.error("Load users error:",e),u.error("加载用户列表失败")}},ge=async()=>{var l,e;if(!r.userId){u.warning("请选择用户");return}V.value=!0;try{const o=await he(f.value.id,{userId:r.userId,role:r.role,startDate:r.startDate,endDate:r.endDate,hourlyRate:r.hourlyRate?parseFloat(r.hourlyRate):null});o.success?(u.success("添加人员成功"),Object.assign(r,{userId:null,role:"",startDate:((l=f.value)==null?void 0:l.startDate)||"",endDate:((e=f.value)==null?void 0:e.endDate)||"",hourlyRate:""}),await h(),await A()):u.error(o.message||"添加人员失败")}catch(o){console.error("Add assignment error:",o),u.error("添加人员失败")}finally{V.value=!1}},ve=l=>{g.value=l,Object.assign(r,{userId:l.userId,role:l.role||"",startDate:l.startDate||"",endDate:l.endDate||"",hourlyRate:l.hourlyRate||""})},G=()=>{var l,e;g.value=null,Object.assign(r,{userId:null,role:"",startDate:((l=f.value)==null?void 0:l.startDate)||"",endDate:((e=f.value)==null?void 0:e.endDate)||"",hourlyRate:""})},fe=async()=>{if(!g.value){u.warning("编辑信息丢失，请重新选择");return}V.value=!0;try{const l=await Ae(f.value.id,g.value.id,{role:r.role,startDate:r.startDate,endDate:r.endDate,hourlyRate:r.hourlyRate?parseFloat(r.hourlyRate):null});l.success?(u.success("更新人员信息成功"),G(),await h(),await A()):u.error(l.message||"更新人员信息失败")}catch(l){console.error("Update assignment error:",l),u.error("更新人员信息失败")}finally{V.value=!1}},ye=async l=>{try{await ee.confirm(`确定要移除"${l.userName}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await $e(f.value.id,l.id);e.success?(u.success("移除成功"),await h(),await A()):u.error(e.message||"移除失败")}catch(e){e!=="cancel"&&(console.error("Remove assignment error:",e),u.error("移除失败"))}};return De(()=>{re(),C()}),(l,e)=>{var Z;const o=v("el-button"),p=v("el-input"),d=v("el-form-item"),c=v("el-option"),$=v("el-select"),S=v("el-form"),I=v("el-card"),m=v("el-table-column"),q=v("el-tag"),K=v("el-table"),be=v("el-pagination"),z=v("el-date-picker"),X=v("el-dialog"),Y=Ve("loading");return D(),T("div",Me,[j("div",Be,[e[22]||(e[22]=j("h2",null,"工程管理",-1)),a(o,{type:"primary",icon:we(Ie),onClick:de},{default:s(()=>e[21]||(e[21]=[i(" 新建工程 ")])),_:1,__:[21]},8,["icon"])]),a(I,{class:"search-card"},{default:s(()=>[a(S,{model:_,inline:""},{default:s(()=>[a(d,{label:"工程名称"},{default:s(()=>[a(p,{modelValue:_.name,"onUpdate:modelValue":e[0]||(e[0]=t=>_.name=t),placeholder:"请输入工程名称",clearable:"",onClear:N,onKeyup:je(N,["enter"])},null,8,["modelValue"])]),_:1}),a(d,{label:"状态"},{default:s(()=>[a($,{modelValue:_.status,"onUpdate:modelValue":e[1]||(e[1]=t=>_.status=t),placeholder:"请选择状态",clearable:""},{default:s(()=>[a(c,{label:"规划中",value:"planning"}),a(c,{label:"进行中",value:"active"}),a(c,{label:"暂停",value:"paused"}),a(c,{label:"已完成",value:"completed"}),a(c,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),a(d,null,{default:s(()=>[a(o,{type:"primary",onClick:N},{default:s(()=>e[23]||(e[23]=[i("搜索")])),_:1,__:[23]}),a(o,{onClick:se},{default:s(()=>e[24]||(e[24]=[i("重置")])),_:1,__:[24]})]),_:1})]),_:1},8,["model"])]),_:1}),a(I,null,{default:s(()=>[H((D(),k(K,{data:E.value,style:{width:"100%"}},{default:s(()=>[a(m,{prop:"name",label:"工程名称","min-width":"200"}),a(m,{prop:"clientName",label:"客户名称","min-width":"150"}),a(m,{prop:"managerName",label:"项目经理","min-width":"120"}),a(m,{prop:"status",label:"状态",width:"100"},{default:s(({row:t})=>[a(q,{type:te(t.status)},{default:s(()=>[i(U(le(t.status)),1)]),_:2},1032,["type"])]),_:1}),a(m,{prop:"startDate",label:"开始日期",width:"120"}),a(m,{prop:"endDate",label:"结束日期",width:"120"}),a(m,{prop:"budget",label:"预算",width:"120"},{default:s(({row:t})=>[i(U(t.budget?`¥${(t.budget/1e4).toFixed(1)}万`:"-"),1)]),_:1}),a(m,{label:"操作",width:"280",fixed:"right"},{default:s(({row:t})=>[a(o,{text:"",type:"primary",onClick:R=>ue(t)},{default:s(()=>e[25]||(e[25]=[i(" 查看 ")])),_:2,__:[25]},1032,["onClick"]),a(o,{text:"",type:"success",onClick:R=>me(t)},{default:s(()=>e[26]||(e[26]=[i(" 人员管理 ")])),_:2,__:[26]},1032,["onClick"]),a(o,{text:"",type:"primary",onClick:R=>ie(t)},{default:s(()=>e[27]||(e[27]=[i(" 编辑 ")])),_:2,__:[27]},1032,["onClick"]),a(o,{text:"",type:"danger",onClick:R=>pe(t)},{default:s(()=>e[28]||(e[28]=[i(" 删除 ")])),_:2,__:[28]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Y,B.value]]),j("div",Fe,[a(be,{"current-page":y.page,"onUpdate:currentPage":e[2]||(e[2]=t=>y.page=t),"page-size":y.size,"onUpdate:pageSize":e[3]||(e[3]=t=>y.size=t),"page-sizes":[10,20,50,100],total:y.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:oe,onCurrentChange:ne},null,8,["current-page","page-size","total"])])]),_:1}),a(X,{modelValue:x.value,"onUpdate:modelValue":e[13]||(e[13]=t=>x.value=t),title:w.value?"编辑工程":"新建工程",width:"600px"},{footer:s(()=>[a(o,{onClick:e[12]||(e[12]=t=>x.value=!1)},{default:s(()=>e[30]||(e[30]=[i("取消")])),_:1,__:[30]}),a(o,{type:"primary",onClick:ce},{default:s(()=>e[31]||(e[31]=[i("确定")])),_:1,__:[31]})]),default:s(()=>[a(S,{ref:"projectFormRef",model:n,rules:ae,"label-width":"100px"},{default:s(()=>[a(d,{label:"工程名称",prop:"name"},{default:s(()=>[a(p,{modelValue:n.name,"onUpdate:modelValue":e[4]||(e[4]=t=>n.name=t),placeholder:"请输入工程名称"},null,8,["modelValue"])]),_:1}),a(d,{label:"客户名称",prop:"clientName"},{default:s(()=>[a(p,{modelValue:n.clientName,"onUpdate:modelValue":e[5]||(e[5]=t=>n.clientName=t),placeholder:"请输入客户名称"},null,8,["modelValue"])]),_:1}),a(d,{label:"工程描述"},{default:s(()=>[a(p,{modelValue:n.description,"onUpdate:modelValue":e[6]||(e[6]=t=>n.description=t),type:"textarea",rows:3,placeholder:"请输入工程描述"},null,8,["modelValue"])]),_:1}),a(d,{label:"项目经理"},{default:s(()=>[a($,{modelValue:n.projectManager,"onUpdate:modelValue":e[7]||(e[7]=t=>n.projectManager=t),placeholder:"请选择项目经理"},{default:s(()=>[(D(!0),T(J,null,Q(P.value,t=>(D(),k(c,{key:t.id,label:t.realName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"开始日期"},{default:s(()=>[a(z,{modelValue:n.startDate,"onUpdate:modelValue":e[8]||(e[8]=t=>n.startDate=t),type:"date",placeholder:"选择开始日期"},null,8,["modelValue"])]),_:1}),a(d,{label:"结束日期"},{default:s(()=>[a(z,{modelValue:n.endDate,"onUpdate:modelValue":e[9]||(e[9]=t=>n.endDate=t),type:"date",placeholder:"选择结束日期"},null,8,["modelValue"])]),_:1}),a(d,{label:"工程状态"},{default:s(()=>[a($,{modelValue:n.status,"onUpdate:modelValue":e[10]||(e[10]=t=>n.status=t),placeholder:"请选择工程状态"},{default:s(()=>[a(c,{label:"规划中",value:"planning"}),a(c,{label:"进行中",value:"active"}),a(c,{label:"暂停",value:"paused"}),a(c,{label:"已完成",value:"completed"}),a(c,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"预算"},{default:s(()=>[a(p,{modelValue:n.budget,"onUpdate:modelValue":e[11]||(e[11]=t=>n.budget=t),placeholder:"请输入预算金额",type:"number"},{append:s(()=>e[29]||(e[29]=[i("元")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),a(X,{modelValue:F.value,"onUpdate:modelValue":e[20]||(e[20]=t=>F.value=t),title:`${(Z=f.value)==null?void 0:Z.name} - 人员管理`,width:"800px"},{default:s(()=>[j("div",Oe,[a(I,{class:"add-user-card",shadow:"never"},{header:s(()=>[j("div",Se,[j("span",null,U(g.value?"编辑人员":"添加人员"),1),g.value?(D(),k(o,{key:0,text:"",type:"info",onClick:G},{default:s(()=>e[32]||(e[32]=[i(" 取消编辑 ")])),_:1,__:[32]})):W("",!0)])]),default:s(()=>[a(S,{model:r,inline:""},{default:s(()=>[a(d,{label:"选择用户"},{default:s(()=>[a($,{modelValue:r.userId,"onUpdate:modelValue":e[14]||(e[14]=t=>r.userId=t),placeholder:"请选择用户",style:{width:"200px"},disabled:!!g.value},{default:s(()=>[(D(!0),T(J,null,Q(L.value,t=>(D(),k(c,{key:t.id,label:`${t.realName} (${t.username})`,value:t.id},null,8,["label","value"]))),128)),g.value?(D(),k(c,{key:g.value.userId,label:`${g.value.userName} (${g.value.userUsername||""})`,value:g.value.userId},null,8,["label","value"])):W("",!0)]),_:1},8,["modelValue","disabled"])]),_:1}),a(d,{label:"角色"},{default:s(()=>[a(p,{modelValue:r.role,"onUpdate:modelValue":e[15]||(e[15]=t=>r.role=t),placeholder:"如：工程师、技术员",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),a(d,{label:"开始日期"},{default:s(()=>[a(z,{modelValue:r.startDate,"onUpdate:modelValue":e[16]||(e[16]=t=>r.startDate=t),type:"date",placeholder:"选择开始日期",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),a(d,{label:"结束日期"},{default:s(()=>[a(z,{modelValue:r.endDate,"onUpdate:modelValue":e[17]||(e[17]=t=>r.endDate=t),type:"date",placeholder:"选择结束日期",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),a(d,{label:"时薪"},{default:s(()=>[a(p,{modelValue:r.hourlyRate,"onUpdate:modelValue":e[18]||(e[18]=t=>r.hourlyRate=t),placeholder:"时薪",type:"number",style:{width:"180px"}},{append:s(()=>e[33]||(e[33]=[i("元/小时")])),_:1},8,["modelValue"])]),_:1}),a(d,null,{default:s(()=>[a(o,{type:"primary",onClick:e[19]||(e[19]=t=>g.value?fe():ge()),loading:V.value},{default:s(()=>[i(U(g.value?"更新":"添加"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),a(I,{class:"assignments-list-card",shadow:"never"},{header:s(()=>e[34]||(e[34]=[j("span",null,"已分配人员",-1)])),default:s(()=>[H((D(),k(K,{data:O.value},{default:s(()=>[a(m,{prop:"userName",label:"姓名"}),a(m,{prop:"userPhone",label:"电话"}),a(m,{prop:"role",label:"角色"}),a(m,{prop:"startDate",label:"开始日期"}),a(m,{prop:"endDate",label:"结束日期"}),a(m,{prop:"hourlyRate",label:"时薪"},{default:s(({row:t})=>[i(U(t.hourlyRate?`${t.hourlyRate}元/小时`:"-"),1)]),_:1}),a(m,{prop:"isActive",label:"状态"},{default:s(({row:t})=>[a(q,{type:t.isActive?"success":"danger"},{default:s(()=>[i(U(t.isActive?"激活":"停用"),1)]),_:2},1032,["type"])]),_:1}),a(m,{label:"操作",width:"150"},{default:s(({row:t})=>[a(o,{text:"",type:"warning",onClick:R=>ve(t)},{default:s(()=>e[35]||(e[35]=[i(" 编辑 ")])),_:2,__:[35]},1032,["onClick"]),a(o,{text:"",type:"danger",onClick:R=>ye(t)},{default:s(()=>e[36]||(e[36]=[i(" 移除 ")])),_:2,__:[36]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Y,V.value]])]),_:1})])]),_:1},8,["modelValue","title"])])}}});const qe=ze(Te,[["__scopeId","data-v-1e836a13"]]);export{qe as default};
