import{d as lr,r as le,a as et,K as $t,p as or,b as de,M as nr,o as se,c as Ie,e as x,f as l,w as i,i as A,g as Ce,y as j,q as be,x as ye,N as Wt,O as jt,s as qe,P as sr,ai as ir,E as B,aj as ur,ak as dr,al as cr,am as pr,Q as bt,an as mr,ao as Ht,ap as zt,aq as Yt,ar as fr,as as vr,Y as hr,a1 as gr,at as Ne,au as yr,F as br,av as _r,aw as wr,ax as Dr,W as _t,J as wt}from"./index-4256ff3d.js";import{P as we,D as Ut,r as Gt,f as ca,g as kr,S as Bt,h as Xt,l as Dt,_ as Oe,j as pa,k as Ze,m as It,n as Sr,o as ma,p as qt,q as Mt,s as at,t as fa,v as xr,w as Cr,x as Ue,y as Ge,z as va,A as Ar,B as At,G as Ir,C as Mr,F as Lt,H as ha,R as ga,I as Me,J as ya,K as ba,L as Lr,M as Tr,N as _a,O as wa,Q as Pr,T as Da,U as ft,V as Nr,W as Tt,X as Rr,Y as Er,Z as Vr,$ as Fr,a0 as Or,a1 as $r,a2 as Re,a3 as ka,a4 as Wr,a5 as jr,a6 as Hr,a7 as zr,a8 as Yr,a9 as Zt,aa as Ur,ab as Gr,ac as Br,ad as Xr,u as qr,i as Zr,a as Jr,b as Kr,c as Qr,d as el,e as tl,E as Jt}from"./index-850506a9.js";import{_ as al}from"./_plugin-vue_export-helper-c27b6911.js";function Sa(o,r,a,t,s,p,c,m){var h=s-o,y=p-r,f=a-o,w=t-r,C=Math.sqrt(f*f+w*w);f/=C,w/=C;var S=h*f+y*w,k=S/C;m&&(k=Math.min(Math.max(k,0),1)),k*=C;var L=c[0]=o+k*f,T=c[1]=r+k*w;return Math.sqrt((L-s)*(L-s)+(T-p)*(T-p))}var Ve=new we,he=new we,_e=new we,Fe=new we,Le=new we,mt=[],ke=new we;function rl(o,r){if(r<=180&&r>0){r=r/180*Math.PI,Ve.fromArray(o[0]),he.fromArray(o[1]),_e.fromArray(o[2]),we.sub(Fe,Ve,he),we.sub(Le,_e,he);var a=Fe.len(),t=Le.len();if(!(a<.001||t<.001)){Fe.scale(1/a),Le.scale(1/t);var s=Fe.dot(Le),p=Math.cos(r);if(p<s){var c=Sa(he.x,he.y,_e.x,_e.y,Ve.x,Ve.y,mt,!1);ke.fromArray(mt),ke.scaleAndAdd(Le,c/Math.tan(Math.PI-r));var m=_e.x!==he.x?(ke.x-he.x)/(_e.x-he.x):(ke.y-he.y)/(_e.y-he.y);if(isNaN(m))return;m<0?we.copy(ke,he):m>1&&we.copy(ke,_e),ke.toArray(o[1])}}}}function ll(o,r,a){if(a<=180&&a>0){a=a/180*Math.PI,Ve.fromArray(o[0]),he.fromArray(o[1]),_e.fromArray(o[2]),we.sub(Fe,he,Ve),we.sub(Le,_e,he);var t=Fe.len(),s=Le.len();if(!(t<.001||s<.001)){Fe.scale(1/t),Le.scale(1/s);var p=Fe.dot(r),c=Math.cos(a);if(p<c){var m=Sa(he.x,he.y,_e.x,_e.y,Ve.x,Ve.y,mt,!1);ke.fromArray(mt);var h=Math.PI/2,y=Math.acos(Le.dot(r)),f=h+y-a;if(f>=h)we.copy(ke,_e);else{ke.scaleAndAdd(Le,m/Math.tan(Math.PI/2-f));var w=_e.x!==he.x?(ke.x-he.x)/(_e.x-he.x):(ke.y-he.y)/(_e.y-he.y);if(isNaN(w))return;w<0?we.copy(ke,he):w>1&&we.copy(ke,_e)}ke.toArray(o[1])}}}}function kt(o,r,a,t){var s=a==="normal",p=s?o:o.ensureState(a);p.ignore=r;var c=t.get("smooth");c&&c===!0&&(c=.3),p.shape=p.shape||{},c>0&&(p.shape.smooth=c);var m=t.getModel("lineStyle").getLineStyle();s?o.useStyle(m):p.style=m}function ol(o,r){var a=r.smooth,t=r.points;if(t)if(o.moveTo(t[0][0],t[0][1]),a>0&&t.length>=3){var s=Xt(t[0],t[1]),p=Xt(t[1],t[2]);if(!s||!p){o.lineTo(t[1][0],t[1][1]),o.lineTo(t[2][0],t[2][1]);return}var c=Math.min(s,p)*a,m=Dt([],t[1],t[0],c/s),h=Dt([],t[1],t[2],c/p),y=Dt([],m,h,.5);o.bezierCurveTo(m[0],m[1],m[0],m[1],y[0],y[1]),o.bezierCurveTo(h[0],h[1],h[0],h[1],t[2][0],t[2][1])}else for(var f=1;f<t.length;f++)o.lineTo(t[f][0],t[f][1])}function nl(o,r,a){var t=o.getTextGuideLine(),s=o.getTextContent();if(!s){t&&o.removeTextGuideLine();return}for(var p=r.normal,c=p.get("show"),m=s.ignore,h=0;h<Ut.length;h++){var y=Ut[h],f=r[y],w=y==="normal";if(f){var C=f.get("show"),S=w?m:Gt(s.states[y]&&s.states[y].ignore,m);if(S||!Gt(C,c)){var k=w?t:t&&t.states[y];k&&(k.ignore=!0),t&&kt(t,!0,y,f);continue}t||(t=new ca,o.setTextGuideLine(t),!w&&(m||!c)&&kt(t,!0,"normal",r.normal),o.stateProxy&&(t.stateProxy=o.stateProxy)),kt(t,!1,y,f)}}if(t){kr(t.style,a),t.style.fill=null;var L=p.get("showAbove"),T=o.textGuideLineConfig=o.textGuideLineConfig||{};T.showAbove=L||!1,t.buildPath=ol}}function sl(o,r){r=r||"labelLine";for(var a={normal:o.getModel(r)},t=0;t<Bt.length;t++){var s=Bt[t];a[s]=o.getModel([s,r])}return a}var xa=function(o){Oe(r,o);function r(){var a=o!==null&&o.apply(this,arguments)||this;return a.type=r.type,a}return r.prototype.getInitialData=function(a,t){return pa(null,this,{useEncodeDefaulter:!0})},r.prototype.getMarkerPosition=function(a,t,s){var p=this.coordinateSystem;if(p&&p.clampData){var c=p.clampData(a),m=p.dataToPoint(c);if(s)Ze(p.getAxes(),function(C,S){if(C.type==="category"&&t!=null){var k=C.getTicksCoords(),L=C.getTickModel().get("alignWithLabel"),T=c[S],F=t[S]==="x1"||t[S]==="y1";if(F&&!L&&(T+=1),k.length<2)return;if(k.length===2){m[S]=C.toGlobalCoord(C.getExtent()[F?1:0]);return}for(var V=void 0,I=void 0,W=1,U=0;U<k.length;U++){var X=k[U].coord,N=U===k.length-1?k[U-1].tickValue+W:k[U].tickValue;if(N===T){I=X;break}else if(N<T)V=X;else if(V!=null&&N>T){I=(X+V)/2;break}U===1&&(W=N-k[0].tickValue)}I==null&&(V?V&&(I=k[k.length-1].coord):I=k[0].coord),m[S]=C.toGlobalCoord(I)}});else{var h=this.getData(),y=h.getLayout("offset"),f=h.getLayout("size"),w=p.getBaseAxis().isHorizontal()?0:1;m[w]+=y+f/2}return m}return[NaN,NaN]},r.type="series.__base_bar__",r.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},r}(It);It.registerClass(xa);const Kt=xa;var il=function(o){Oe(r,o);function r(){var a=o!==null&&o.apply(this,arguments)||this;return a.type=r.type,a}return r.prototype.getInitialData=function(){return pa(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},r.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},r.prototype.getProgressiveThreshold=function(){var a=this.get("progressiveThreshold"),t=this.get("largeThreshold");return t>a&&(a=t),a},r.prototype.brushSelector=function(a,t,s){return s.rect(t.getItemLayout(a))},r.type="series.bar",r.dependencies=["grid","polar"],r.defaultOption=Sr(Kt.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),r}(Kt);const ul=il;var dl=function(){function o(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return o}(),cl=function(o){Oe(r,o);function r(a){var t=o.call(this,a)||this;return t.type="sausage",t}return r.prototype.getDefaultShape=function(){return new dl},r.prototype.buildPath=function(a,t){var s=t.cx,p=t.cy,c=Math.max(t.r0||0,0),m=Math.max(t.r,0),h=(m-c)*.5,y=c+h,f=t.startAngle,w=t.endAngle,C=t.clockwise,S=Math.PI*2,k=C?w-f<S:f-w<S;k||(f=w-(C?S:-S));var L=Math.cos(f),T=Math.sin(f),F=Math.cos(w),V=Math.sin(w);k?(a.moveTo(L*c+s,T*c+p),a.arc(L*y+s,T*y+p,h,-Math.PI+f,f,!C)):a.moveTo(L*m+s,T*m+p),a.arc(s,p,m,f,w,!C),a.arc(F*y+s,V*y+p,h,w-Math.PI*2,w-Math.PI,!C),c!==0&&a.arc(s,p,c,w,f,C)},r}(ma);const Qt=cl;function pl(o,r){r=r||{};var a=r.isRoundCap;return function(t,s,p){var c=s.position;if(!c||c instanceof Array)return qt(t,s,p);var m=o(c),h=s.distance!=null?s.distance:5,y=this.shape,f=y.cx,w=y.cy,C=y.r,S=y.r0,k=(C+S)/2,L=y.startAngle,T=y.endAngle,F=(L+T)/2,V=a?Math.abs(C-S)/2:0,I=Math.cos,W=Math.sin,U=f+C*I(L),X=w+C*W(L),N="left",H="top";switch(m){case"startArc":U=f+(S-h)*I(F),X=w+(S-h)*W(F),N="center",H="top";break;case"insideStartArc":U=f+(S+h)*I(F),X=w+(S+h)*W(F),N="center",H="bottom";break;case"startAngle":U=f+k*I(L)+ut(L,h+V,!1),X=w+k*W(L)+dt(L,h+V,!1),N="right",H="middle";break;case"insideStartAngle":U=f+k*I(L)+ut(L,-h+V,!1),X=w+k*W(L)+dt(L,-h+V,!1),N="left",H="middle";break;case"middle":U=f+k*I(F),X=w+k*W(F),N="center",H="middle";break;case"endArc":U=f+(C+h)*I(F),X=w+(C+h)*W(F),N="center",H="bottom";break;case"insideEndArc":U=f+(C-h)*I(F),X=w+(C-h)*W(F),N="center",H="top";break;case"endAngle":U=f+k*I(T)+ut(T,h+V,!0),X=w+k*W(T)+dt(T,h+V,!0),N="left",H="middle";break;case"insideEndAngle":U=f+k*I(T)+ut(T,-h+V,!0),X=w+k*W(T)+dt(T,-h+V,!0),N="right",H="middle";break;default:return qt(t,s,p)}return t=t||{},t.x=U,t.y=X,t.align=N,t.verticalAlign=H,t}}function ml(o,r,a,t){if(Mt(t)){o.setTextConfig({rotation:t});return}else if(at(r)){o.setTextConfig({rotation:0});return}var s=o.shape,p=s.clockwise?s.startAngle:s.endAngle,c=s.clockwise?s.endAngle:s.startAngle,m=(p+c)/2,h,y=a(r);switch(y){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":h=m;break;case"startAngle":case"insideStartAngle":h=p;break;case"endAngle":case"insideEndAngle":h=c;break;default:o.setTextConfig({rotation:0});return}var f=Math.PI*1.5-h;y==="middle"&&f>Math.PI/2&&f<Math.PI*1.5&&(f-=Math.PI),o.setTextConfig({rotation:f})}function ut(o,r,a){return r*Math.sin(o)*(a?-1:1)}function dt(o,r,a){return r*Math.cos(o)*(a?1:-1)}function tt(o,r,a){var t=o.get("borderRadius");if(t==null)return a?{cornerRadius:0}:null;at(t)||(t=[t,t,t,t]);var s=Math.abs(r.r||0-r.r0||0);return{cornerRadius:fa(t,function(p){return xr(p,s)})}}var St=Math.max,xt=Math.min;function fl(o,r){var a=o.getArea&&o.getArea();if(Da(o,"cartesian2d")){var t=o.getBaseAxis();if(t.type!=="category"||!t.onBand){var s=r.getLayout("bandWidth");t.isHorizontal()?(a.x-=s,a.width+=s*2):(a.y-=s,a.height+=s*2)}}return a}var vl=function(o){Oe(r,o);function r(){var a=o.call(this)||this;return a.type=r.type,a._isFirstFrame=!0,a}return r.prototype.render=function(a,t,s,p){this._model=a,this._removeOnRenderedListener(s),this._updateDrawMode(a);var c=a.get("coordinateSystem");(c==="cartesian2d"||c==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(a,t,s):this._renderNormal(a,t,s,p))},r.prototype.incrementalPrepareRender=function(a){this._clear(),this._updateDrawMode(a),this._updateLargeClip(a)},r.prototype.incrementalRender=function(a,t){this._progressiveEls=[],this._incrementalRenderLarge(a,t)},r.prototype.eachRendered=function(a){Cr(this._progressiveEls||this.group,a)},r.prototype._updateDrawMode=function(a){var t=a.pipelineContext.large;(this._isLargeDraw==null||t!==this._isLargeDraw)&&(this._isLargeDraw=t,this._clear())},r.prototype._renderNormal=function(a,t,s,p){var c=this.group,m=a.getData(),h=this._data,y=a.coordinateSystem,f=y.getBaseAxis(),w;y.type==="cartesian2d"?w=f.isHorizontal():y.type==="polar"&&(w=f.dim==="angle");var C=a.isAnimationEnabled()?a:null,S=hl(a,y);S&&this._enableRealtimeSort(S,m,s);var k=a.get("clip",!0)||S,L=fl(y,m);c.removeClipPath();var T=a.get("roundCap",!0),F=a.get("showBackground",!0),V=a.getModel("backgroundStyle"),I=V.get("borderRadius")||0,W=[],U=this._backgroundEls,X=p&&p.isInitSort,N=p&&p.type==="changeAxisOrder";function H(Z){var ee=ct[y.type](m,Z),K=kl(y,w,ee);return K.useStyle(V.getItemStyle()),y.type==="cartesian2d"?K.setShape("r",I):K.setShape("cornerRadius",I),W[Z]=K,K}m.diff(h).add(function(Z){var ee=m.getItemModel(Z),K=ct[y.type](m,Z,ee);if(F&&H(Z),!(!m.hasValue(Z)||!la[y.type](K))){var G=!1;k&&(G=ea[y.type](L,K));var z=ta[y.type](a,m,Z,K,w,C,f.model,!1,T);S&&(z.forceLabelAnimation=!0),oa(z,m,Z,ee,K,a,w,y.type==="polar"),X?z.attr({shape:K}):S?aa(S,C,z,K,Z,w,!1,!1):Ue(z,{shape:K},a,Z),m.setItemGraphicEl(Z,z),c.add(z),z.ignore=G}}).update(function(Z,ee){var K=m.getItemModel(Z),G=ct[y.type](m,Z,K);if(F){var z=void 0;U.length===0?z=H(ee):(z=U[ee],z.useStyle(V.getItemStyle()),y.type==="cartesian2d"?z.setShape("r",I):z.setShape("cornerRadius",I),W[Z]=z);var re=ct[y.type](m,Z),Q=Aa(w,re,y);Ge(z,{shape:Q},C,Z)}var te=h.getItemGraphicEl(ee);if(!m.hasValue(Z)||!la[y.type](G)){c.remove(te);return}var oe=!1;if(k&&(oe=ea[y.type](L,G),oe&&c.remove(te)),te?va(te):te=ta[y.type](a,m,Z,G,w,C,f.model,!!te,T),S&&(te.forceLabelAnimation=!0),N){var ce=te.getTextContent();if(ce){var q=Ar(ce);q.prevValue!=null&&(q.prevValue=q.value)}}else oa(te,m,Z,K,G,a,w,y.type==="polar");X?te.attr({shape:G}):S?aa(S,C,te,G,Z,w,!0,N):Ge(te,{shape:G},a,Z,null),m.setItemGraphicEl(Z,te),te.ignore=oe,c.add(te)}).remove(function(Z){var ee=h.getItemGraphicEl(Z);ee&&At(ee,a,Z)}).execute();var ae=this._backgroundGroup||(this._backgroundGroup=new Ir);ae.removeAll();for(var me=0;me<W.length;++me)ae.add(W[me]);c.add(ae),this._backgroundEls=W,this._data=m},r.prototype._renderLarge=function(a,t,s){this._clear(),sa(a,this.group),this._updateLargeClip(a)},r.prototype._incrementalRenderLarge=function(a,t){this._removeBackground(),sa(t,this.group,this._progressiveEls,!0)},r.prototype._updateLargeClip=function(a){var t=a.get("clip",!0)&&Mr(a.coordinateSystem,!1,a),s=this.group;t?s.setClipPath(t):s.removeClipPath()},r.prototype._enableRealtimeSort=function(a,t,s){var p=this;if(t.count()){var c=a.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(t,a,s),this._isFirstFrame=!1;else{var m=function(h){var y=t.getItemGraphicEl(h),f=y&&y.shape;return f&&Math.abs(c.isHorizontal()?f.height:f.width)||0};this._onRendered=function(){p._updateSortWithinSameData(t,m,c,s)},s.getZr().on("rendered",this._onRendered)}}},r.prototype._dataSort=function(a,t,s){var p=[];return a.each(a.mapDimension(t.dim),function(c,m){var h=s(m);h=h??NaN,p.push({dataIndex:m,mappedValue:h,ordinalNumber:c})}),p.sort(function(c,m){return m.mappedValue-c.mappedValue}),{ordinalNumbers:fa(p,function(c){return c.ordinalNumber})}},r.prototype._isOrderChangedWithinSameData=function(a,t,s){for(var p=s.scale,c=a.mapDimension(s.dim),m=Number.MAX_VALUE,h=0,y=p.getOrdinalMeta().categories.length;h<y;++h){var f=a.rawIndexOf(c,p.getRawOrdinalNumber(h)),w=f<0?Number.MIN_VALUE:t(a.indexOfRawIndex(f));if(w>m)return!0;m=w}return!1},r.prototype._isOrderDifferentInView=function(a,t){for(var s=t.scale,p=s.getExtent(),c=Math.max(0,p[0]),m=Math.min(p[1],s.getOrdinalMeta().categories.length-1);c<=m;++c)if(a.ordinalNumbers[c]!==s.getRawOrdinalNumber(c))return!0},r.prototype._updateSortWithinSameData=function(a,t,s,p){if(this._isOrderChangedWithinSameData(a,t,s)){var c=this._dataSort(a,s,t);this._isOrderDifferentInView(c,s)&&(this._removeOnRenderedListener(p),p.dispatchAction({type:"changeAxisOrder",componentType:s.dim+"Axis",axisId:s.index,sortInfo:c}))}},r.prototype._dispatchInitSort=function(a,t,s){var p=t.baseAxis,c=this._dataSort(a,p,function(m){return a.get(a.mapDimension(t.otherAxis.dim),m)});s.dispatchAction({type:"changeAxisOrder",componentType:p.dim+"Axis",isInitSort:!0,axisId:p.index,sortInfo:c})},r.prototype.remove=function(a,t){this._clear(this._model),this._removeOnRenderedListener(t)},r.prototype.dispose=function(a,t){this._removeOnRenderedListener(t)},r.prototype._removeOnRenderedListener=function(a){this._onRendered&&(a.getZr().off("rendered",this._onRendered),this._onRendered=null)},r.prototype._clear=function(a){var t=this.group,s=this._data;a&&a.isAnimationEnabled()&&s&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],s.eachItemGraphicEl(function(p){At(p,a,Lt(p).dataIndex)})):t.removeAll(),this._data=null,this._isFirstFrame=!0},r.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},r.type="bar",r}(ha),ea={cartesian2d:function(o,r){var a=r.width<0?-1:1,t=r.height<0?-1:1;a<0&&(r.x+=r.width,r.width=-r.width),t<0&&(r.y+=r.height,r.height=-r.height);var s=o.x+o.width,p=o.y+o.height,c=St(r.x,o.x),m=xt(r.x+r.width,s),h=St(r.y,o.y),y=xt(r.y+r.height,p),f=m<c,w=y<h;return r.x=f&&c>s?m:c,r.y=w&&h>p?y:h,r.width=f?0:m-c,r.height=w?0:y-h,a<0&&(r.x+=r.width,r.width=-r.width),t<0&&(r.y+=r.height,r.height=-r.height),f||w},polar:function(o,r){var a=r.r0<=r.r?1:-1;if(a<0){var t=r.r;r.r=r.r0,r.r0=t}var s=xt(r.r,o.r),p=St(r.r0,o.r0);r.r=s,r.r0=p;var c=s-p<0;if(a<0){var t=r.r;r.r=r.r0,r.r0=t}return c}},ta={cartesian2d:function(o,r,a,t,s,p,c,m,h){var y=new ga({shape:Me({},t),z2:1});if(y.__dataIndex=a,y.name="item",p){var f=y.shape,w=s?"height":"width";f[w]=0}return y},polar:function(o,r,a,t,s,p,c,m,h){var y=!s&&h?Qt:ft,f=new y({shape:t,z2:1});f.name="item";var w=Ca(s);if(f.calculateTextPosition=pl(w,{isRoundCap:y===Qt}),p){var C=f.shape,S=s?"r":"endAngle",k={};C[S]=s?t.r0:t.startAngle,k[S]=t[S],(m?Ge:Ue)(f,{shape:k},p)}return f}};function hl(o,r){var a=o.get("realtimeSort",!0),t=r.getBaseAxis();if(a&&t.type==="category"&&r.type==="cartesian2d")return{baseAxis:t,otherAxis:r.getOtherAxis(t)}}function aa(o,r,a,t,s,p,c,m){var h,y;p?(y={x:t.x,width:t.width},h={y:t.y,height:t.height}):(y={y:t.y,height:t.height},h={x:t.x,width:t.width}),m||(c?Ge:Ue)(a,{shape:h},r,s,null);var f=r?o.baseAxis.model:null;(c?Ge:Ue)(a,{shape:y},f,s)}function ra(o,r){for(var a=0;a<r.length;a++)if(!isFinite(o[r[a]]))return!0;return!1}var gl=["x","y","width","height"],yl=["cx","cy","r","startAngle","endAngle"],la={cartesian2d:function(o){return!ra(o,gl)},polar:function(o){return!ra(o,yl)}},ct={cartesian2d:function(o,r,a){var t=o.getItemLayout(r),s=a?_l(a,t):0,p=t.width>0?1:-1,c=t.height>0?1:-1;return{x:t.x+p*s/2,y:t.y+c*s/2,width:t.width-p*s,height:t.height-c*s}},polar:function(o,r,a){var t=o.getItemLayout(r);return{cx:t.cx,cy:t.cy,r0:t.r0,r:t.r,startAngle:t.startAngle,endAngle:t.endAngle,clockwise:t.clockwise}}};function bl(o){return o.startAngle!=null&&o.endAngle!=null&&o.startAngle===o.endAngle}function Ca(o){return function(r){var a=r?"Arc":"Angle";return function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+a;default:return t}}}(o)}function oa(o,r,a,t,s,p,c,m){var h=r.getItemVisual(a,"style");if(m){if(!p.get("roundCap")){var f=o.shape,w=tt(t.getModel("itemStyle"),f,!0);Me(f,w),o.setShape(f)}}else{var y=t.get(["itemStyle","borderRadius"])||0;o.setShape("r",y)}o.useStyle(h);var C=t.getShallow("cursor");C&&o.attr("cursor",C);var S=m?c?s.r>=s.r0?"endArc":"startArc":s.endAngle>=s.startAngle?"endAngle":"startAngle":c?s.height>=0?"bottom":"top":s.width>=0?"right":"left",k=ya(t);ba(o,k,{labelFetcher:p,labelDataIndex:a,defaultText:Lr(p.getData(),a),inheritColor:h.fill,defaultOpacity:h.opacity,defaultOutsidePosition:S});var L=o.getTextContent();if(m&&L){var T=t.get(["label","position"]);o.textConfig.inside=T==="middle"?!0:null,ml(o,T==="outside"?S:T,Ca(c),t.get(["label","rotate"]))}Tr(L,k,p.getRawValue(a),function(V){return Nr(r,V)});var F=t.getModel(["emphasis"]);_a(o,F.get("focus"),F.get("blurScope"),F.get("disabled")),wa(o,t),bl(s)&&(o.style.fill="none",o.style.stroke="none",Ze(o.states,function(V){V.style&&(V.style.fill=V.style.stroke="none")}))}function _l(o,r){var a=o.get(["itemStyle","borderColor"]);if(!a||a==="none")return 0;var t=o.get(["itemStyle","borderWidth"])||0,s=isNaN(r.width)?Number.MAX_VALUE:Math.abs(r.width),p=isNaN(r.height)?Number.MAX_VALUE:Math.abs(r.height);return Math.min(t,s,p)}var wl=function(){function o(){}return o}(),na=function(o){Oe(r,o);function r(a){var t=o.call(this,a)||this;return t.type="largeBar",t}return r.prototype.getDefaultShape=function(){return new wl},r.prototype.buildPath=function(a,t){for(var s=t.points,p=this.baseDimIdx,c=1-this.baseDimIdx,m=[],h=[],y=this.barWidth,f=0;f<s.length;f+=3)h[p]=y,h[c]=s[f+2],m[p]=s[f+p],m[c]=s[f+c],a.rect(m[0],m[1],h[0],h[1])},r}(ma);function sa(o,r,a,t){var s=o.getData(),p=s.getLayout("valueAxisHorizontal")?1:0,c=s.getLayout("largeDataIndices"),m=s.getLayout("size"),h=o.getModel("backgroundStyle"),y=s.getLayout("largeBackgroundPoints");if(y){var f=new na({shape:{points:y},incremental:!!t,silent:!0,z2:0});f.baseDimIdx=p,f.largeDataIndices=c,f.barWidth=m,f.useStyle(h.getItemStyle()),r.add(f),a&&a.push(f)}var w=new na({shape:{points:s.getLayout("largePoints")},incremental:!!t,ignoreCoarsePointer:!0,z2:1});w.baseDimIdx=p,w.largeDataIndices=c,w.barWidth=m,r.add(w),w.useStyle(s.getVisual("style")),w.style.stroke=null,Lt(w).seriesIndex=o.seriesIndex,o.get("silent")||(w.on("mousedown",ia),w.on("mousemove",ia)),a&&a.push(w)}var ia=Pr(function(o){var r=this,a=Dl(r,o.offsetX,o.offsetY);Lt(r).dataIndex=a>=0?a:null},30,!1);function Dl(o,r,a){for(var t=o.baseDimIdx,s=1-t,p=o.shape.points,c=o.largeDataIndices,m=[],h=[],y=o.barWidth,f=0,w=p.length/3;f<w;f++){var C=f*3;if(h[t]=y,h[s]=p[C+2],m[t]=p[C+t],m[s]=p[C+s],h[s]<0&&(m[s]+=h[s],h[s]=-h[s]),r>=m[0]&&r<=m[0]+h[0]&&a>=m[1]&&a<=m[1]+h[1])return c[f]}return-1}function Aa(o,r,a){if(Da(a,"cartesian2d")){var t=r,s=a.getArea();return{x:o?t.x:s.x,y:o?s.y:t.y,width:o?t.width:s.width,height:o?s.height:t.height}}else{var s=a.getArea(),p=r;return{cx:s.cx,cy:s.cy,r0:o?s.r0:p.r0,r:o?s.r:p.r,startAngle:o?p.startAngle:0,endAngle:o?p.endAngle:Math.PI*2}}}function kl(o,r,a){var t=o.type==="polar"?ft:ga;return new t({shape:Aa(r,a,o),silent:!0,z2:0})}const Sl=vl;function xl(o){o.registerChartView(Sl),o.registerSeriesModel(ul),o.registerLayout(o.PRIORITY.VISUAL.LAYOUT,Tt(Vr,"bar")),o.registerLayout(o.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Rr("bar")),o.registerProcessor(o.PRIORITY.PROCESSOR.STATISTIC,Er("bar")),o.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(r,a){var t=r.componentType||"series";a.eachComponent({mainType:t,query:r},function(s){r.sortInfo&&s.axis.setCategorySortInfo(r.sortInfo)})})}var ua=Math.PI*2,pt=Math.PI/180;function Ia(o,r){return $r(o.getBoxLayoutParams(),{width:r.getWidth(),height:r.getHeight()})}function Ma(o,r){var a=Ia(o,r),t=o.get("center"),s=o.get("radius");at(s)||(s=[0,s]);var p=Re(a.width,r.getWidth()),c=Re(a.height,r.getHeight()),m=Math.min(p,c),h=Re(s[0],m/2),y=Re(s[1],m/2),f,w,C=o.coordinateSystem;if(C){var S=C.dataToPoint(t);f=S[0]||0,w=S[1]||0}else at(t)||(t=[t,t]),f=Re(t[0],p)+a.x,w=Re(t[1],c)+a.y;return{cx:f,cy:w,r0:h,r:y}}function Cl(o,r,a){r.eachSeriesByType(o,function(t){var s=t.getData(),p=s.mapDimension("value"),c=Ia(t,a),m=Ma(t,a),h=m.cx,y=m.cy,f=m.r,w=m.r0,C=-t.get("startAngle")*pt,S=t.get("endAngle"),k=t.get("padAngle")*pt;S=S==="auto"?C-ua:-S*pt;var L=t.get("minAngle")*pt,T=L+k,F=0;s.each(p,function(Q){!isNaN(Q)&&F++});var V=s.getSum(p),I=Math.PI/(V||F)*2,W=t.get("clockwise"),U=t.get("roseType"),X=t.get("stillShowZeroSum"),N=s.getDataExtent(p);N[0]=0;var H=W?1:-1,ae=[C,S],me=H*k/2;Fr(ae,!W),C=ae[0],S=ae[1];var Z=La(t);Z.startAngle=C,Z.endAngle=S,Z.clockwise=W;var ee=Math.abs(S-C),K=ee,G=0,z=C;if(s.setLayout({viewRect:c,r:f}),s.each(p,function(Q,te){var oe;if(isNaN(Q)){s.setItemLayout(te,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:W,cx:h,cy:y,r0:w,r:U?NaN:f});return}U!=="area"?oe=V===0&&X?I:Q*I:oe=ee/F,oe<T?(oe=T,K-=T):G+=Q;var ce=z+H*oe,q=0,ve=0;k>oe?(q=z+H*oe/2,ve=q):(q=z+me,ve=ce-me),s.setItemLayout(te,{angle:oe,startAngle:q,endAngle:ve,clockwise:W,cx:h,cy:y,r0:w,r:U?Or(Q,N,[w,f]):f}),z=ce}),K<ua&&F)if(K<=.001){var re=ee/F;s.each(p,function(Q,te){if(!isNaN(Q)){var oe=s.getItemLayout(te);oe.angle=re;var ce=0,q=0;re<k?(ce=C+H*(te+1/2)*re,q=ce):(ce=C+H*te*re+me,q=C+H*(te+1)*re-me),oe.startAngle=ce,oe.endAngle=q}})}else I=K/G,z=C,s.each(p,function(Q,te){if(!isNaN(Q)){var oe=s.getItemLayout(te),ce=oe.angle===T?T:Q*I,q=0,ve=0;ce<k?(q=z+H*ce/2,ve=q):(q=z+me,ve=z+H*ce-me),oe.startAngle=q,oe.endAngle=ve,z+=H*ce}})})}var La=ka();function Al(o){return{seriesType:o,reset:function(r,a){var t=a.findComponents({mainType:"legend"});if(!(!t||!t.length)){var s=r.getData();s.filterSelf(function(p){for(var c=s.getName(p),m=0;m<t.length;m++)if(!t[m].isSelected(c))return!1;return!0})}}}}var Il=Math.PI/180;function da(o,r,a,t,s,p,c,m,h,y){if(o.length<2)return;function f(L){for(var T=L.rB,F=T*T,V=0;V<L.list.length;V++){var I=L.list[V],W=Math.abs(I.label.y-a),U=t+I.len,X=U*U,N=Math.sqrt(Math.abs((1-W*W/F)*X)),H=r+(N+I.len2)*s,ae=H-I.label.x,me=I.targetTextWidth-ae*s;Ta(I,me,!0),I.label.x=H}}function w(L){for(var T={list:[],maxY:0},F={list:[],maxY:0},V=0;V<L.length;V++)if(L[V].labelAlignTo==="none"){var I=L[V],W=I.label.y>a?F:T,U=Math.abs(I.label.y-a);if(U>=W.maxY){var X=I.label.x-r-I.len2*s,N=t+I.len,H=Math.abs(X)<N?Math.sqrt(U*U/(1-X*X/N/N)):N;W.rB=H,W.maxY=U}W.list.push(I)}f(T),f(F)}for(var C=o.length,S=0;S<C;S++)if(o[S].position==="outer"&&o[S].labelAlignTo==="labelLine"){var k=o[S].label.x-y;o[S].linePoints[1][0]+=k,o[S].label.x=y}Wr(o,h,h+c)&&w(o)}function Ml(o,r,a,t,s,p,c,m){for(var h=[],y=[],f=Number.MAX_VALUE,w=-Number.MAX_VALUE,C=0;C<o.length;C++){var S=o[C].label;Ct(o[C])||(S.x<r?(f=Math.min(f,S.x),h.push(o[C])):(w=Math.max(w,S.x),y.push(o[C])))}for(var C=0;C<o.length;C++){var k=o[C];if(!Ct(k)&&k.linePoints){if(k.labelStyleWidth!=null)continue;var S=k.label,L=k.linePoints,T=void 0;k.labelAlignTo==="edge"?S.x<r?T=L[2][0]-k.labelDistance-c-k.edgeDistance:T=c+s-k.edgeDistance-L[2][0]-k.labelDistance:k.labelAlignTo==="labelLine"?S.x<r?T=f-c-k.bleedMargin:T=c+s-w-k.bleedMargin:S.x<r?T=S.x-c-k.bleedMargin:T=c+s-S.x-k.bleedMargin,k.targetTextWidth=T,Ta(k,T)}}da(y,r,a,t,1,s,p,c,m,w),da(h,r,a,t,-1,s,p,c,m,f);for(var C=0;C<o.length;C++){var k=o[C];if(!Ct(k)&&k.linePoints){var S=k.label,L=k.linePoints,F=k.labelAlignTo==="edge",V=S.style.padding,I=V?V[1]+V[3]:0,W=S.style.backgroundColor?0:I,U=k.rect.width+W,X=L[1][0]-L[2][0];F?S.x<r?L[2][0]=c+k.edgeDistance+U+k.labelDistance:L[2][0]=c+s-k.edgeDistance-U-k.labelDistance:(S.x<r?L[2][0]=S.x+k.labelDistance:L[2][0]=S.x-k.labelDistance,L[1][0]=L[2][0]+X),L[1][1]=L[2][1]=S.y}}}function Ta(o,r,a){if(a===void 0&&(a=!1),o.labelStyleWidth==null){var t=o.label,s=t.style,p=o.rect,c=s.backgroundColor,m=s.padding,h=m?m[1]+m[3]:0,y=s.overflow,f=p.width+(c?0:h);if(r<f||a){var w=p.height;if(y&&y.match("break")){t.setStyle("backgroundColor",null),t.setStyle("width",r-h);var C=t.getBoundingRect();t.setStyle("width",Math.ceil(C.width)),t.setStyle("backgroundColor",c)}else{var S=r-h,k=r<f?S:a?S>o.unconstrainedWidth?null:S:null;t.setStyle("width",k)}var L=t.getBoundingRect();p.width=L.width;var T=(t.style.margin||0)+2.1;p.height=L.height+T,p.y-=(p.height-w)/2}}}function Ct(o){return o.position==="center"}function Ll(o){var r=o.getData(),a=[],t,s,p=!1,c=(o.get("minShowLabelAngle")||0)*Il,m=r.getLayout("viewRect"),h=r.getLayout("r"),y=m.width,f=m.x,w=m.y,C=m.height;function S(X){X.ignore=!0}function k(X){if(!X.ignore)return!0;for(var N in X.states)if(X.states[N].ignore===!1)return!0;return!1}r.each(function(X){var N=r.getItemGraphicEl(X),H=N.shape,ae=N.getTextContent(),me=N.getTextGuideLine(),Z=r.getItemModel(X),ee=Z.getModel("label"),K=ee.get("position")||Z.get(["emphasis","label","position"]),G=ee.get("distanceToLabelLine"),z=ee.get("alignTo"),re=Re(ee.get("edgeDistance"),y),Q=ee.get("bleedMargin"),te=Z.getModel("labelLine"),oe=te.get("length");oe=Re(oe,y);var ce=te.get("length2");if(ce=Re(ce,y),Math.abs(H.endAngle-H.startAngle)<c){Ze(ae.states,S),ae.ignore=!0,me&&(Ze(me.states,S),me.ignore=!0);return}if(k(ae)){var q=(H.startAngle+H.endAngle)/2,ve=Math.cos(q),ge=Math.sin(q),$e,We,Te,Be;t=H.cx,s=H.cy;var Se=K==="inside"||K==="inner";if(K==="center")$e=H.cx,We=H.cy,Be="center";else{var Je=(Se?(H.r+H.r0)/2*ve:H.r*ve)+t,Ke=(Se?(H.r+H.r0)/2*ge:H.r*ge)+s;if($e=Je+ve*3,We=Ke+ge*3,!Se){var rt=Je+ve*(oe+h-H.r),lt=Ke+ge*(oe+h-H.r),ot=rt+(ve<0?-1:1)*ce,nt=lt;z==="edge"?$e=ve<0?f+re:f+y-re:$e=ot+(ve<0?-G:G),We=nt,Te=[[Je,Ke],[rt,lt],[ot,nt]]}Be=Se?"center":z==="edge"?ve>0?"right":"left":ve>0?"left":"right"}var je=Math.PI,Ee=0,He=ee.get("rotate");if(Mt(He))Ee=He*(je/180);else if(K==="center")Ee=0;else if(He==="radial"||He===!0){var vt=ve<0?-q+je:-q;Ee=vt}else if(He==="tangential"&&K!=="outside"&&K!=="outer"){var Pe=Math.atan2(ve,ge);Pe<0&&(Pe=je*2+Pe);var ht=ge>0;ht&&(Pe=je+Pe),Ee=Pe-je}if(p=!!Ee,ae.x=$e,ae.y=We,ae.rotation=Ee,ae.setStyle({verticalAlign:"middle"}),Se){ae.setStyle({align:Be});var Qe=ae.states.select;Qe&&(Qe.x+=ae.x,Qe.y+=ae.y)}else{var ze=ae.getBoundingRect().clone();ze.applyTransform(ae.getComputedTransform());var st=(ae.style.margin||0)+2.1;ze.y-=st/2,ze.height+=st,a.push({label:ae,labelLine:me,position:K,len:oe,len2:ce,minTurnAngle:te.get("minTurnAngle"),maxSurfaceAngle:te.get("maxSurfaceAngle"),surfaceNormal:new we(ve,ge),linePoints:Te,textAlign:Be,labelDistance:G,labelAlignTo:z,edgeDistance:re,bleedMargin:Q,rect:ze,unconstrainedWidth:ze.width,labelStyleWidth:ae.style.width})}N.setTextConfig({inside:Se})}}),!p&&o.get("avoidLabelOverlap")&&Ml(a,t,s,h,y,C,f,w);for(var L=0;L<a.length;L++){var T=a[L],F=T.label,V=T.labelLine,I=isNaN(F.x)||isNaN(F.y);if(F){F.setStyle({align:T.textAlign}),I&&(Ze(F.states,S),F.ignore=!0);var W=F.states.select;W&&(W.x+=F.x,W.y+=F.y)}if(V){var U=T.linePoints;I||!U?(Ze(V.states,S),V.ignore=!0):(rl(U,T.minTurnAngle),ll(U,T.surfaceNormal,T.maxSurfaceAngle),V.setShape({points:U}),F.__hostTarget.textGuideLineConfig={anchor:new we(U[0][0],U[0][1])})}}}var Tl=function(o){Oe(r,o);function r(a,t,s){var p=o.call(this)||this;p.z2=2;var c=new jr;return p.setTextContent(c),p.updateData(a,t,s,!0),p}return r.prototype.updateData=function(a,t,s,p){var c=this,m=a.hostModel,h=a.getItemModel(t),y=h.getModel("emphasis"),f=a.getItemLayout(t),w=Me(tt(h.getModel("itemStyle"),f,!0),f);if(isNaN(w.startAngle)){c.setShape(w);return}if(p){c.setShape(w);var C=m.getShallow("animationType");m.ecModel.ssr?(Ue(c,{scaleX:0,scaleY:0},m,{dataIndex:t,isFrom:!0}),c.originX=w.cx,c.originY=w.cy):C==="scale"?(c.shape.r=f.r0,Ue(c,{shape:{r:f.r}},m,t)):s!=null?(c.setShape({startAngle:s,endAngle:s}),Ue(c,{shape:{startAngle:f.startAngle,endAngle:f.endAngle}},m,t)):(c.shape.endAngle=f.startAngle,Ge(c,{shape:{endAngle:f.endAngle}},m,t))}else va(c),Ge(c,{shape:w},m,t);c.useStyle(a.getItemVisual(t,"style")),wa(c,h);var S=(f.startAngle+f.endAngle)/2,k=m.get("selectedOffset"),L=Math.cos(S)*k,T=Math.sin(S)*k,F=h.getShallow("cursor");F&&c.attr("cursor",F),this._updateLabel(m,a,t),c.ensureState("emphasis").shape=Me({r:f.r+(y.get("scale")&&y.get("scaleSize")||0)},tt(y.getModel("itemStyle"),f)),Me(c.ensureState("select"),{x:L,y:T,shape:tt(h.getModel(["select","itemStyle"]),f)}),Me(c.ensureState("blur"),{shape:tt(h.getModel(["blur","itemStyle"]),f)});var V=c.getTextGuideLine(),I=c.getTextContent();V&&Me(V.ensureState("select"),{x:L,y:T}),Me(I.ensureState("select"),{x:L,y:T}),_a(this,y.get("focus"),y.get("blurScope"),y.get("disabled"))},r.prototype._updateLabel=function(a,t,s){var p=this,c=t.getItemModel(s),m=c.getModel("labelLine"),h=t.getItemVisual(s,"style"),y=h&&h.fill,f=h&&h.opacity;ba(p,ya(c),{labelFetcher:t.hostModel,labelDataIndex:s,inheritColor:y,defaultOpacity:f,defaultText:a.getFormattedLabel(s,"normal")||t.getName(s)});var w=p.getTextContent();p.setTextConfig({position:null,rotation:null}),w.attr({z2:10});var C=a.get(["label","position"]);if(C!=="outside"&&C!=="outer")p.removeTextGuideLine();else{var S=this.getTextGuideLine();S||(S=new ca,this.setTextGuideLine(S)),nl(this,sl(c),{stroke:y,opacity:Hr(m.get(["lineStyle","opacity"]),f,1)})}},r}(ft),Pl=function(o){Oe(r,o);function r(){var a=o!==null&&o.apply(this,arguments)||this;return a.ignoreLabelLineUpdate=!0,a}return r.prototype.render=function(a,t,s,p){var c=a.getData(),m=this._data,h=this.group,y;if(!m&&c.count()>0){for(var f=c.getItemLayout(0),w=1;isNaN(f&&f.startAngle)&&w<c.count();++w)f=c.getItemLayout(w);f&&(y=f.startAngle)}if(this._emptyCircleSector&&h.remove(this._emptyCircleSector),c.count()===0&&a.get("showEmptyCircle")){var C=La(a),S=new ft({shape:Me(Ma(a,s),C)});S.useStyle(a.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=S,h.add(S)}c.diff(m).add(function(k){var L=new Tl(c,k,y);c.setItemGraphicEl(k,L),h.add(L)}).update(function(k,L){var T=m.getItemGraphicEl(L);T.updateData(c,k,y),T.off("click"),h.add(T),c.setItemGraphicEl(k,T)}).remove(function(k){var L=m.getItemGraphicEl(k);At(L,a,k)}).execute(),Ll(a),a.get("animationTypeUpdate")!=="expansion"&&(this._data=c)},r.prototype.dispose=function(){},r.prototype.containPoint=function(a,t){var s=t.getData(),p=s.getItemLayout(0);if(p){var c=a[0]-p.cx,m=a[1]-p.cy,h=Math.sqrt(c*c+m*m);return h<=p.r&&h>=p.r0}},r.type="pie",r}(ha);const Nl=Pl;function Rl(o,r,a){r=at(r)&&{coordDimensions:r}||Me({encodeDefine:o.getEncode()},r);var t=o.getSource(),s=zr(t,r).dimensions,p=new Yr(s,o);return p.initData(t,a),p}var El=function(){function o(r,a){this._getDataWithEncodedVisual=r,this._getRawData=a}return o.prototype.getAllNames=function(){var r=this._getRawData();return r.mapArray(r.getName)},o.prototype.containName=function(r){var a=this._getRawData();return a.indexOfName(r)>=0},o.prototype.indexOfName=function(r){var a=this._getDataWithEncodedVisual();return a.indexOfName(r)},o.prototype.getItemVisual=function(r,a){var t=this._getDataWithEncodedVisual();return t.getItemVisual(r,a)},o}();const Vl=El;var Fl=ka(),Ol=function(o){Oe(r,o);function r(){return o!==null&&o.apply(this,arguments)||this}return r.prototype.init=function(a){o.prototype.init.apply(this,arguments),this.legendVisualProvider=new Vl(Zt(this.getData,this),Zt(this.getRawData,this)),this._defaultLabelLine(a)},r.prototype.mergeOption=function(){o.prototype.mergeOption.apply(this,arguments)},r.prototype.getInitialData=function(){return Rl(this,{coordDimensions:["value"],encodeDefaulter:Tt(Ur,this)})},r.prototype.getDataParams=function(a){var t=this.getData(),s=Fl(t),p=s.seats;if(!p){var c=[];t.each(t.mapDimension("value"),function(h){c.push(h)}),p=s.seats=Gr(c,t.hostModel.get("percentPrecision"))}var m=o.prototype.getDataParams.call(this,a);return m.percent=p[a]||0,m.$vars.push("percent"),m},r.prototype._defaultLabelLine=function(a){Br(a,"labelLine",["show"]);var t=a.labelLine,s=a.emphasis.labelLine;t.show=t.show&&a.label.show,s.show=s.show&&a.emphasis.label.show},r.type="series.pie",r.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},r}(It);const $l=Ol;function Wl(o){return{seriesType:o,reset:function(r,a){var t=r.getData();t.filterSelf(function(s){var p=t.mapDimension("value"),c=t.get(p,s);return!(Mt(c)&&!isNaN(c)&&c<0)})}}}function jl(o){o.registerChartView(Nl),o.registerSeriesModel($l),Xr("pie",o.registerAction),o.registerLayout(Tt(Cl,"pie")),o.registerProcessor(Al("pie")),o.registerProcessor(Wl("pie"))}const Hl={class:"reports-page"},zl={class:"page-header"},Yl={class:"header-actions"},Ul={class:"stat-content"},Gl={class:"stat-value"},Bl={class:"stat-content"},Xl={class:"stat-value"},ql={class:"stat-content"},Zl={class:"stat-value"},Jl={class:"stat-content"},Kl={class:"stat-value"},Ql={class:"stat-content"},eo={class:"stat-value"},to={class:"stat-content"},ao={class:"stat-value"},ro={class:"stat-content"},lo={class:"stat-value"},oo={class:"stat-content"},no={class:"stat-value"},so={class:"stat-content"},io={class:"stat-value"},uo={class:"stat-content"},co={class:"stat-value"},po={class:"card-header"},mo={class:"record-count"},fo={class:"pagination"},vo={class:"dialog-header"},ho={class:"dialog-title"},go={class:"dialog-actions"},yo={class:"employee-detail-content"},bo={class:"summary-item"},_o={class:"summary-value"},wo={class:"summary-item"},Do={class:"summary-value"},ko={class:"summary-item"},So={class:"summary-value"},xo={class:"summary-item"},Co={class:"summary-value"},Ao={class:"dialog-header"},Io={class:"dialog-title"},Mo={class:"dialog-actions"},Lo={class:"project-detail-content"},To={class:"summary-item"},Po={class:"summary-value"},No={class:"summary-item"},Ro={class:"summary-value"},Eo={class:"summary-item"},Vo={class:"summary-value"},Fo={class:"summary-item"},Oo={class:"summary-value"},$o={class:"summary-item"},Wo={class:"summary-value"},jo={class:"summary-item"},Ho={class:"summary-value"},zo={class:"dialog-footer"},Yo={key:0},Uo={key:1},Go={key:2},Bo={key:3},Xo={key:4},qo={key:5,class:"empty-data"},Zo=lr({__name:"Reports",setup(o){qr([Zr,Jr,jl,xl,Kr,Qr,el,tl]);const r=le(!1),a=le(!1),t=le(!1),s=le([]),p=le([]),c=le([]),m=le([]),h=le([]),y=le(!1),f=le([]),w=le(!1),C=le(!1),S=le(!1),k=le(!1),L=le([]),T=le([]),F=le(null),V=le(null),I=le("daily"),W=le(""),U=le(!1),X=le(!1),N=et({format:"excel",content:"filtered",fields:[],filename:"",includeSummary:!0,includeCharts:!1,pdfMethod:"html2canvas",pdfLanguage:"english"}),H=et({totalEmployees:0,presentToday:0,absentToday:0,lateToday:0,monthlyWorkHours:0,abnormalRecords:0}),ae=le(!1),me=le(""),Z=le(!1),ee=le([]),K=le(""),G=et({projectId:null,userId:null,status:""}),z=le([]),re=et({page:1,size:20,total:0}),Q=et({totalDays:0,normalDays:0,abnormalDays:0,totalHours:0}),te=$t(()=>({title:{text:"出勤趋势",left:"center"},tooltip:{trigger:"axis"},legend:{data:["出勤人数","异常人数"],bottom:0},grid:{left:"3%",right:"4%",bottom:"10%",containLabel:!0},xAxis:{type:"category",data:ce.value.dates},yAxis:{type:"value"},series:[{name:"出勤人数",type:"line",data:ce.value.attendance,smooth:!0,itemStyle:{color:"#67C23A"}},{name:"异常人数",type:"line",data:ce.value.abnormal,smooth:!0,itemStyle:{color:"#F56C6C"}}]})),oe=$t(()=>({title:{text:"出勤分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"出勤统计",type:"pie",radius:"50%",data:[{value:Q.normalDays,name:"正常出勤",itemStyle:{color:"#67C23A"}},{value:Q.abnormalDays,name:"异常记录",itemStyle:{color:"#F56C6C"}},{value:H.absentToday,name:"缺勤",itemStyle:{color:"#909399"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]})),ce=le({dates:[],attendance:[],abnormal:[]}),q=d=>new Date(d).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),ve=d=>new Date(d).toLocaleString("zh-CN"),ge=d=>{if(!d)return"-";const e=Math.floor(d/60),g=d%60;return`${e}h${g}m`},$e=d=>{const{columns:e,data:g}=d,n=[];return e.forEach((b,_)=>{if(_===0){n[_]="合计";return}if(b.property==="totalWorkMinutes"){const v=g.reduce((P,E)=>P+(E.totalWorkMinutes||0),0);n[_]=ge(v)}else if(b.property==="checkInCount"||b.property==="checkOutCount"){const v=g.reduce((P,E)=>P+(E[b.property]||0),0);n[_]=v.toString()}else n[_]=""}),n},We=async()=>{try{const d=await sr();if(d.success){const e=d.data;console.log("实时数据API响应:",e),Object.assign(H,{totalEmployees:e.totalUsers||0,presentToday:e.todayPresentUsers||0,absentToday:e.todayAbsentUsers||0,lateToday:e.todayLateUsers||0,monthlyWorkHours:Math.round((e.monthlyTotalWorkMinutes||0)/60),abnormalRecords:e.todayAbnormalRecords||0}),console.log("更新后的实时数据:",H)}}catch(d){console.error("Load realtime data error:",d)}},Te=async d=>{console.log("点击卡片:",d),K.value=d,Z.value=!0,ae.value=!0;try{switch(d){case"presentToday":me.value="今日出勤员工列表";const e=await pr();e.success?ee.value=e.data||[]:B.error(e.message||"获取今日出勤员工列表失败");break;case"absentToday":me.value="今日缺勤员工列表";const g=await cr();g.success?ee.value=g.data||[]:B.error(g.message||"获取今日缺勤员工列表失败");break;case"lateToday":me.value="今日迟到员工列表";const n=await dr();n.success?ee.value=n.data||[]:B.error(n.message||"获取今日迟到员工列表失败");break;case"monthlyWorkHours":me.value="本月工时统计详情";const b=await ur();b.success?ee.value=b.data||[]:B.error(b.message||"获取本月工时统计详情失败");break;case"abnormalRecords":me.value="今日异常打卡记录";const _=await ir();_.success?ee.value=_.data||[]:B.error(_.message||"获取今日异常打卡记录失败");break;default:d==="totalEmployees"&&(window.open("/users","_blank"),ae.value=!1);break}}catch(e){console.error("获取详情数据失败:",e),B.error("获取详情数据失败")}finally{Z.value=!1}},Be=()=>{re.page=1,Se()},Se=async()=>{r.value=!0;try{I.value==="daily"?await Je():I.value==="monthly"?await Ke():I.value==="employee-project"&&await rt()}finally{r.value=!1}},Je=async()=>{var g,n,b;const d={page:re.page,limit:re.size,projectId:G.projectId,userId:G.userId,startDate:(g=z.value)==null?void 0:g[0],endDate:(n=z.value)==null?void 0:n[1]};G.status==="normal"?d.isAbnormal=!1:G.status==="abnormal"&&(d.isAbnormal=!0);const e=await bt(d);if(e.success){s.value=e.data.records||[],re.total=((b=e.data.pagination)==null?void 0:b.total)||0;const _=await mr(d);if(_.success){const v=_.data.summary;Q.totalDays=v.totalDays||0,Q.normalDays=v.normalDays||0,Q.abnormalDays=v.abnormalDays||0,Q.totalHours=Math.round((v.totalWorkMinutes||0)/60)}await lt()}else B.error(e.message||"加载报表数据失败")},Ke=async()=>{const[d,e]=W.value?W.value.split("-"):[new Date().getFullYear().toString(),(new Date().getMonth()+1).toString()],g={year:d,month:e,projectId:G.projectId,userId:G.userId},n=await Ht(g);if(n.success){const b=n.data;p.value=b.employees||[],re.total=p.value.length,p.value=p.value.map(_=>({..._,department:_.userRole||"未分配",totalWorkHours:parseFloat(_.totalWorkHours)||0,avgWorkHours:parseFloat(_.avgWorkHours)||0,attendanceRate:parseFloat(_.attendanceRate)||0})),b.summary&&(Q.totalDays=b.summary.totalAttendanceDays||0,Q.normalDays=b.summary.totalAttendanceDays-b.summary.totalAbnormalDays||0,Q.abnormalDays=b.summary.totalAbnormalDays||0,Q.totalHours=Math.round(p.value.reduce((_,v)=>_+v.totalWorkHours,0)))}else B.error(n.message||"加载月度数据失败")},rt=async()=>{var g,n,b;const d={page:re.page,limit:re.size,projectId:G.projectId,userId:G.userId,startDate:(g=z.value)==null?void 0:g[0],endDate:(n=z.value)==null?void 0:n[1]},e=await zt(d);if(e.success){const _=e.data.records||[];re.total=((b=e.data.pagination)==null?void 0:b.total)||0,c.value=_.map(M=>({...M,role:M.userRole||"员工",attendanceDays:M.totalDays||0,totalWorkHours:M.workHours||0,avgWorkHours:M.avgWorkHours||0,attendanceRate:parseFloat(M.attendanceRate)||0}));const v=c.value.reduce((M,Y)=>M+Y.attendanceDays,0),P=c.value.reduce((M,Y)=>M+Y.workDays,0),E=c.value.reduce((M,Y)=>M+Y.absentDays,0),$=c.value.reduce((M,Y)=>M+Y.totalWorkHours,0);Q.totalDays=v,Q.normalDays=P,Q.abnormalDays=E,Q.totalHours=Math.round($)}else B.error(e.message||"加载员工项目数据失败")},lt=async d=>{a.value=!0;try{const e=[],g=[],n=[];for(let b=6;b>=0;b--){const _=new Date;_.setDate(_.getDate()-b),e.push(_.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})),g.push(Math.floor(Math.random()*20)+10),n.push(Math.floor(Math.random()*5))}ce.value={dates:e,attendance:g,abnormal:n}}finally{a.value=!1}},ot=()=>{G.projectId=null,G.userId=null,G.status="",z.value=[],W.value="",re.page=1,Se()},nt=d=>{re.size=d,Se()},je=d=>{re.page=d,Se()},Ee=async d=>{try{const e=await Yt({userId:d.userId,date:d.date});e.success?(f.value=e.data||[],y.value=!0):B.error(e.message||"加载详细记录失败")}catch(e){console.error("Load detail records error:",e),B.error("加载详细记录失败")}},He=async d=>{F.value=d,S.value=!0,w.value=!0;try{const[e,g]=W.value?W.value.split("-"):[new Date().getFullYear().toString(),(new Date().getMonth()+1).toString()],n=await fr({userId:d.userId,year:e,month:g});if(n.success){const b=n.data.dailyRecords||[];if(L.value=b.map(_=>{var E,$,M,Y,R,ie;let v=null,P=null;if(_.pairs&&_.pairs.length>0&&(v=($=(E=_.pairs[0])==null?void 0:E.checkIn)==null?void 0:$.createdAt,P=(Y=(M=_.pairs[_.pairs.length-1])==null?void 0:M.checkOut)==null?void 0:Y.createdAt),!v&&_.unpaired&&_.unpaired.length>0){const O=_.unpaired.filter(ne=>ne.type==="in");O.length>0&&(v=(R=O[0].record)==null?void 0:R.createdAt)}if(!P&&_.unpaired&&_.unpaired.length>0){const O=_.unpaired.filter(ne=>ne.type==="out");O.length>0&&(P=(ie=O[O.length-1].record)==null?void 0:ie.createdAt)}return{date:_.date,projectName:"多项目",firstCheckIn:v,lastCheckOut:P,checkInCount:_.checkInCount||0,checkOutCount:_.checkOutCount||0,totalWorkMinutes:_.totalWorkMinutes||0,isAbnormal:_.isAbnormal||!1,userId:n.data.userId}}),n.data.summary){const _=n.data.summary;F.value={...F.value,attendanceDays:_.totalDays||0,normalDays:_.normalDays||0,abnormalDays:_.abnormalDays||0,totalWorkHours:_.totalWorkHours||"0.0"}}}else B.error(n.message||"加载员工详细记录失败"),L.value=[]}catch(e){console.error("Load employee daily records error:",e),B.error("加载员工详细记录失败"),L.value=[]}finally{S.value=!1}},vt=async d=>{var e,g;V.value=d,k.value=!0,C.value=!0;try{const n=await bt({userId:d.userId,projectId:d.projectId,startDate:(e=z.value)==null?void 0:e[0],endDate:(g=z.value)==null?void 0:g[1],page:1,limit:1e3});n.success?T.value=n.data.records||[]:(B.error(n.message||"加载项目详细记录失败"),T.value=[])}catch(n){console.error("Load project daily records error:",n),B.error("加载项目详细记录失败"),T.value=[]}finally{k.value=!1}},Pe=async d=>{var e,g;try{const n=await Yt({userId:d.userId||((e=F.value)==null?void 0:e.userId)||((g=V.value)==null?void 0:g.userId),date:d.date});n.success?(f.value=n.data||[],y.value=!0):B.error(n.message||"加载详细打卡记录失败")}catch(n){console.error("Load day detail records error:",n),B.error("加载详细打卡记录失败")}},ht=d=>{d==="custom"?Pa():Qe(d)},ze=async d=>{if(!F.value||!L.value.length){B.warning("没有可导出的数据");return}const[e,g]=W.value?W.value.split("-"):[new Date().getFullYear().toString(),(new Date().getMonth()+1).toString()],n=`${F.value.userName}_${e}年${g}月考勤详情_${new Date().toISOString().split("T")[0].replace(/-/g,"")}`;X.value=!0;try{await Ra({format:d,employee:F.value,dailyRecords:L.value,year:e,month:g,filename:n}),B.success("导出成功")}catch(b){console.error("Export error:",b),B.error("导出失败，请重试")}finally{X.value=!1}},st=async d=>{var _,v;if(!V.value||!T.value.length){B.warning("没有可导出的数据");return}const e=((_=z.value)==null?void 0:_[0])||"",g=((v=z.value)==null?void 0:v[1])||"",n=e&&g?`${e.replace(/-/g,"")}_${g.replace(/-/g,"")}`:new Date().toISOString().split("T")[0].replace(/-/g,""),b=`${V.value.userName}_${V.value.projectName}_项目考勤详情_${n}`;X.value=!0;try{await Ea({format:d,employeeProject:V.value,dailyRecords:T.value,startDate:e,endDate:g,filename:b}),B.success("导出成功")}catch(P){console.error("Export error:",P),B.error("导出失败，请重试")}finally{X.value=!1}},Qe=async d=>{const e=Rt();X.value=!0;try{await Pt({format:d,content:"filtered",fields:Nt(),filename:e,includeSummary:!0,includeCharts:d==="excel",pdfMethod:"html2canvas",pdfLanguage:"english"})}finally{X.value=!1}},Pa=()=>{N.format="excel",N.content="filtered",N.fields=Nt(),N.filename=Rt(),N.includeSummary=!0,N.includeCharts=!1,N.pdfMethod="html2canvas",N.pdfLanguage="english",U.value=!0},Na=async()=>{if(!N.filename.trim()){B.warning("请输入文件名");return}if(N.fields.length===0){B.warning("请至少选择一个导出字段");return}X.value=!0;try{await Pt(N),U.value=!1}finally{X.value=!1}},Pt=async d=>{var e,g;try{const n=await Va(d);if(!n.records||n.records.length===0){B.warning("没有数据可导出");return}await Wa(n,d);try{await vr({type:I.value,format:d.format,recordCount:n.records.length,filters:{startDate:(e=z.value)==null?void 0:e[0],endDate:(g=z.value)==null?void 0:g[1],selectedMonth:W.value,projectId:G.projectId,userId:G.userId,status:G.status}})}catch(b){console.warn("Backend export API call failed:",b)}}catch(n){console.error("Export error:",n),B.error("导出失败，请重试")}},Ra=async d=>{try{const{employee:e,dailyRecords:g,year:n,month:b,format:_,filename:v}=d,P={records:g,summary:{employeeName:e.userName,year:n,month:b,attendanceDays:e.attendanceDays||0,normalDays:e.normalDays||0,abnormalDays:e.abnormalDays||0,totalWorkHours:e.totalWorkHours||0,avgWorkHours:e.avgWorkHours||0,attendanceRate:e.attendanceRate||0},metadata:{exportTime:new Date().toISOString(),viewMode:"employee-detail",totalRecords:g.length,filters:{employeeName:e.userName,year:n,month:b}}};if(!P.records||P.records.length===0){B.warning("没有数据可导出");return}await ja(P,{format:_,filename:v})}catch(e){console.error("Employee export error:",e),B.error("导出失败，请重试")}},Ea=async d=>{try{const{employeeProject:e,dailyRecords:g,startDate:n,endDate:b,format:_,filename:v}=d,P={records:g,summary:{employeeName:e.userName,projectName:e.projectName,clientName:e.clientName,role:e.role,startDate:n,endDate:b,attendanceDays:e.attendanceDays||0,totalWorkHours:e.totalWorkHours||0,avgWorkHours:e.avgWorkHours||0,attendanceRate:e.attendanceRate||0},metadata:{exportTime:new Date().toISOString(),viewMode:"project-detail",totalRecords:g.length,filters:{employeeName:e.userName,projectName:e.projectName,startDate:n,endDate:b}}};if(!P.records||P.records.length===0){B.warning("没有数据可导出");return}await Ha(P,{format:_,filename:v})}catch(e){console.error("Project export error:",e),B.error("导出失败，请重试")}},Va=async d=>{var b,_,v,P;let e=[],g=null;if(d.content==="current")I.value==="daily"?e=s.value:I.value==="monthly"?e=p.value:I.value==="employee-project"&&(e=c.value);else if(d.content==="all"||d.content==="filtered"){if(I.value==="daily"){const E=await bt({projectId:G.projectId,userId:G.userId,startDate:(b=z.value)==null?void 0:b[0],endDate:(_=z.value)==null?void 0:_[1],page:1,limit:1e4});e=E.success?E.data.records||[]:[]}else if(I.value==="monthly"){const[E,$]=W.value?W.value.split("-"):[new Date().getFullYear().toString(),(new Date().getMonth()+1).toString()],M=await Ht({year:E,month:$,projectId:G.projectId,userId:G.userId});e=M.success?M.data.employees||[]:[],g=M.success?M.data.summary:null}else if(I.value==="employee-project"){const E=await zt({projectId:G.projectId,userId:G.userId,startDate:(v=z.value)==null?void 0:v[0],endDate:(P=z.value)==null?void 0:P[1],page:1,limit:1e4});e=E.success?E.data.records||[]:[]}}const n=e.map(E=>{const $={};return d.fields.forEach(M=>{$[M]=E[M]}),$});return{records:n,summary:d.includeSummary?g||Q:null,metadata:{viewMode:I.value,exportTime:new Date().toISOString(),totalRecords:n.length,filters:{dateRange:z.value,selectedMonth:W.value,projectId:G.projectId,userId:G.userId,status:G.status}}}},Fa=async()=>{var d;try{const e=await hr();e.success?m.value=((d=e.data)==null?void 0:d.projects)||[]:B.error(e.message||"加载工程列表失败")}catch(e){console.error("Load projects error:",e),B.error("加载工程列表失败")}},Oa=async()=>{var d;try{const e=await gr();e.success?h.value=((d=e.data)==null?void 0:d.users)||[]:B.error(e.message||"加载用户列表失败")}catch(e){console.error("Load users error:",e),B.error("加载用户列表失败")}},Nt=()=>I.value==="daily"?["date","userName","projectName","firstCheckIn","lastCheckOut","totalWorkMinutes","isAbnormal"]:I.value==="monthly"?["userName","department","attendanceDays","totalWorkHours","attendanceRate"]:I.value==="employee-project"?["userName","projectName","clientName","attendanceDays","totalWorkHours","avgWorkHours","attendanceRate"]:[],Rt=d=>{const e=new Date,g=e.toISOString().split("T")[0],n=e.toTimeString().split(" ")[0].replace(/:/g,"");let b="";return I.value==="daily"?b="考勤明细":I.value==="monthly"?b="月度统计":I.value==="employee-project"&&(b="员工项目统计"),`${b}_${g}_${n}`},$a=(d,e)=>{const g=document.createElement("a");g.href=d,g.download=e,document.body.appendChild(g),g.click(),document.body.removeChild(g)},Wa=async(d,e)=>{var g;console.log("开始前端导出:",{format:e.format,recordCount:(g=d.records)==null?void 0:g.length});try{if(e.format==="csv")gt(d,e);else if(e.format==="excel")await za(d,e);else if(e.format==="pdf")await Ya(d,e);else throw new Error(`不支持的导出格式: ${e.format}`)}catch(n){throw console.error("前端导出失败:",n),n}},ja=async(d,e)=>{var g;console.log("开始员工详情导出:",{format:e.format,recordCount:(g=d.records)==null?void 0:g.length});try{if(e.format==="excel")await Ka(d,e);else if(e.format==="pdf")await Qa(d,e);else throw new Error(`不支持的导出格式: ${e.format}`)}catch(n){throw console.error("员工详情导出失败:",n),n}},Ha=async(d,e)=>{var g;console.log("开始项目详情导出:",{format:e.format,recordCount:(g=d.records)==null?void 0:g.length});try{if(e.format==="excel")await er(d,e);else if(e.format==="pdf")await tr(d,e);else throw new Error(`不支持的导出格式: ${e.format}`)}catch(n){throw console.error("项目详情导出失败:",n),n}},gt=(d,e)=>{try{const g=d.records||[];if(g.length===0){B.warning("没有数据可导出");return}console.log("CSV导出开始:",{recordCount:g.length,fields:e.fields});const b=[e.fields.map(P=>it(P)).join(","),...g.map(P=>e.fields.map(E=>{let $=P[E]||"";return E==="totalWorkMinutes"?$=ge($):E==="isAbnormal"?$=$?"异常":"正常":E==="attendanceRate"?$=`${$}%`:E==="firstCheckIn"||E==="lastCheckOut"?$=$?q($):"-":E==="date"&&($=$?new Date($).toLocaleDateString("zh-CN"):""),typeof $=="string"&&($.includes(",")||$.includes('"')||$.includes(`
`))?$=`"${$.replace(/"/g,'""')}"`:$=`"${$}"`,$}).join(","))].join(`
`),_=new Blob(["\uFEFF"+b],{type:"text/csv;charset=utf-8;"}),v=URL.createObjectURL(_);$a(v,`${e.filename}.csv`),URL.revokeObjectURL(v),B.success(`CSV导出成功！共导出 ${g.length} 条记录`)}catch(g){throw console.error("CSV导出失败:",g),B.error("CSV导出失败"),g}},za=async(d,e)=>{var g;try{const n=await Ne(()=>import("./xlsx-6ed613d4.js"),[]),b=d.records||[];if(b.length===0){B.warning("没有数据可导出");return}const _=n.utils.book_new(),P=[e.fields.map(M=>it(M)),...b.map(M=>e.fields.map(Y=>{let R=M[Y]||"";return Y==="totalWorkMinutes"?R=ge(R):Y==="isAbnormal"?R=R?"异常":"正常":Y==="attendanceRate"?R=`${R}%`:Y==="firstCheckIn"||Y==="lastCheckOut"?R=R?q(R):"-":Y==="date"&&(R=R?new Date(R).toLocaleDateString("zh-CN"):""),R}))],E=n.utils.aoa_to_sheet(P),$=e.fields.map(M=>M==="date"?{wch:12}:M==="userName"?{wch:10}:M==="projectName"?{wch:20}:M==="firstCheckIn"||M==="lastCheckOut"?{wch:15}:M==="totalWorkMinutes"?{wch:12}:{wch:10});if(E["!cols"]=$,n.utils.book_append_sheet(_,E,"考勤数据"),e.includeSummary&&d.summary){const M=[["统计项目","数值"],["总天数",d.summary.totalDays||0],["正常天数",d.summary.normalDays||0],["异常天数",d.summary.abnormalDays||0],["总工时",`${d.summary.totalHours||0}小时`]];I.value==="monthly"&&d.summary.totalEmployees&&(M.push(["总员工数",d.summary.totalEmployees]),M.push(["平均出勤率",`${d.summary.avgAttendanceRate||0}%`]));const Y=n.utils.aoa_to_sheet(M);Y["!cols"]=[{wch:15},{wch:15}],n.utils.book_append_sheet(_,Y,"统计汇总")}if(d.metadata){const M=[["导出信息",""],["导出时间",new Date(d.metadata.exportTime).toLocaleString("zh-CN")],["数据类型",d.metadata.viewMode==="daily"?"按日统计":d.metadata.viewMode==="monthly"?"按月统计":"员工项目统计"],["记录总数",d.metadata.totalRecords],["",""],["筛选条件",""],["时间范围",((g=d.metadata.filters.dateRange)==null?void 0:g.join(" 至 "))||"全部"],["选择月份",d.metadata.filters.selectedMonth||"全部"],["工程筛选",d.metadata.filters.projectId?"已筛选":"全部"],["用户筛选",d.metadata.filters.userId?"已筛选":"全部"],["状态筛选",d.metadata.filters.status||"全部"]],Y=n.utils.aoa_to_sheet(M);Y["!cols"]=[{wch:12},{wch:25}],n.utils.book_append_sheet(_,Y,"导出信息")}n.writeFile(_,`${e.filename}.xlsx`),B.success("Excel导出成功！")}catch(n){console.error("Excel export error:",n),B.error("Excel导出失败，已改为CSV格式"),gt(d,e)}},Ya=async(d,e)=>{var g;try{if(console.log("PDF导出开始:",{options:e,recordCount:(g=d.records)==null?void 0:g.length}),(d.records||[]).length===0){B.warning("没有数据可导出");return}e.pdfMethod==="html2canvas"?await Ua(d,e):await Ba(d,e)}catch(n){console.error("PDF export error:",n),B.error("PDF导出失败，已改为CSV格式"),gt(d,e)}},Ua=async(d,e)=>{const g=(await Ne(()=>import("./html2canvas.esm-e0a7d97b.js"),[])).default,n=(await Ne(()=>import("./jspdf.es.min-3a1f04d2.js").then(v=>v.j),["assets/jspdf.es.min-3a1f04d2.js","assets/index-4256ff3d.js","assets/index-b50506e3.css"])).default,b=Ga(d,e),_=document.createElement("div");_.innerHTML=b,_.style.position="absolute",_.style.left="-9999px",_.style.top="-9999px",_.style.width="1200px",_.style.backgroundColor="white",_.style.padding="20px",_.style.fontFamily='Arial, "Microsoft YaHei", "SimHei", sans-serif',document.body.appendChild(_);try{const v=await g(_,{scale:2,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff"}),P=new n({orientation:"landscape",unit:"mm",format:"a4"}),E=v.toDataURL("image/png"),$=277,M=v.height*$/v.width,Y=190;let R=M,ie=10;for(P.addImage(E,"PNG",10,ie,$,M),R-=Y;R>=0;)ie=R-M+10,P.addPage(),P.addImage(E,"PNG",10,ie,$,M),R-=Y;P.save(`${e.filename}.pdf`),B.success("PDF导出成功！（HTML转图片方式，完美支持中文）")}finally{document.body.removeChild(_)}},Ga=(d,e)=>{const g=d.records||[],b={daily:"考勤明细报表",monthly:"月度统计报表","employee-project":"员工项目统计报表"}[I.value]||"考勤报表",v=e.fields.map($=>it($)).map($=>`<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: center;">${$}</th>`).join(""),P=g.map($=>`<tr>${e.fields.map(Y=>{let R=$[Y]||"";return Y==="totalWorkMinutes"?R=ge(R):Y==="isAbnormal"?R=R?"异常":"正常":Y==="attendanceRate"?R=`${R}%`:Y==="firstCheckIn"||Y==="lastCheckOut"?R=R?q(R):"-":Y==="date"&&(R=R?new Date(R).toLocaleDateString("zh-CN"):""),`<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${R}</td>`}).join("")}</tr>`).join("");let E="";return e.includeSummary&&d.summary&&(E=`
      <div style="margin-top: 30px;">
        <h3 style="color: #333; margin-bottom: 15px;">统计汇总</h3>
        <table style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; width: 150px;"><strong>总天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${d.summary.totalDays||0}</td>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; width: 150px;"><strong>正常天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${d.summary.normalDays||0}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>异常天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${d.summary.abnormalDays||0}</td>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>总工时</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${d.summary.totalHours||0}小时</td>
          </tr>
        </table>
      </div>
    `),`
    <div style="font-family: Arial, 'Microsoft YaHei', 'SimHei', sans-serif; color: #333;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333; margin: 0; font-size: 24px;">${b}</h1>
        <p style="color: #666; margin: 10px 0; font-size: 14px;">导出时间: ${new Date().toLocaleString("zh-CN")}</p>
      </div>

      <table style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
        <thead>
          <tr>${v}</tr>
        </thead>
        <tbody>
          ${P}
        </tbody>
      </table>

      ${E}

      <div style="margin-top: 30px; text-align: center; color: #999; font-size: 12px;">
        <p>共 ${g.length} 条记录</p>
      </div>
    </div>
  `},Ba=async(d,e)=>{const g=(await Ne(()=>import("./jspdf.es.min-3a1f04d2.js").then(R=>R.j),["assets/jspdf.es.min-3a1f04d2.js","assets/index-4256ff3d.js","assets/index-b50506e3.css"])).default,n=d.records||[],b=new g({orientation:"landscape",unit:"mm",format:"a4"});b.setFont("helvetica");const _=e.pdfLanguage==="english";b.setFontSize(16);let v="";_?v={daily:"Daily Attendance Report",monthly:"Monthly Attendance Report","employee-project":"Employee Project Report"}[I.value]||"Attendance Report":v=I.value==="daily"?"考勤明细报表":I.value==="monthly"?"月度统计报表":"员工项目统计报表",b.text(v,20,20),b.setFontSize(10);const P=_?`Export Time: ${new Date().toISOString().replace("T"," ").substring(0,19)}`:`导出时间: ${new Date().toLocaleString("zh-CN")}`;b.text(P,20,30);const E=_?e.fields.map(R=>Xa(R)):e.fields.map(R=>it(R)),$=n.map(R=>e.fields.map(ie=>{let O=R[ie]||"";return ie==="totalWorkMinutes"?O=ge(O):ie==="isAbnormal"?O=_?O?"Abnormal":"Normal":O?"异常":"正常":ie==="attendanceRate"?O=`${O}%`:ie==="firstCheckIn"||ie==="lastCheckOut"?O=O?q(O):"-":ie==="date"&&(O=O?new Date(O).toLocaleDateString(_?"en-US":"zh-CN"):""),_&&typeof O=="string"&&(O=qa(O)),String(O)})),M=(await Ne(()=>import("./jspdf.plugin.autotable-9efb0360.js"),[])).default;if(M(b,{head:[E],body:$,startY:40,styles:{fontSize:8,cellPadding:2,font:"helvetica"},headStyles:{fillColor:[64,158,255],textColor:255,font:"helvetica",fontStyle:"bold"},alternateRowStyles:{fillColor:[245,245,245]},margin:{top:40,left:20,right:20}}),e.includeSummary&&d.summary){const R=b.lastAutoTable.finalY+20;b.setFontSize(12);const ie=_?"Summary Statistics":"统计汇总";b.text(ie,20,R);const O=_?[["Total Days",String(d.summary.totalDays||0)],["Normal Days",String(d.summary.normalDays||0)],["Abnormal Days",String(d.summary.abnormalDays||0)],["Total Hours",`${d.summary.totalHours||0}h`]]:[["总天数",String(d.summary.totalDays||0)],["正常天数",String(d.summary.normalDays||0)],["异常天数",String(d.summary.abnormalDays||0)],["总工时",`${d.summary.totalHours||0}小时`]];I.value==="monthly"&&d.summary.totalEmployees&&(_?(O.push(["Total Employees",String(d.summary.totalEmployees)]),O.push(["Avg Attendance Rate",`${d.summary.avgAttendanceRate||0}%`])):(O.push(["总员工数",String(d.summary.totalEmployees)]),O.push(["平均出勤率",`${d.summary.avgAttendanceRate||0}%`]))),M(b,{head:[_?["Item","Value"]:["统计项目","数值"]],body:O,startY:R+10,styles:{fontSize:10,cellPadding:3,font:"helvetica"},headStyles:{fillColor:[67,194,58],textColor:255,font:"helvetica",fontStyle:"bold"},margin:{left:20}})}b.save(`${e.filename}.pdf`);const Y=_?"PDF导出成功！（文本模式-英文版本）":"PDF导出成功！（文本模式-中文版本，可能存在字体显示问题）";B.success(Y)},it=d=>({date:"日期",userName:"姓名",projectName:"工程",firstCheckIn:"首次签到",lastCheckOut:"最后签退",checkInCount:"签到次数",checkOutCount:"签退次数",totalWorkMinutes:"工作时长",isAbnormal:"状态",department:"部门",attendanceDays:"出勤天数",absentDays:"缺勤天数",abnormalDays:"异常天数",totalWorkHours:"总工时",avgWorkHours:"平均工时",attendanceRate:"出勤率",clientName:"客户",role:"角色"})[d]||d,Xa=d=>({date:"Date",userName:"Name",projectName:"Project",firstCheckIn:"First Check-in",lastCheckOut:"Last Check-out",checkInCount:"Check-in Count",checkOutCount:"Check-out Count",totalWorkMinutes:"Work Duration",isAbnormal:"Status",department:"Department",attendanceDays:"Attendance Days",absentDays:"Absent Days",abnormalDays:"Abnormal Days",totalWorkHours:"Total Hours",avgWorkHours:"Avg Hours",attendanceRate:"Attendance Rate",clientName:"Client",role:"Role"})[d]||d,qa=d=>{if(!d||typeof d!="string")return d;const e={张经理:"Zhang Manager",李工程师:"Li Engineer",王主管:"Wang Supervisor",CBD商业中心建设:"CBD Commercial Center Construction",住宅小区项目:"Residential Community Project",办公楼建设:"Office Building Construction",工程部:"Engineering Dept",管理部:"Management Dept",财务部:"Finance Dept",人事部:"HR Dept",项目经理:"Project Manager",工程师:"Engineer",技术员:"Technician",管理员:"Administrator",员工:"Employee",恒大集团:"Evergrande Group",万科集团:"Vanke Group",碧桂园:"Country Garden",正常:"Normal",异常:"Abnormal",迟到:"Late",早退:"Early Leave",缺勤:"Absent"};if(e[d])return e[d];let g=d;return Object.entries(e).forEach(([n,b])=>{g=g.replace(new RegExp(n,"g"),b)}),/[\u4e00-\u9fa5]/.test(g)&&(g=g.replace(/[\u4e00-\u9fa5]/g,n=>`U${n.charCodeAt(0).toString(16).toUpperCase()}`)),g},Za=()=>{const d=new Date;W.value=`${d.getFullYear()}-${String(d.getMonth()+1).padStart(2,"0")}`};or(()=>{Za(),We(),Fa(),Oa(),Se(),setInterval(We,3e4)});const Ja=async()=>{t.value=!0;try{const[d,e]=W.value?W.value.split("-"):[new Date().getFullYear().toString(),(new Date().getMonth()+1).toString()],g=`${d}-${String(e).padStart(2,"0")}-01`,n=new Date(parseInt(d),parseInt(e),0).getDate(),b=`${d}-${String(e).padStart(2,"0")}-${String(n).padStart(2,"0")}`,_=await yr({startDate:g,endDate:b});_.success?(B.success(`${d}年${e}月考勤数据重新计算完成！`),await Se()):B.error(_.message||"重新计算失败")}catch(d){console.error("Recalculate error:",d),B.error("重新计算失败，请重试")}finally{t.value=!1}},Ka=async(d,e)=>{try{const g=await Ne(()=>import("./xlsx-6ed613d4.js"),[]),{summary:n,records:b}=d,_=g.utils.book_new(),v=[];if(v.push(["员工考勤详情报表"]),v.push([]),v.push(["员工姓名",n.employeeName]),v.push(["统计月份",`${n.year}年${n.month}月`]),v.push(["出勤天数",n.attendanceDays]),v.push(["正常天数",n.normalDays]),v.push(["异常天数",n.abnormalDays]),v.push(["总工时",`${n.totalWorkHours}小时`]),v.push(["平均工时",`${n.avgWorkHours}小时/天`]),v.push(["出勤率",`${n.attendanceRate}%`]),v.push([]),v.push(["导出时间",new Date().toLocaleString("zh-CN")]),v.push([]),v.push([]),b&&b.length>0){v.push(["每日详细考勤记录"]),v.push([]);const $=["日期","项目名称","首次签到","最后签退","签到次数","签退次数","工作时长","状态"];v.push($),b.forEach(M=>{v.push([M.date||"",M.projectName||"未分配项目",M.firstCheckIn?q(M.firstCheckIn):"-",M.lastCheckOut?q(M.lastCheckOut):"-",M.checkInCount||0,M.checkOutCount||0,ge(M.totalWorkMinutes||0),M.isAbnormal?"异常":"正常"])})}const P=g.utils.aoa_to_sheet(v),E=[{wch:12},{wch:20},{wch:12},{wch:12},{wch:10},{wch:10},{wch:12},{wch:8}];P["!cols"]=E,P.A1&&(P.A1.s={font:{bold:!0,sz:16},alignment:{horizontal:"center"}}),P["!merges"]=[{s:{r:0,c:0},e:{r:0,c:7}}],g.utils.book_append_sheet(_,P,"考勤详情"),g.writeFile(_,`${e.filename}.xlsx`),console.log("员工Excel导出完成")}catch(g){throw console.error("员工Excel导出失败:",g),g}},Qa=async(d,e)=>{try{const g=await Ne(()=>import("./jspdf.es.min-3a1f04d2.js").then(O=>O.j),["assets/jspdf.es.min-3a1f04d2.js","assets/index-4256ff3d.js","assets/index-b50506e3.css"]),n=new g.default("p","mm","a4"),{summary:b,records:_}=d;let v=20;n.setFontSize(18),n.setFont("helvetica","bold");const P=`${b.employeeName} - ${b.year}年${b.month}月考勤详情`,E=n.getTextWidth(P);n.text(P,(210-E)/2,v),v+=20,n.setLineWidth(.5),n.line(20,v,190,v),v+=15,n.setFontSize(12),n.setFont("helvetica","bold"),n.text("考勤汇总信息",20,v),v+=10,n.setFont("helvetica","normal"),n.setFontSize(10);const $=[["出勤天数",`${b.attendanceDays}天`,"正常天数",`${b.normalDays}天`],["异常天数",`${b.abnormalDays}天`,"出勤率",`${b.attendanceRate}%`],["总工时",`${b.totalWorkHours}小时`,"平均工时",`${b.avgWorkHours}小时/天`]],M=42,Y=8;let R=20;if($.forEach((O,ne)=>{let J=R;O.forEach((D,ue)=>{n.rect(J,v,M,Y),ue%2===0&&(n.setFillColor(240,240,240),n.rect(J,v,M,Y,"F")),n.text(D,J+2,v+5),J+=M}),v+=Y}),v+=15,_&&_.length>0){n.setFontSize(12),n.setFont("helvetica","bold"),n.text("每日详细考勤记录",20,v),v+=10,n.setFontSize(9),n.setFont("helvetica","bold");const O=["日期","项目","首次签到","最后签退","签到次数","签退次数","工作时长","状态"],ne=[22,30,20,20,16,16,20,16];let J=20;n.setFillColor(220,220,220),O.forEach((D,ue)=>{n.rect(J,v,ne[ue],8,"F"),n.rect(J,v,ne[ue],8),n.text(D,J+1,v+5),J+=ne[ue]}),v+=8,n.setFont("helvetica","normal"),n.setFontSize(8),_.forEach((D,ue)=>{v>270&&(n.addPage(),v=20,n.setFontSize(9),n.setFont("helvetica","bold"),J=20,n.setFillColor(220,220,220),O.forEach((xe,fe)=>{n.rect(J,v,ne[fe],8,"F"),n.rect(J,v,ne[fe],8),n.text(xe,J+1,v+5),J+=ne[fe]}),v+=8,n.setFont("helvetica","normal"),n.setFontSize(8)),J=20;const De=[D.date||"",(D.projectName||"未分配").substring(0,12),D.firstCheckIn?q(D.firstCheckIn):"-",D.lastCheckOut?q(D.lastCheckOut):"-",(D.checkInCount||0).toString(),(D.checkOutCount||0).toString(),ge(D.totalWorkMinutes||0),D.isAbnormal?"异常":"正常"];ue%2===1&&(n.setFillColor(248,248,248),n.rect(20,v,160,6,"F")),De.forEach((xe,fe)=>{n.rect(J,v,ne[fe],6),fe===7&&xe==="异常"?n.setTextColor(255,0,0):fe===7&&xe==="正常"?n.setTextColor(0,128,0):n.setTextColor(0,0,0),n.text(xe,J+1,v+4),J+=ne[fe]}),n.setTextColor(0,0,0),v+=6})}const ie=n.internal.getNumberOfPages();for(let O=1;O<=ie;O++)n.setPage(O),n.setFontSize(8),n.setTextColor(128,128,128),n.text(`导出时间: ${new Date().toLocaleString("zh-CN")}`,20,290),n.text(`第 ${O} 页，共 ${ie} 页`,160,290);n.save(`${e.filename}.pdf`),console.log("员工PDF导出完成")}catch(g){throw console.error("员工PDF导出失败:",g),g}},er=async(d,e)=>{try{const g=await Ne(()=>import("./xlsx-6ed613d4.js"),[]),{summary:n,records:b}=d,_=g.utils.book_new(),v=[];if(v.push(["员工项目考勤详情报表"]),v.push([]),v.push(["员工姓名",n.employeeName]),v.push(["项目名称",n.projectName]),v.push(["客户名称",n.clientName]),v.push(["项目角色",n.role]),v.push(["统计时间",`${n.startDate} 至 ${n.endDate}`]),v.push(["出勤天数",n.attendanceDays]),v.push(["总工时",`${n.totalWorkHours}小时`]),v.push(["平均工时",`${n.avgWorkHours}小时/天`]),v.push(["出勤率",`${n.attendanceRate}%`]),v.push([]),v.push(["导出时间",new Date().toLocaleString("zh-CN")]),v.push([]),v.push([]),b&&b.length>0){v.push(["每日详细考勤记录"]),v.push([]);const $=["日期","项目名称","首次签到","最后签退","签到次数","签退次数","工作时长","状态"];v.push($),b.forEach(M=>{v.push([M.date||"",M.projectName||"未分配项目",M.firstCheckIn?q(M.firstCheckIn):"-",M.lastCheckOut?q(M.lastCheckOut):"-",M.checkInCount||0,M.checkOutCount||0,ge(M.totalWorkMinutes||0),M.isAbnormal?"异常":"正常"])})}const P=g.utils.aoa_to_sheet(v),E=[{wch:12},{wch:20},{wch:12},{wch:12},{wch:10},{wch:10},{wch:12},{wch:8}];P["!cols"]=E,P.A1&&(P.A1.s={font:{bold:!0,sz:16},alignment:{horizontal:"center"}}),P["!merges"]=[{s:{r:0,c:0},e:{r:0,c:7}}],g.utils.book_append_sheet(_,P,"项目考勤详情"),g.writeFile(_,`${e.filename}.xlsx`),console.log("项目Excel导出完成")}catch(g){throw console.error("项目Excel导出失败:",g),g}},tr=async(d,e)=>{try{const g=await Ne(()=>import("./jspdf.es.min-3a1f04d2.js").then(O=>O.j),["assets/jspdf.es.min-3a1f04d2.js","assets/index-4256ff3d.js","assets/index-b50506e3.css"]),n=new g.default("p","mm","a4"),{summary:b,records:_}=d;let v=20;n.setFontSize(18),n.setFont("helvetica","bold");const P=`${b.employeeName} - ${b.projectName} 项目考勤详情`,E=n.getTextWidth(P);n.text(P,(210-E)/2,v),v+=20,n.setLineWidth(.5),n.line(20,v,190,v),v+=15,n.setFontSize(12),n.setFont("helvetica","bold"),n.text("项目汇总信息",20,v),v+=10,n.setFont("helvetica","normal"),n.setFontSize(10);const $=[["员工姓名",b.employeeName,"项目名称",b.projectName],["客户名称",b.clientName,"项目角色",b.role],["统计时间",`${b.startDate} 至 ${b.endDate}`,"",""],["出勤天数",`${b.attendanceDays}天`,"出勤率",`${b.attendanceRate}%`],["总工时",`${b.totalWorkHours}小时`,"平均工时",`${b.avgWorkHours}小时/天`]],M=42,Y=8;let R=20;if($.forEach((O,ne)=>{let J=R;O.forEach((D,ue)=>{D&&(n.rect(J,v,M,Y),ue%2===0&&(n.setFillColor(240,240,240),n.rect(J,v,M,Y,"F")),n.text(D,J+2,v+5)),J+=M}),v+=Y}),v+=15,_&&_.length>0){n.setFontSize(12),n.setFont("helvetica","bold"),n.text("每日详细考勤记录",20,v),v+=10,n.setFontSize(9),n.setFont("helvetica","bold");const O=["日期","项目","首次签到","最后签退","签到次数","签退次数","工作时长","状态"],ne=[22,30,20,20,16,16,20,16];let J=20;n.setFillColor(220,220,220),O.forEach((D,ue)=>{n.rect(J,v,ne[ue],8,"F"),n.rect(J,v,ne[ue],8),n.text(D,J+1,v+5),J+=ne[ue]}),v+=8,n.setFont("helvetica","normal"),n.setFontSize(8),_.forEach((D,ue)=>{v>270&&(n.addPage(),v=20,n.setFontSize(9),n.setFont("helvetica","bold"),J=20,n.setFillColor(220,220,220),O.forEach((xe,fe)=>{n.rect(J,v,ne[fe],8,"F"),n.rect(J,v,ne[fe],8),n.text(xe,J+1,v+5),J+=ne[fe]}),v+=8,n.setFont("helvetica","normal"),n.setFontSize(8)),J=20;const De=[D.date||"",(D.projectName||"未分配").substring(0,12),D.firstCheckIn?q(D.firstCheckIn):"-",D.lastCheckOut?q(D.lastCheckOut):"-",(D.checkInCount||0).toString(),(D.checkOutCount||0).toString(),ge(D.totalWorkMinutes||0),D.isAbnormal?"异常":"正常"];ue%2===1&&(n.setFillColor(248,248,248),n.rect(20,v,160,6,"F")),De.forEach((xe,fe)=>{n.rect(J,v,ne[fe],6),fe===7&&xe==="异常"?n.setTextColor(255,0,0):fe===7&&xe==="正常"?n.setTextColor(0,128,0):n.setTextColor(0,0,0),n.text(xe,J+1,v+4),J+=ne[fe]}),n.setTextColor(0,0,0),v+=6})}const ie=n.internal.getNumberOfPages();for(let O=1;O<=ie;O++)n.setPage(O),n.setFontSize(8),n.setTextColor(128,128,128),n.text(`导出时间: ${new Date().toLocaleString("zh-CN")}`,20,290),n.text(`第 ${O} 页，共 ${ie} 页`,160,290);n.save(`${e.filename}.pdf`),console.log("项目PDF导出完成")}catch(g){throw console.error("项目PDF导出失败:",g),g}};return(d,e)=>{var Vt,Ft,Ot;const g=de("el-button"),n=de("el-icon"),b=de("el-dropdown-item"),_=de("el-dropdown-menu"),v=de("el-dropdown"),P=de("el-card"),E=de("el-col"),$=de("el-row"),M=de("el-radio-button"),Y=de("el-radio-group"),R=de("el-form-item"),ie=de("el-date-picker"),O=de("el-option"),ne=de("el-select"),J=de("el-form"),D=de("el-table-column"),ue=de("el-tag"),De=de("el-table"),xe=de("el-pagination"),fe=de("el-dialog"),Ae=de("el-radio"),pe=de("el-checkbox"),yt=de("el-checkbox-group"),ar=de("el-input"),Et=de("el-switch"),rr=de("el-empty"),Xe=nr("loading");return se(),Ie("div",Hl,[x("div",zl,[e[37]||(e[37]=x("h2",null,"考勤报表",-1)),x("div",Yl,[l(g,{type:"warning",loading:t.value,onClick:Ja,style:{"margin-right":"10px"}},{default:i(()=>e[31]||(e[31]=[A(" 重新计算当前月数据 ")])),_:1,__:[31]},8,["loading"]),l(v,{onCommand:ht,trigger:"click"},{dropdown:i(()=>[l(_,null,{default:i(()=>[l(b,{command:"excel"},{default:i(()=>[l(n,null,{default:i(()=>[l(Ce(br))]),_:1}),e[33]||(e[33]=A(" 导出Excel "))]),_:1,__:[33]}),l(b,{command:"csv"},{default:i(()=>[l(n,null,{default:i(()=>[l(Ce(_r))]),_:1}),e[34]||(e[34]=A(" 导出CSV "))]),_:1,__:[34]}),l(b,{command:"pdf"},{default:i(()=>[l(n,null,{default:i(()=>[l(Ce(wr))]),_:1}),e[35]||(e[35]=A(" 导出PDF "))]),_:1,__:[35]}),l(b,{divided:"",command:"custom"},{default:i(()=>[l(n,null,{default:i(()=>[l(Ce(Dr))]),_:1}),e[36]||(e[36]=A(" 自定义导出 "))]),_:1,__:[36]})]),_:1})]),default:i(()=>[l(g,{type:"success",icon:Ce(_t)},{default:i(()=>[e[32]||(e[32]=A(" 导出报表 ")),l(n,{class:"el-icon--right"},{default:i(()=>[l(Ce(wt))]),_:1})]),_:1,__:[32]},8,["icon"])]),_:1})])]),l($,{gutter:20,class:"realtime-stats"},{default:i(()=>[l(E,{span:4},{default:i(()=>[l(P,{class:"stat-card realtime clickable",onClick:e[0]||(e[0]=u=>Te("totalEmployees"))},{default:i(()=>[x("div",Ul,[e[38]||(e[38]=x("div",{class:"stat-icon"},"👥",-1)),x("div",Gl,j(H.totalEmployees),1),e[39]||(e[39]=x("div",{class:"stat-label"},"总员工数",-1))])]),_:1})]),_:1}),l(E,{span:4},{default:i(()=>[l(P,{class:"stat-card realtime clickable",onClick:e[1]||(e[1]=u=>Te("presentToday"))},{default:i(()=>[x("div",Bl,[e[40]||(e[40]=x("div",{class:"stat-icon"},"✅",-1)),x("div",Xl,j(H.presentToday),1),e[41]||(e[41]=x("div",{class:"stat-label"},"今日出勤",-1))])]),_:1})]),_:1}),l(E,{span:4},{default:i(()=>[l(P,{class:"stat-card realtime clickable",onClick:e[2]||(e[2]=u=>Te("absentToday"))},{default:i(()=>[x("div",ql,[e[42]||(e[42]=x("div",{class:"stat-icon"},"❌",-1)),x("div",Zl,j(H.absentToday),1),e[43]||(e[43]=x("div",{class:"stat-label"},"今日缺勤",-1))])]),_:1})]),_:1}),l(E,{span:4},{default:i(()=>[l(P,{class:"stat-card realtime clickable",onClick:e[3]||(e[3]=u=>Te("lateToday"))},{default:i(()=>[x("div",Jl,[e[44]||(e[44]=x("div",{class:"stat-icon"},"⏰",-1)),x("div",Kl,j(H.lateToday),1),e[45]||(e[45]=x("div",{class:"stat-label"},"今日迟到",-1))])]),_:1})]),_:1}),l(E,{span:4},{default:i(()=>[l(P,{class:"stat-card realtime clickable",onClick:e[4]||(e[4]=u=>Te("monthlyWorkHours"))},{default:i(()=>[x("div",Ql,[e[46]||(e[46]=x("div",{class:"stat-icon"},"📊",-1)),x("div",eo,j(H.monthlyWorkHours)+"h",1),e[47]||(e[47]=x("div",{class:"stat-label"},"本月总工时",-1))])]),_:1})]),_:1}),l(E,{span:4},{default:i(()=>[l(P,{class:"stat-card realtime clickable",onClick:e[5]||(e[5]=u=>Te("abnormalRecords"))},{default:i(()=>[x("div",to,[e[48]||(e[48]=x("div",{class:"stat-icon"},"⚠️",-1)),x("div",ao,j(H.abnormalRecords),1),e[49]||(e[49]=x("div",{class:"stat-label"},"今日异常打卡",-1))])]),_:1})]),_:1})]),_:1}),l(P,{class:"filter-card"},{default:i(()=>[l(J,{model:G,inline:""},{default:i(()=>[l(R,{label:"视图模式"},{default:i(()=>[l(Y,{modelValue:I.value,"onUpdate:modelValue":e[6]||(e[6]=u=>I.value=u),onChange:Be},{default:i(()=>[l(M,{value:"daily"},{default:i(()=>e[50]||(e[50]=[A("按日统计")])),_:1,__:[50]}),l(M,{value:"monthly"},{default:i(()=>e[51]||(e[51]=[A("按月统计")])),_:1,__:[51]}),l(M,{value:"employee-project"},{default:i(()=>e[52]||(e[52]=[A("员工项目")])),_:1,__:[52]})]),_:1},8,["modelValue"])]),_:1}),I.value==="daily"?(se(),be(R,{key:0,label:"时间范围"},{default:i(()=>[l(ie,{modelValue:z.value,"onUpdate:modelValue":e[7]||(e[7]=u=>z.value=u),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})):ye("",!0),I.value==="monthly"?(se(),be(R,{key:1,label:"选择月份"},{default:i(()=>[l(ie,{modelValue:W.value,"onUpdate:modelValue":e[8]||(e[8]=u=>W.value=u),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM"},null,8,["modelValue"])]),_:1})):ye("",!0),I.value==="employee-project"?(se(),be(R,{key:2,label:"时间范围"},{default:i(()=>[l(ie,{modelValue:z.value,"onUpdate:modelValue":e[9]||(e[9]=u=>z.value=u),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})):ye("",!0),l(R,{label:"工程"},{default:i(()=>[l(ne,{modelValue:G.projectId,"onUpdate:modelValue":e[10]||(e[10]=u=>G.projectId=u),placeholder:"请选择工程",clearable:""},{default:i(()=>[(se(!0),Ie(Wt,null,jt(m.value,u=>(se(),be(O,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(R,{label:"用户"},{default:i(()=>[l(ne,{modelValue:G.userId,"onUpdate:modelValue":e[11]||(e[11]=u=>G.userId=u),placeholder:"请选择用户",clearable:"",filterable:""},{default:i(()=>[(se(!0),Ie(Wt,null,jt(h.value,u=>(se(),be(O,{key:u.id,label:u.realName,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),I.value==="daily"?(se(),be(R,{key:3,label:"状态"},{default:i(()=>[l(ne,{modelValue:G.status,"onUpdate:modelValue":e[12]||(e[12]=u=>G.status=u),placeholder:"请选择状态",clearable:""},{default:i(()=>[l(O,{label:"全部",value:""}),l(O,{label:"正常",value:"normal"}),l(O,{label:"异常",value:"abnormal"})]),_:1},8,["modelValue"])]),_:1})):ye("",!0),l(R,null,{default:i(()=>[l(g,{type:"primary",onClick:Se},{default:i(()=>e[53]||(e[53]=[A("查询")])),_:1,__:[53]}),l(g,{onClick:ot},{default:i(()=>e[54]||(e[54]=[A("重置")])),_:1,__:[54]})]),_:1})]),_:1},8,["model"])]),_:1}),I.value!=="employee-project"?(se(),be($,{key:0,gutter:20,class:"charts-row"},{default:i(()=>[l(E,{span:12},{default:i(()=>[l(P,null,{header:i(()=>e[55]||(e[55]=[x("span",null,"出勤趋势图",-1)])),default:i(()=>[l(Ce(Jt),{class:"chart",option:te.value,loading:a.value,autoresize:""},null,8,["option","loading"])]),_:1})]),_:1}),l(E,{span:12},{default:i(()=>[l(P,null,{header:i(()=>e[56]||(e[56]=[x("span",null,"出勤分布",-1)])),default:i(()=>[l(Ce(Jt),{class:"chart",option:oe.value,loading:a.value,autoresize:""},null,8,["option","loading"])]),_:1})]),_:1})]),_:1})):ye("",!0),l($,{gutter:20,class:"stats-row"},{default:i(()=>[l(E,{span:6},{default:i(()=>[l(P,{class:"stat-card"},{default:i(()=>[x("div",ro,[x("div",lo,j(Q.totalDays),1),e[57]||(e[57]=x("div",{class:"stat-label"},"总天数",-1))])]),_:1})]),_:1}),l(E,{span:6},{default:i(()=>[l(P,{class:"stat-card"},{default:i(()=>[x("div",oo,[x("div",no,j(Q.normalDays),1),e[58]||(e[58]=x("div",{class:"stat-label"},"正常天数",-1))])]),_:1})]),_:1}),l(E,{span:6},{default:i(()=>[l(P,{class:"stat-card"},{default:i(()=>[x("div",so,[x("div",io,j(Q.abnormalDays),1),e[59]||(e[59]=x("div",{class:"stat-label"},"异常天数",-1))])]),_:1})]),_:1}),l(E,{span:6},{default:i(()=>[l(P,{class:"stat-card"},{default:i(()=>[x("div",uo,[x("div",co,j(Q.totalHours),1),e[60]||(e[60]=x("div",{class:"stat-label"},"总工时",-1))])]),_:1})]),_:1})]),_:1}),l(P,null,{header:i(()=>[x("div",po,[x("span",null,j(I.value==="daily"?"考勤详情":I.value==="monthly"?"月度统计":"员工项目统计"),1),x("span",mo,"共 "+j(re.total)+" 条记录",1)])]),default:i(()=>[I.value==="daily"?qe((se(),be(De,{key:0,data:s.value,style:{width:"100%"},"summary-method":$e,"show-summary":""},{default:i(()=>[l(D,{prop:"date",label:"日期",width:"120"}),l(D,{prop:"userName",label:"姓名",width:"100"}),l(D,{prop:"projectName",label:"工程",width:"150"}),l(D,{prop:"firstCheckIn",label:"首次签到",width:"120"},{default:i(({row:u})=>[A(j(u.firstCheckIn?q(u.firstCheckIn):"-"),1)]),_:1}),l(D,{prop:"lastCheckOut",label:"最后签退",width:"120"},{default:i(({row:u})=>[A(j(u.lastCheckOut?q(u.lastCheckOut):"-"),1)]),_:1}),l(D,{prop:"checkInCount",label:"签到次数",width:"100"}),l(D,{prop:"checkOutCount",label:"签退次数",width:"100"}),l(D,{prop:"totalWorkMinutes",label:"工作时长",width:"120"},{default:i(({row:u})=>[A(j(ge(u.totalWorkMinutes)),1)]),_:1}),l(D,{prop:"isAbnormal",label:"状态",width:"80"},{default:i(({row:u})=>[l(ue,{type:u.isAbnormal?"danger":"success"},{default:i(()=>[A(j(u.isAbnormal?"异常":"正常"),1)]),_:2},1032,["type"])]),_:1}),l(D,{label:"操作",width:"120"},{default:i(({row:u})=>[l(g,{text:"",type:"primary",onClick:Ye=>Ee(u)},{default:i(()=>e[61]||(e[61]=[A(" 查看详情 ")])),_:2,__:[61]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Xe,r.value]]):ye("",!0),I.value==="monthly"?qe((se(),be(De,{key:1,data:p.value,style:{width:"100%"}},{default:i(()=>[l(D,{prop:"userName",label:"姓名",width:"120"}),l(D,{prop:"department",label:"部门",width:"120"}),l(D,{prop:"attendanceDays",label:"出勤天数",width:"100"}),l(D,{prop:"absentDays",label:"缺勤天数",width:"100"}),l(D,{prop:"abnormalDays",label:"异常天数",width:"100"}),l(D,{prop:"totalWorkHours",label:"总工时",width:"100"},{default:i(({row:u})=>[A(j(u.totalWorkHours)+"h ",1)]),_:1}),l(D,{prop:"avgWorkHours",label:"平均工时",width:"100"},{default:i(({row:u})=>[A(j(u.avgWorkHours)+"h ",1)]),_:1}),l(D,{prop:"attendanceRate",label:"出勤率",width:"100"},{default:i(({row:u})=>[l(ue,{type:u.attendanceRate>=90?"success":u.attendanceRate>=80?"warning":"danger"},{default:i(()=>[A(j(u.attendanceRate)+"% ",1)]),_:2},1032,["type"])]),_:1}),l(D,{label:"操作",width:"120"},{default:i(({row:u})=>[l(g,{text:"",type:"primary",onClick:Ye=>He(u)},{default:i(()=>e[62]||(e[62]=[A(" 查看详情 ")])),_:2,__:[62]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Xe,r.value]]):ye("",!0),I.value==="employee-project"?qe((se(),be(De,{key:2,data:c.value,style:{width:"100%"}},{default:i(()=>[l(D,{prop:"userName",label:"姓名",width:"120"}),l(D,{prop:"projectName",label:"工程",width:"150"}),l(D,{prop:"clientName",label:"客户",width:"120"}),l(D,{prop:"role",label:"角色",width:"100"}),l(D,{prop:"attendanceDays",label:"出勤天数",width:"100"}),l(D,{prop:"totalWorkHours",label:"总工时",width:"100"},{default:i(({row:u})=>[A(j(u.totalWorkHours)+"h ",1)]),_:1}),l(D,{prop:"avgWorkHours",label:"平均工时",width:"100"},{default:i(({row:u})=>[A(j(u.avgWorkHours)+"h ",1)]),_:1}),l(D,{prop:"attendanceRate",label:"出勤率",width:"100"},{default:i(({row:u})=>[l(ue,{type:u.attendanceRate>=90?"success":u.attendanceRate>=80?"warning":"danger"},{default:i(()=>[A(j(u.attendanceRate)+"% ",1)]),_:2},1032,["type"])]),_:1}),l(D,{label:"操作",width:"120"},{default:i(({row:u})=>[l(g,{text:"",type:"primary",onClick:Ye=>vt(u)},{default:i(()=>e[63]||(e[63]=[A(" 查看详情 ")])),_:2,__:[63]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Xe,r.value]]):ye("",!0),x("div",fo,[l(xe,{"current-page":re.page,"onUpdate:currentPage":e[13]||(e[13]=u=>re.page=u),"page-size":re.size,"onUpdate:pageSize":e[14]||(e[14]=u=>re.size=u),"page-sizes":[10,20,50,100],total:re.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:nt,onCurrentChange:je},null,8,["current-page","page-size","total"])])]),_:1}),l(fe,{modelValue:y.value,"onUpdate:modelValue":e[15]||(e[15]=u=>y.value=u),title:"考勤详情",width:"800px"},{default:i(()=>[l(De,{data:f.value,style:{width:"100%"}},{default:i(()=>[l(D,{prop:"checkType",label:"类型",width:"80"},{default:i(({row:u})=>[l(ue,{type:u.checkType==="in"?"success":"warning"},{default:i(()=>[A(j(u.checkType==="in"?"签到":"签退"),1)]),_:2},1032,["type"])]),_:1}),l(D,{prop:"createdAt",label:"时间",width:"160"},{default:i(({row:u})=>[A(j(ve(u.createdAt)),1)]),_:1}),l(D,{prop:"locationName",label:"地点",width:"150"}),l(D,{prop:"address",label:"地址","min-width":"200"}),l(D,{prop:"distance",label:"距离",width:"80"},{default:i(({row:u})=>[A(j(u.distance?Math.round(u.distance)+"m":"-"),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),l(fe,{modelValue:w.value,"onUpdate:modelValue":e[16]||(e[16]=u=>w.value=u),title:`${(Vt=F.value)==null?void 0:Vt.userName} - ${W.value}月详细考勤记录`,width:"1200px",top:"5vh"},{header:i(()=>{var u;return[x("div",vo,[x("span",ho,j((u=F.value)==null?void 0:u.userName)+" - "+j(W.value)+"月详细考勤记录",1),x("div",go,[l(v,{onCommand:ze},{dropdown:i(()=>[l(_,null,{default:i(()=>[l(b,{command:"excel"},{default:i(()=>e[65]||(e[65]=[A("导出Excel")])),_:1,__:[65]}),l(b,{command:"pdf"},{default:i(()=>e[66]||(e[66]=[A("导出PDF")])),_:1,__:[66]})]),_:1})]),default:i(()=>[l(g,{type:"primary",icon:Ce(_t)},{default:i(()=>[e[64]||(e[64]=A(" 导出报表")),l(n,{class:"el-icon--right"},{default:i(()=>[l(Ce(wt))]),_:1})]),_:1,__:[64]},8,["icon"])]),_:1})])])]}),default:i(()=>[x("div",yo,[l(P,{class:"summary-card",shadow:"never"},{header:i(()=>e[67]||(e[67]=[x("span",null,"月度汇总",-1)])),default:i(()=>[l($,{gutter:20},{default:i(()=>[l(E,{span:6},{default:i(()=>{var u;return[x("div",bo,[x("div",_o,j(((u=F.value)==null?void 0:u.attendanceDays)||0),1),e[68]||(e[68]=x("div",{class:"summary-label"},"出勤天数",-1))])]}),_:1}),l(E,{span:6},{default:i(()=>{var u;return[x("div",wo,[x("div",Do,j(((u=F.value)==null?void 0:u.normalDays)||0),1),e[69]||(e[69]=x("div",{class:"summary-label"},"正常天数",-1))])]}),_:1}),l(E,{span:6},{default:i(()=>{var u;return[x("div",ko,[x("div",So,j(((u=F.value)==null?void 0:u.abnormalDays)||0),1),e[70]||(e[70]=x("div",{class:"summary-label"},"异常天数",-1))])]}),_:1}),l(E,{span:6},{default:i(()=>{var u;return[x("div",xo,[x("div",Co,j(((u=F.value)==null?void 0:u.totalWorkHours)||0)+"h",1),e[71]||(e[71]=x("div",{class:"summary-label"},"总工时",-1))])]}),_:1})]),_:1})]),_:1}),l(P,{class:"detail-card"},{header:i(()=>e[72]||(e[72]=[x("span",null,"每日详细记录",-1)])),default:i(()=>[qe((se(),be(De,{data:L.value,style:{width:"100%"},"default-sort":{prop:"date",order:"descending"}},{default:i(()=>[l(D,{prop:"date",label:"日期",width:"120",sortable:""}),l(D,{prop:"projectName",label:"项目",width:"150"}),l(D,{prop:"firstCheckIn",label:"首次签到",width:"120"},{default:i(({row:u})=>[A(j(u.firstCheckIn?q(u.firstCheckIn):"-"),1)]),_:1}),l(D,{prop:"lastCheckOut",label:"最后签退",width:"120"},{default:i(({row:u})=>[A(j(u.lastCheckOut?q(u.lastCheckOut):"-"),1)]),_:1}),l(D,{prop:"checkInCount",label:"签到次数",width:"100"}),l(D,{prop:"checkOutCount",label:"签退次数",width:"100"}),l(D,{prop:"totalWorkMinutes",label:"工作时长",width:"120"},{default:i(({row:u})=>[A(j(ge(u.totalWorkMinutes)),1)]),_:1}),l(D,{prop:"isAbnormal",label:"状态",width:"80"},{default:i(({row:u})=>[l(ue,{type:u.isAbnormal?"danger":"success"},{default:i(()=>[A(j(u.isAbnormal?"异常":"正常"),1)]),_:2},1032,["type"])]),_:1}),l(D,{label:"操作",width:"100"},{default:i(({row:u})=>[l(g,{text:"",type:"primary",onClick:Ye=>Pe(u)},{default:i(()=>e[73]||(e[73]=[A(" 查看打卡 ")])),_:2,__:[73]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Xe,S.value]])]),_:1})])]),_:1},8,["modelValue","title"]),l(fe,{modelValue:C.value,"onUpdate:modelValue":e[17]||(e[17]=u=>C.value=u),title:`${(Ft=V.value)==null?void 0:Ft.userName} - ${(Ot=V.value)==null?void 0:Ot.projectName} 项目详细记录`,width:"1200px",top:"5vh"},{header:i(()=>{var u,Ye;return[x("div",Ao,[x("span",Io,j((u=V.value)==null?void 0:u.userName)+" - "+j((Ye=V.value)==null?void 0:Ye.projectName)+" 项目详细记录",1),x("div",Mo,[l(v,{onCommand:st},{dropdown:i(()=>[l(_,null,{default:i(()=>[l(b,{command:"excel"},{default:i(()=>e[75]||(e[75]=[A("导出Excel")])),_:1,__:[75]}),l(b,{command:"pdf"},{default:i(()=>e[76]||(e[76]=[A("导出PDF")])),_:1,__:[76]})]),_:1})]),default:i(()=>[l(g,{type:"primary",icon:Ce(_t)},{default:i(()=>[e[74]||(e[74]=A(" 导出报表")),l(n,{class:"el-icon--right"},{default:i(()=>[l(Ce(wt))]),_:1})]),_:1,__:[74]},8,["icon"])]),_:1})])])]}),default:i(()=>[x("div",Lo,[l(P,{class:"summary-card",shadow:"never"},{header:i(()=>e[77]||(e[77]=[x("span",null,"项目汇总",-1)])),default:i(()=>[l($,{gutter:20},{default:i(()=>[l(E,{span:4},{default:i(()=>{var u;return[x("div",To,[x("div",Po,j(((u=V.value)==null?void 0:u.attendanceDays)||0),1),e[78]||(e[78]=x("div",{class:"summary-label"},"出勤天数",-1))])]}),_:1}),l(E,{span:4},{default:i(()=>{var u;return[x("div",No,[x("div",Ro,j(((u=V.value)==null?void 0:u.totalWorkHours)||0)+"h",1),e[79]||(e[79]=x("div",{class:"summary-label"},"总工时",-1))])]}),_:1}),l(E,{span:4},{default:i(()=>{var u;return[x("div",Eo,[x("div",Vo,j(((u=V.value)==null?void 0:u.avgWorkHours)||0)+"h",1),e[80]||(e[80]=x("div",{class:"summary-label"},"平均工时",-1))])]}),_:1}),l(E,{span:4},{default:i(()=>{var u;return[x("div",Fo,[x("div",Oo,j(((u=V.value)==null?void 0:u.attendanceRate)||0)+"%",1),e[81]||(e[81]=x("div",{class:"summary-label"},"出勤率",-1))])]}),_:1}),l(E,{span:4},{default:i(()=>{var u;return[x("div",$o,[x("div",Wo,j(((u=V.value)==null?void 0:u.role)||"员工"),1),e[82]||(e[82]=x("div",{class:"summary-label"},"项目角色",-1))])]}),_:1}),l(E,{span:4},{default:i(()=>{var u;return[x("div",jo,[x("div",Ho,j(((u=V.value)==null?void 0:u.clientName)||"未知"),1),e[83]||(e[83]=x("div",{class:"summary-label"},"客户",-1))])]}),_:1})]),_:1})]),_:1}),l(P,{class:"detail-card"},{header:i(()=>e[84]||(e[84]=[x("span",null,"项目考勤记录",-1)])),default:i(()=>[qe((se(),be(De,{data:T.value,style:{width:"100%"},"default-sort":{prop:"date",order:"descending"}},{default:i(()=>[l(D,{prop:"date",label:"日期",width:"120",sortable:""}),l(D,{prop:"firstCheckIn",label:"首次签到",width:"120"},{default:i(({row:u})=>[A(j(u.firstCheckIn?q(u.firstCheckIn):"-"),1)]),_:1}),l(D,{prop:"lastCheckOut",label:"最后签退",width:"120"},{default:i(({row:u})=>[A(j(u.lastCheckOut?q(u.lastCheckOut):"-"),1)]),_:1}),l(D,{prop:"checkInCount",label:"签到次数",width:"100"}),l(D,{prop:"checkOutCount",label:"签退次数",width:"100"}),l(D,{prop:"totalWorkMinutes",label:"工作时长",width:"120"},{default:i(({row:u})=>[A(j(ge(u.totalWorkMinutes)),1)]),_:1}),l(D,{prop:"isAbnormal",label:"状态",width:"80"},{default:i(({row:u})=>[l(ue,{type:u.isAbnormal?"danger":"success"},{default:i(()=>[A(j(u.isAbnormal?"异常":"正常"),1)]),_:2},1032,["type"])]),_:1}),l(D,{label:"操作",width:"100"},{default:i(({row:u})=>[l(g,{text:"",type:"primary",onClick:Ye=>Pe(u)},{default:i(()=>e[85]||(e[85]=[A(" 查看打卡 ")])),_:2,__:[85]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Xe,k.value]])]),_:1})])]),_:1},8,["modelValue","title"]),l(fe,{modelValue:U.value,"onUpdate:modelValue":e[29]||(e[29]=u=>U.value=u),title:"自定义导出设置",width:"600px"},{footer:i(()=>[x("div",zo,[l(g,{onClick:e[28]||(e[28]=u=>U.value=!1)},{default:i(()=>e[124]||(e[124]=[A("取消")])),_:1,__:[124]}),l(g,{type:"primary",onClick:Na,loading:X.value},{default:i(()=>e[125]||(e[125]=[A(" 开始导出 ")])),_:1,__:[125]},8,["loading"])])]),default:i(()=>[l(J,{model:N,"label-width":"120px"},{default:i(()=>[l(R,{label:"导出格式"},{default:i(()=>[l(Y,{modelValue:N.format,"onUpdate:modelValue":e[18]||(e[18]=u=>N.format=u)},{default:i(()=>[l(Ae,{value:"excel"},{default:i(()=>e[86]||(e[86]=[A("Excel (.xlsx)")])),_:1,__:[86]}),l(Ae,{value:"csv"},{default:i(()=>e[87]||(e[87]=[A("CSV (.csv)")])),_:1,__:[87]}),l(Ae,{value:"pdf"},{default:i(()=>e[88]||(e[88]=[A("PDF (.pdf)")])),_:1,__:[88]})]),_:1},8,["modelValue"])]),_:1}),l(R,{label:"导出内容"},{default:i(()=>[l(Y,{modelValue:N.content,"onUpdate:modelValue":e[19]||(e[19]=u=>N.content=u)},{default:i(()=>[l(Ae,{value:"current"},{default:i(()=>e[89]||(e[89]=[A("当前页面数据")])),_:1,__:[89]}),l(Ae,{value:"all"},{default:i(()=>e[90]||(e[90]=[A("全部数据")])),_:1,__:[90]}),l(Ae,{value:"filtered"},{default:i(()=>e[91]||(e[91]=[A("筛选后的数据")])),_:1,__:[91]})]),_:1},8,["modelValue"])]),_:1}),I.value==="daily"?(se(),be(R,{key:0,label:"包含字段"},{default:i(()=>[l(yt,{modelValue:N.fields,"onUpdate:modelValue":e[20]||(e[20]=u=>N.fields=u)},{default:i(()=>[l(pe,{value:"date"},{default:i(()=>e[92]||(e[92]=[A("日期")])),_:1,__:[92]}),l(pe,{value:"userName"},{default:i(()=>e[93]||(e[93]=[A("姓名")])),_:1,__:[93]}),l(pe,{value:"projectName"},{default:i(()=>e[94]||(e[94]=[A("工程")])),_:1,__:[94]}),l(pe,{value:"firstCheckIn"},{default:i(()=>e[95]||(e[95]=[A("首次签到")])),_:1,__:[95]}),l(pe,{value:"lastCheckOut"},{default:i(()=>e[96]||(e[96]=[A("最后签退")])),_:1,__:[96]}),l(pe,{value:"checkInCount"},{default:i(()=>e[97]||(e[97]=[A("签到次数")])),_:1,__:[97]}),l(pe,{value:"checkOutCount"},{default:i(()=>e[98]||(e[98]=[A("签退次数")])),_:1,__:[98]}),l(pe,{value:"totalWorkMinutes"},{default:i(()=>e[99]||(e[99]=[A("工作时长")])),_:1,__:[99]}),l(pe,{value:"isAbnormal"},{default:i(()=>e[100]||(e[100]=[A("状态")])),_:1,__:[100]})]),_:1},8,["modelValue"])]),_:1})):ye("",!0),I.value==="monthly"?(se(),be(R,{key:1,label:"包含字段"},{default:i(()=>[l(yt,{modelValue:N.fields,"onUpdate:modelValue":e[21]||(e[21]=u=>N.fields=u)},{default:i(()=>[l(pe,{value:"userName"},{default:i(()=>e[101]||(e[101]=[A("姓名")])),_:1,__:[101]}),l(pe,{value:"department"},{default:i(()=>e[102]||(e[102]=[A("部门")])),_:1,__:[102]}),l(pe,{value:"attendanceDays"},{default:i(()=>e[103]||(e[103]=[A("出勤天数")])),_:1,__:[103]}),l(pe,{value:"absentDays"},{default:i(()=>e[104]||(e[104]=[A("缺勤天数")])),_:1,__:[104]}),l(pe,{value:"abnormalDays"},{default:i(()=>e[105]||(e[105]=[A("异常天数")])),_:1,__:[105]}),l(pe,{value:"totalWorkHours"},{default:i(()=>e[106]||(e[106]=[A("总工时")])),_:1,__:[106]}),l(pe,{value:"avgWorkHours"},{default:i(()=>e[107]||(e[107]=[A("平均工时")])),_:1,__:[107]}),l(pe,{value:"attendanceRate"},{default:i(()=>e[108]||(e[108]=[A("出勤率")])),_:1,__:[108]})]),_:1},8,["modelValue"])]),_:1})):ye("",!0),I.value==="employee-project"?(se(),be(R,{key:2,label:"包含字段"},{default:i(()=>[l(yt,{modelValue:N.fields,"onUpdate:modelValue":e[22]||(e[22]=u=>N.fields=u)},{default:i(()=>[l(pe,{value:"userName"},{default:i(()=>e[109]||(e[109]=[A("姓名")])),_:1,__:[109]}),l(pe,{value:"projectName"},{default:i(()=>e[110]||(e[110]=[A("工程")])),_:1,__:[110]}),l(pe,{value:"clientName"},{default:i(()=>e[111]||(e[111]=[A("客户")])),_:1,__:[111]}),l(pe,{value:"role"},{default:i(()=>e[112]||(e[112]=[A("角色")])),_:1,__:[112]}),l(pe,{value:"attendanceDays"},{default:i(()=>e[113]||(e[113]=[A("出勤天数")])),_:1,__:[113]}),l(pe,{value:"totalWorkHours"},{default:i(()=>e[114]||(e[114]=[A("总工时")])),_:1,__:[114]}),l(pe,{value:"avgWorkHours"},{default:i(()=>e[115]||(e[115]=[A("平均工时")])),_:1,__:[115]}),l(pe,{value:"attendanceRate"},{default:i(()=>e[116]||(e[116]=[A("出勤率")])),_:1,__:[116]})]),_:1},8,["modelValue"])]),_:1})):ye("",!0),l(R,{label:"文件名"},{default:i(()=>[l(ar,{modelValue:N.filename,"onUpdate:modelValue":e[23]||(e[23]=u=>N.filename=u),placeholder:"请输入文件名"},{append:i(()=>[A("."+j(N.format==="excel"?"xlsx":N.format),1)]),_:1},8,["modelValue"])]),_:1}),l(R,{label:"包含汇总"},{default:i(()=>[l(Et,{modelValue:N.includeSummary,"onUpdate:modelValue":e[24]||(e[24]=u=>N.includeSummary=u)},null,8,["modelValue"]),e[117]||(e[117]=x("span",{class:"form-tip"},"是否在导出文件中包含统计汇总信息",-1))]),_:1,__:[117]}),N.format==="excel"?(se(),be(R,{key:3,label:"包含图表"},{default:i(()=>[l(Et,{modelValue:N.includeCharts,"onUpdate:modelValue":e[25]||(e[25]=u=>N.includeCharts=u)},null,8,["modelValue"]),e[118]||(e[118]=x("span",{class:"form-tip"},"是否在Excel中包含图表（仅Excel格式支持）",-1))]),_:1,__:[118]})):ye("",!0),N.format==="pdf"?(se(),be(R,{key:4,label:"PDF生成方式"},{default:i(()=>[l(Y,{modelValue:N.pdfMethod,"onUpdate:modelValue":e[26]||(e[26]=u=>N.pdfMethod=u)},{default:i(()=>[l(Ae,{value:"html2canvas"},{default:i(()=>e[119]||(e[119]=[A("HTML转图片（推荐，完美支持中文）")])),_:1,__:[119]}),l(Ae,{value:"text"},{default:i(()=>e[120]||(e[120]=[A("文本模式（英文/中文可选）")])),_:1,__:[120]})]),_:1},8,["modelValue"]),e[121]||(e[121]=x("div",{class:"form-tip"}," HTML转图片方式可以完美显示中文，但文件较大。文本模式文件小但中文可能乱码。 ",-1))]),_:1,__:[121]})):ye("",!0),N.format==="pdf"&&N.pdfMethod==="text"?(se(),be(R,{key:5,label:"PDF语言"},{default:i(()=>[l(Y,{modelValue:N.pdfLanguage,"onUpdate:modelValue":e[27]||(e[27]=u=>N.pdfLanguage=u)},{default:i(()=>[l(Ae,{value:"english"},{default:i(()=>e[122]||(e[122]=[A("英文（避免乱码）")])),_:1,__:[122]}),l(Ae,{value:"chinese"},{default:i(()=>e[123]||(e[123]=[A("中文（可能乱码）")])),_:1,__:[123]})]),_:1},8,["modelValue"])]),_:1})):ye("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(fe,{modelValue:ae.value,"onUpdate:modelValue":e[30]||(e[30]=u=>ae.value=u),title:me.value,width:"80%","close-on-click-modal":!1},{default:i(()=>[qe((se(),Ie("div",null,[K.value==="presentToday"?(se(),Ie("div",Yo,[l(De,{data:ee.value,style:{width:"100%"}},{default:i(()=>[l(D,{prop:"userName",label:"员工姓名",width:"120"}),l(D,{prop:"projectName",label:"项目",width:"150"}),l(D,{prop:"locationName",label:"打卡地点",width:"150"}),l(D,{prop:"locationAddress",label:"地点地址","min-width":"200"}),l(D,{prop:"firstCheckIn",label:"首次签到时间",width:"180"},{default:i(({row:u})=>[A(j(u.firstCheckIn||"-"),1)]),_:1})]),_:1},8,["data"])])):ye("",!0),K.value==="absentToday"?(se(),Ie("div",Uo,[l(De,{data:ee.value,style:{width:"100%"}},{default:i(()=>[l(D,{prop:"realName",label:"员工姓名",width:"120"}),l(D,{prop:"username",label:"用户名",width:"120"}),l(D,{prop:"role",label:"角色",width:"100"},{default:i(({row:u})=>[l(ue,{type:u.role==="admin"?"danger":u.role==="manager"?"warning":"info"},{default:i(()=>[A(j(u.role==="admin"?"管理员":u.role==="manager"?"经理":"员工"),1)]),_:2},1032,["type"])]),_:1}),l(D,{prop:"phone",label:"联系电话",width:"130"}),l(D,{prop:"email",label:"邮箱","min-width":"180"})]),_:1},8,["data"])])):ye("",!0),K.value==="lateToday"?(se(),Ie("div",Go,[l(De,{data:ee.value,style:{width:"100%"}},{default:i(()=>[l(D,{prop:"userName",label:"员工姓名",width:"120"}),l(D,{prop:"projectName",label:"项目",width:"150"}),l(D,{prop:"locationName",label:"打卡地点",width:"150"}),l(D,{prop:"workTimeStart",label:"应到时间",width:"100"}),l(D,{prop:"checkInTimeStr",label:"实际到达时间",width:"120"}),l(D,{prop:"lateTime",label:"迟到时长",width:"120"},{default:i(({row:u})=>[l(ue,{type:"warning"},{default:i(()=>[A(j(u.lateTime),1)]),_:2},1024)]),_:1})]),_:1},8,["data"])])):ye("",!0),K.value==="monthlyWorkHours"?(se(),Ie("div",Bo,[l(De,{data:ee.value,style:{width:"100%"}},{default:i(()=>[l(D,{prop:"userName",label:"员工姓名",width:"120"}),l(D,{prop:"projectName",label:"项目",width:"150"}),l(D,{prop:"workDays",label:"工作天数",width:"100"}),l(D,{prop:"totalWorkHours",label:"总工时",width:"100"},{default:i(({row:u})=>[A(j(u.totalWorkHours)+"h ",1)]),_:1}),l(D,{prop:"avgWorkHours",label:"平均工时",width:"100"},{default:i(({row:u})=>[A(j(u.avgWorkHours)+"h ",1)]),_:1})]),_:1},8,["data"])])):ye("",!0),K.value==="abnormalRecords"?(se(),Ie("div",Xo,[l(De,{data:ee.value,style:{width:"100%"}},{default:i(()=>[l(D,{prop:"userName",label:"员工姓名",width:"120"}),l(D,{prop:"projectName",label:"项目",width:"150"}),l(D,{prop:"checkType",label:"打卡类型",width:"100"},{default:i(({row:u})=>[l(ue,{type:u.checkType==="in"?"success":"warning"},{default:i(()=>[A(j(u.checkType==="in"?"签到":"签退"),1)]),_:2},1032,["type"])]),_:1}),l(D,{prop:"createdAt",label:"打卡时间",width:"180"},{default:i(({row:u})=>[A(j(u.createdAt),1)]),_:1}),l(D,{prop:"abnormalType",label:"异常类型",width:"100"},{default:i(({row:u})=>[l(ue,{type:"danger"},{default:i(()=>[A(j(u.abnormalType),1)]),_:2},1024)]),_:1}),l(D,{prop:"abnormalReason",label:"异常原因","min-width":"200"}),l(D,{prop:"locationName",label:"打卡地点",width:"150"})]),_:1},8,["data"])])):ye("",!0),!Z.value&&ee.value.length===0?(se(),Ie("div",qo,[l(rr,{description:"暂无数据"})])):ye("",!0)])),[[Xe,Z.value]])]),_:1},8,["modelValue","title"])])}}});const en=al(Zo,[["__scopeId","data-v-efbd5daf"]]);export{en as default};
