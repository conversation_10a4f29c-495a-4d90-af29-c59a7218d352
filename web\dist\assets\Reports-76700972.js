import{d as Oa,r as ne,a as Xe,K as Vt,p as ja,b as ae,M as Wa,o as de,c as gt,e as w,f as s,w as o,g as Me,i as M,y as z,q as pe,x as ge,N as Rt,O as Nt,s as qe,P as Ha,Q as ht,ai as Fa,E as ee,aj as Et,ak as Ot,al as jt,am as $a,an as Ya,Y as za,a1 as Ua,ao as Ze,F as Ba,ap as Ga,aq as Xa,ar as qa,W as Za,J as Ja}from"./index-fea52ff4.js";import{P as me,D as Wt,r as Ht,f as na,g as Ka,S as Ft,h as $t,l as yt,_ as Te,j as sa,k as Fe,m as At,n as Qa,o as oa,p as Yt,q as xt,s as Ke,t as ia,v as er,w as tr,x as Ne,y as Ee,z as ua,A as ar,B as St,G as rr,C as lr,F as kt,H as da,R as ca,I as be,J as pa,K as va,L as nr,M as sr,N as ma,O as fa,Q as or,T as ga,U as ct,V as ir,W as Ct,X as ur,Y as dr,Z as cr,$ as pr,a0 as vr,a1 as mr,a2 as we,a3 as ha,a4 as fr,a5 as gr,a6 as hr,a7 as yr,a8 as br,a9 as zt,aa as _r,ab as Dr,ac as wr,ad as Sr,u as Ar,i as xr,a as kr,b as Cr,c as Ir,d as Mr,e as Lr,E as Ut}from"./index-0ea1856e.js";import{_ as Pr}from"./_plugin-vue_export-helper-c27b6911.js";function ya(l,r,a,t,n,i,u,c){var m=n-l,f=i-r,p=a-l,g=t-r,b=Math.sqrt(p*p+g*g);p/=b,g/=b;var y=m*p+f*g,h=y/b;c&&(h=Math.min(Math.max(h,0),1)),h*=b;var k=u[0]=l+h*p,S=u[1]=r+h*g;return Math.sqrt((k-n)*(k-n)+(S-i)*(S-i))}var Le=new me,ue=new me,ve=new me,Pe=new me,_e=new me,dt=[],fe=new me;function Tr(l,r){if(r<=180&&r>0){r=r/180*Math.PI,Le.fromArray(l[0]),ue.fromArray(l[1]),ve.fromArray(l[2]),me.sub(Pe,Le,ue),me.sub(_e,ve,ue);var a=Pe.len(),t=_e.len();if(!(a<.001||t<.001)){Pe.scale(1/a),_e.scale(1/t);var n=Pe.dot(_e),i=Math.cos(r);if(i<n){var u=ya(ue.x,ue.y,ve.x,ve.y,Le.x,Le.y,dt,!1);fe.fromArray(dt),fe.scaleAndAdd(_e,u/Math.tan(Math.PI-r));var c=ve.x!==ue.x?(fe.x-ue.x)/(ve.x-ue.x):(fe.y-ue.y)/(ve.y-ue.y);if(isNaN(c))return;c<0?me.copy(fe,ue):c>1&&me.copy(fe,ve),fe.toArray(l[1])}}}}function Vr(l,r,a){if(a<=180&&a>0){a=a/180*Math.PI,Le.fromArray(l[0]),ue.fromArray(l[1]),ve.fromArray(l[2]),me.sub(Pe,ue,Le),me.sub(_e,ve,ue);var t=Pe.len(),n=_e.len();if(!(t<.001||n<.001)){Pe.scale(1/t),_e.scale(1/n);var i=Pe.dot(r),u=Math.cos(a);if(i<u){var c=ya(ue.x,ue.y,ve.x,ve.y,Le.x,Le.y,dt,!1);fe.fromArray(dt);var m=Math.PI/2,f=Math.acos(_e.dot(r)),p=m+f-a;if(p>=m)me.copy(fe,ve);else{fe.scaleAndAdd(_e,c/Math.tan(Math.PI/2-p));var g=ve.x!==ue.x?(fe.x-ue.x)/(ve.x-ue.x):(fe.y-ue.y)/(ve.y-ue.y);if(isNaN(g))return;g<0?me.copy(fe,ue):g>1&&me.copy(fe,ve)}fe.toArray(l[1])}}}}function bt(l,r,a,t){var n=a==="normal",i=n?l:l.ensureState(a);i.ignore=r;var u=t.get("smooth");u&&u===!0&&(u=.3),i.shape=i.shape||{},u>0&&(i.shape.smooth=u);var c=t.getModel("lineStyle").getLineStyle();n?l.useStyle(c):i.style=c}function Rr(l,r){var a=r.smooth,t=r.points;if(t)if(l.moveTo(t[0][0],t[0][1]),a>0&&t.length>=3){var n=$t(t[0],t[1]),i=$t(t[1],t[2]);if(!n||!i){l.lineTo(t[1][0],t[1][1]),l.lineTo(t[2][0],t[2][1]);return}var u=Math.min(n,i)*a,c=yt([],t[1],t[0],u/n),m=yt([],t[1],t[2],u/i),f=yt([],c,m,.5);l.bezierCurveTo(c[0],c[1],c[0],c[1],f[0],f[1]),l.bezierCurveTo(m[0],m[1],m[0],m[1],t[2][0],t[2][1])}else for(var p=1;p<t.length;p++)l.lineTo(t[p][0],t[p][1])}function Nr(l,r,a){var t=l.getTextGuideLine(),n=l.getTextContent();if(!n){t&&l.removeTextGuideLine();return}for(var i=r.normal,u=i.get("show"),c=n.ignore,m=0;m<Wt.length;m++){var f=Wt[m],p=r[f],g=f==="normal";if(p){var b=p.get("show"),y=g?c:Ht(n.states[f]&&n.states[f].ignore,c);if(y||!Ht(b,u)){var h=g?t:t&&t.states[f];h&&(h.ignore=!0),t&&bt(t,!0,f,p);continue}t||(t=new na,l.setTextGuideLine(t),!g&&(c||!u)&&bt(t,!0,"normal",r.normal),l.stateProxy&&(t.stateProxy=l.stateProxy)),bt(t,!1,f,p)}}if(t){Ka(t.style,a),t.style.fill=null;var k=i.get("showAbove"),S=l.textGuideLineConfig=l.textGuideLineConfig||{};S.showAbove=k||!1,t.buildPath=Rr}}function Er(l,r){r=r||"labelLine";for(var a={normal:l.getModel(r)},t=0;t<Ft.length;t++){var n=Ft[t];a[n]=l.getModel([n,r])}return a}var ba=function(l){Te(r,l);function r(){var a=l!==null&&l.apply(this,arguments)||this;return a.type=r.type,a}return r.prototype.getInitialData=function(a,t){return sa(null,this,{useEncodeDefaulter:!0})},r.prototype.getMarkerPosition=function(a,t,n){var i=this.coordinateSystem;if(i&&i.clampData){var u=i.clampData(a),c=i.dataToPoint(u);if(n)Fe(i.getAxes(),function(b,y){if(b.type==="category"&&t!=null){var h=b.getTicksCoords(),k=b.getTickModel().get("alignWithLabel"),S=u[y],V=t[y]==="x1"||t[y]==="y1";if(V&&!k&&(S+=1),h.length<2)return;if(h.length===2){c[y]=b.toGlobalCoord(b.getExtent()[V?1:0]);return}for(var _=void 0,P=void 0,F=1,W=0;W<h.length;W++){var C=h[W].coord,Y=W===h.length-1?h[W-1].tickValue+F:h[W].tickValue;if(Y===S){P=C;break}else if(Y<S)_=C;else if(_!=null&&Y>S){P=(C+_)/2;break}W===1&&(F=Y-h[0].tickValue)}P==null&&(_?_&&(P=h[h.length-1].coord):P=h[0].coord),c[y]=b.toGlobalCoord(P)}});else{var m=this.getData(),f=m.getLayout("offset"),p=m.getLayout("size"),g=i.getBaseAxis().isHorizontal()?0:1;c[g]+=f+p/2}return c}return[NaN,NaN]},r.type="series.__base_bar__",r.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},r}(At);At.registerClass(ba);const Bt=ba;var Or=function(l){Te(r,l);function r(){var a=l!==null&&l.apply(this,arguments)||this;return a.type=r.type,a}return r.prototype.getInitialData=function(){return sa(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},r.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},r.prototype.getProgressiveThreshold=function(){var a=this.get("progressiveThreshold"),t=this.get("largeThreshold");return t>a&&(a=t),a},r.prototype.brushSelector=function(a,t,n){return n.rect(t.getItemLayout(a))},r.type="series.bar",r.dependencies=["grid","polar"],r.defaultOption=Qa(Bt.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),r}(Bt);const jr=Or;var Wr=function(){function l(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return l}(),Hr=function(l){Te(r,l);function r(a){var t=l.call(this,a)||this;return t.type="sausage",t}return r.prototype.getDefaultShape=function(){return new Wr},r.prototype.buildPath=function(a,t){var n=t.cx,i=t.cy,u=Math.max(t.r0||0,0),c=Math.max(t.r,0),m=(c-u)*.5,f=u+m,p=t.startAngle,g=t.endAngle,b=t.clockwise,y=Math.PI*2,h=b?g-p<y:p-g<y;h||(p=g-(b?y:-y));var k=Math.cos(p),S=Math.sin(p),V=Math.cos(g),_=Math.sin(g);h?(a.moveTo(k*u+n,S*u+i),a.arc(k*f+n,S*f+i,m,-Math.PI+p,p,!b)):a.moveTo(k*c+n,S*c+i),a.arc(n,i,c,p,g,!b),a.arc(V*f+n,_*f+i,m,g-Math.PI*2,g-Math.PI,!b),u!==0&&a.arc(n,i,u,g,p,b)},r}(oa);const Gt=Hr;function Fr(l,r){r=r||{};var a=r.isRoundCap;return function(t,n,i){var u=n.position;if(!u||u instanceof Array)return Yt(t,n,i);var c=l(u),m=n.distance!=null?n.distance:5,f=this.shape,p=f.cx,g=f.cy,b=f.r,y=f.r0,h=(b+y)/2,k=f.startAngle,S=f.endAngle,V=(k+S)/2,_=a?Math.abs(b-y)/2:0,P=Math.cos,F=Math.sin,W=p+b*P(k),C=g+b*F(k),Y="left",A="top";switch(c){case"startArc":W=p+(y-m)*P(V),C=g+(y-m)*F(V),Y="center",A="top";break;case"insideStartArc":W=p+(y+m)*P(V),C=g+(y+m)*F(V),Y="center",A="bottom";break;case"startAngle":W=p+h*P(k)+st(k,m+_,!1),C=g+h*F(k)+ot(k,m+_,!1),Y="right",A="middle";break;case"insideStartAngle":W=p+h*P(k)+st(k,-m+_,!1),C=g+h*F(k)+ot(k,-m+_,!1),Y="left",A="middle";break;case"middle":W=p+h*P(V),C=g+h*F(V),Y="center",A="middle";break;case"endArc":W=p+(b+m)*P(V),C=g+(b+m)*F(V),Y="center",A="bottom";break;case"insideEndArc":W=p+(b-m)*P(V),C=g+(b-m)*F(V),Y="center",A="top";break;case"endAngle":W=p+h*P(S)+st(S,m+_,!0),C=g+h*F(S)+ot(S,m+_,!0),Y="left",A="middle";break;case"insideEndAngle":W=p+h*P(S)+st(S,-m+_,!0),C=g+h*F(S)+ot(S,-m+_,!0),Y="right",A="middle";break;default:return Yt(t,n,i)}return t=t||{},t.x=W,t.y=C,t.align=Y,t.verticalAlign=A,t}}function $r(l,r,a,t){if(xt(t)){l.setTextConfig({rotation:t});return}else if(Ke(r)){l.setTextConfig({rotation:0});return}var n=l.shape,i=n.clockwise?n.startAngle:n.endAngle,u=n.clockwise?n.endAngle:n.startAngle,c=(i+u)/2,m,f=a(r);switch(f){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":m=c;break;case"startAngle":case"insideStartAngle":m=i;break;case"endAngle":case"insideEndAngle":m=u;break;default:l.setTextConfig({rotation:0});return}var p=Math.PI*1.5-m;f==="middle"&&p>Math.PI/2&&p<Math.PI*1.5&&(p-=Math.PI),l.setTextConfig({rotation:p})}function st(l,r,a){return r*Math.sin(l)*(a?-1:1)}function ot(l,r,a){return r*Math.cos(l)*(a?1:-1)}function Je(l,r,a){var t=l.get("borderRadius");if(t==null)return a?{cornerRadius:0}:null;Ke(t)||(t=[t,t,t,t]);var n=Math.abs(r.r||0-r.r0||0);return{cornerRadius:ia(t,function(i){return er(i,n)})}}var _t=Math.max,Dt=Math.min;function Yr(l,r){var a=l.getArea&&l.getArea();if(ga(l,"cartesian2d")){var t=l.getBaseAxis();if(t.type!=="category"||!t.onBand){var n=r.getLayout("bandWidth");t.isHorizontal()?(a.x-=n,a.width+=n*2):(a.y-=n,a.height+=n*2)}}return a}var zr=function(l){Te(r,l);function r(){var a=l.call(this)||this;return a.type=r.type,a._isFirstFrame=!0,a}return r.prototype.render=function(a,t,n,i){this._model=a,this._removeOnRenderedListener(n),this._updateDrawMode(a);var u=a.get("coordinateSystem");(u==="cartesian2d"||u==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(a,t,n):this._renderNormal(a,t,n,i))},r.prototype.incrementalPrepareRender=function(a){this._clear(),this._updateDrawMode(a),this._updateLargeClip(a)},r.prototype.incrementalRender=function(a,t){this._progressiveEls=[],this._incrementalRenderLarge(a,t)},r.prototype.eachRendered=function(a){tr(this._progressiveEls||this.group,a)},r.prototype._updateDrawMode=function(a){var t=a.pipelineContext.large;(this._isLargeDraw==null||t!==this._isLargeDraw)&&(this._isLargeDraw=t,this._clear())},r.prototype._renderNormal=function(a,t,n,i){var u=this.group,c=a.getData(),m=this._data,f=a.coordinateSystem,p=f.getBaseAxis(),g;f.type==="cartesian2d"?g=p.isHorizontal():f.type==="polar"&&(g=p.dim==="angle");var b=a.isAnimationEnabled()?a:null,y=Ur(a,f);y&&this._enableRealtimeSort(y,c,n);var h=a.get("clip",!0)||y,k=Yr(f,c);u.removeClipPath();var S=a.get("roundCap",!0),V=a.get("showBackground",!0),_=a.getModel("backgroundStyle"),P=_.get("borderRadius")||0,F=[],W=this._backgroundEls,C=i&&i.isInitSort,Y=i&&i.type==="changeAxisOrder";function A(N){var re=it[f.type](c,N),J=Kr(f,g,re);return J.useStyle(_.getItemStyle()),f.type==="cartesian2d"?J.setShape("r",P):J.setShape("cornerRadius",P),F[N]=J,J}c.diff(m).add(function(N){var re=c.getItemModel(N),J=it[f.type](c,N,re);if(V&&A(N),!(!c.hasValue(N)||!Kt[f.type](J))){var se=!1;h&&(se=Xt[f.type](k,J));var B=qt[f.type](a,c,N,J,g,b,p.model,!1,S);y&&(B.forceLabelAnimation=!0),Qt(B,c,N,re,J,a,g,f.type==="polar"),C?B.attr({shape:J}):y?Zt(y,b,B,J,N,g,!1,!1):Ne(B,{shape:J},a,N),c.setItemGraphicEl(N,B),u.add(B),B.ignore=se}}).update(function(N,re){var J=c.getItemModel(N),se=it[f.type](c,N,J);if(V){var B=void 0;W.length===0?B=A(re):(B=W[re],B.useStyle(_.getItemStyle()),f.type==="cartesian2d"?B.setShape("r",P):B.setShape("cornerRadius",P),F[N]=B);var he=it[f.type](c,N),le=Da(g,he,f);Ee(B,{shape:le},b,N)}var Z=m.getItemGraphicEl(re);if(!c.hasValue(N)||!Kt[f.type](se)){u.remove(Z);return}var K=!1;if(h&&(K=Xt[f.type](k,se),K&&u.remove(Z)),Z?ua(Z):Z=qt[f.type](a,c,N,se,g,b,p.model,!!Z,S),y&&(Z.forceLabelAnimation=!0),Y){var oe=Z.getTextContent();if(oe){var Q=ar(oe);Q.prevValue!=null&&(Q.prevValue=Q.value)}}else Qt(Z,c,N,J,se,a,g,f.type==="polar");C?Z.attr({shape:se}):y?Zt(y,b,Z,se,N,g,!0,Y):Ee(Z,{shape:se},a,N,null),c.setItemGraphicEl(N,Z),Z.ignore=K,u.add(Z)}).remove(function(N){var re=m.getItemGraphicEl(N);re&&St(re,a,N)}).execute();var $=this._backgroundGroup||(this._backgroundGroup=new rr);$.removeAll();for(var q=0;q<F.length;++q)$.add(F[q]);u.add($),this._backgroundEls=F,this._data=c},r.prototype._renderLarge=function(a,t,n){this._clear(),ta(a,this.group),this._updateLargeClip(a)},r.prototype._incrementalRenderLarge=function(a,t){this._removeBackground(),ta(t,this.group,this._progressiveEls,!0)},r.prototype._updateLargeClip=function(a){var t=a.get("clip",!0)&&lr(a.coordinateSystem,!1,a),n=this.group;t?n.setClipPath(t):n.removeClipPath()},r.prototype._enableRealtimeSort=function(a,t,n){var i=this;if(t.count()){var u=a.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(t,a,n),this._isFirstFrame=!1;else{var c=function(m){var f=t.getItemGraphicEl(m),p=f&&f.shape;return p&&Math.abs(u.isHorizontal()?p.height:p.width)||0};this._onRendered=function(){i._updateSortWithinSameData(t,c,u,n)},n.getZr().on("rendered",this._onRendered)}}},r.prototype._dataSort=function(a,t,n){var i=[];return a.each(a.mapDimension(t.dim),function(u,c){var m=n(c);m=m??NaN,i.push({dataIndex:c,mappedValue:m,ordinalNumber:u})}),i.sort(function(u,c){return c.mappedValue-u.mappedValue}),{ordinalNumbers:ia(i,function(u){return u.ordinalNumber})}},r.prototype._isOrderChangedWithinSameData=function(a,t,n){for(var i=n.scale,u=a.mapDimension(n.dim),c=Number.MAX_VALUE,m=0,f=i.getOrdinalMeta().categories.length;m<f;++m){var p=a.rawIndexOf(u,i.getRawOrdinalNumber(m)),g=p<0?Number.MIN_VALUE:t(a.indexOfRawIndex(p));if(g>c)return!0;c=g}return!1},r.prototype._isOrderDifferentInView=function(a,t){for(var n=t.scale,i=n.getExtent(),u=Math.max(0,i[0]),c=Math.min(i[1],n.getOrdinalMeta().categories.length-1);u<=c;++u)if(a.ordinalNumbers[u]!==n.getRawOrdinalNumber(u))return!0},r.prototype._updateSortWithinSameData=function(a,t,n,i){if(this._isOrderChangedWithinSameData(a,t,n)){var u=this._dataSort(a,n,t);this._isOrderDifferentInView(u,n)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:u}))}},r.prototype._dispatchInitSort=function(a,t,n){var i=t.baseAxis,u=this._dataSort(a,i,function(c){return a.get(a.mapDimension(t.otherAxis.dim),c)});n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:u})},r.prototype.remove=function(a,t){this._clear(this._model),this._removeOnRenderedListener(t)},r.prototype.dispose=function(a,t){this._removeOnRenderedListener(t)},r.prototype._removeOnRenderedListener=function(a){this._onRendered&&(a.getZr().off("rendered",this._onRendered),this._onRendered=null)},r.prototype._clear=function(a){var t=this.group,n=this._data;a&&a.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl(function(i){St(i,a,kt(i).dataIndex)})):t.removeAll(),this._data=null,this._isFirstFrame=!0},r.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},r.type="bar",r}(da),Xt={cartesian2d:function(l,r){var a=r.width<0?-1:1,t=r.height<0?-1:1;a<0&&(r.x+=r.width,r.width=-r.width),t<0&&(r.y+=r.height,r.height=-r.height);var n=l.x+l.width,i=l.y+l.height,u=_t(r.x,l.x),c=Dt(r.x+r.width,n),m=_t(r.y,l.y),f=Dt(r.y+r.height,i),p=c<u,g=f<m;return r.x=p&&u>n?c:u,r.y=g&&m>i?f:m,r.width=p?0:c-u,r.height=g?0:f-m,a<0&&(r.x+=r.width,r.width=-r.width),t<0&&(r.y+=r.height,r.height=-r.height),p||g},polar:function(l,r){var a=r.r0<=r.r?1:-1;if(a<0){var t=r.r;r.r=r.r0,r.r0=t}var n=Dt(r.r,l.r),i=_t(r.r0,l.r0);r.r=n,r.r0=i;var u=n-i<0;if(a<0){var t=r.r;r.r=r.r0,r.r0=t}return u}},qt={cartesian2d:function(l,r,a,t,n,i,u,c,m){var f=new ca({shape:be({},t),z2:1});if(f.__dataIndex=a,f.name="item",i){var p=f.shape,g=n?"height":"width";p[g]=0}return f},polar:function(l,r,a,t,n,i,u,c,m){var f=!n&&m?Gt:ct,p=new f({shape:t,z2:1});p.name="item";var g=_a(n);if(p.calculateTextPosition=Fr(g,{isRoundCap:f===Gt}),i){var b=p.shape,y=n?"r":"endAngle",h={};b[y]=n?t.r0:t.startAngle,h[y]=t[y],(c?Ee:Ne)(p,{shape:h},i)}return p}};function Ur(l,r){var a=l.get("realtimeSort",!0),t=r.getBaseAxis();if(a&&t.type==="category"&&r.type==="cartesian2d")return{baseAxis:t,otherAxis:r.getOtherAxis(t)}}function Zt(l,r,a,t,n,i,u,c){var m,f;i?(f={x:t.x,width:t.width},m={y:t.y,height:t.height}):(f={y:t.y,height:t.height},m={x:t.x,width:t.width}),c||(u?Ee:Ne)(a,{shape:m},r,n,null);var p=r?l.baseAxis.model:null;(u?Ee:Ne)(a,{shape:f},p,n)}function Jt(l,r){for(var a=0;a<r.length;a++)if(!isFinite(l[r[a]]))return!0;return!1}var Br=["x","y","width","height"],Gr=["cx","cy","r","startAngle","endAngle"],Kt={cartesian2d:function(l){return!Jt(l,Br)},polar:function(l){return!Jt(l,Gr)}},it={cartesian2d:function(l,r,a){var t=l.getItemLayout(r),n=a?qr(a,t):0,i=t.width>0?1:-1,u=t.height>0?1:-1;return{x:t.x+i*n/2,y:t.y+u*n/2,width:t.width-i*n,height:t.height-u*n}},polar:function(l,r,a){var t=l.getItemLayout(r);return{cx:t.cx,cy:t.cy,r0:t.r0,r:t.r,startAngle:t.startAngle,endAngle:t.endAngle,clockwise:t.clockwise}}};function Xr(l){return l.startAngle!=null&&l.endAngle!=null&&l.startAngle===l.endAngle}function _a(l){return function(r){var a=r?"Arc":"Angle";return function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+a;default:return t}}}(l)}function Qt(l,r,a,t,n,i,u,c){var m=r.getItemVisual(a,"style");if(c){if(!i.get("roundCap")){var p=l.shape,g=Je(t.getModel("itemStyle"),p,!0);be(p,g),l.setShape(p)}}else{var f=t.get(["itemStyle","borderRadius"])||0;l.setShape("r",f)}l.useStyle(m);var b=t.getShallow("cursor");b&&l.attr("cursor",b);var y=c?u?n.r>=n.r0?"endArc":"startArc":n.endAngle>=n.startAngle?"endAngle":"startAngle":u?n.height>=0?"bottom":"top":n.width>=0?"right":"left",h=pa(t);va(l,h,{labelFetcher:i,labelDataIndex:a,defaultText:nr(i.getData(),a),inheritColor:m.fill,defaultOpacity:m.opacity,defaultOutsidePosition:y});var k=l.getTextContent();if(c&&k){var S=t.get(["label","position"]);l.textConfig.inside=S==="middle"?!0:null,$r(l,S==="outside"?y:S,_a(u),t.get(["label","rotate"]))}sr(k,h,i.getRawValue(a),function(_){return ir(r,_)});var V=t.getModel(["emphasis"]);ma(l,V.get("focus"),V.get("blurScope"),V.get("disabled")),fa(l,t),Xr(n)&&(l.style.fill="none",l.style.stroke="none",Fe(l.states,function(_){_.style&&(_.style.fill=_.style.stroke="none")}))}function qr(l,r){var a=l.get(["itemStyle","borderColor"]);if(!a||a==="none")return 0;var t=l.get(["itemStyle","borderWidth"])||0,n=isNaN(r.width)?Number.MAX_VALUE:Math.abs(r.width),i=isNaN(r.height)?Number.MAX_VALUE:Math.abs(r.height);return Math.min(t,n,i)}var Zr=function(){function l(){}return l}(),ea=function(l){Te(r,l);function r(a){var t=l.call(this,a)||this;return t.type="largeBar",t}return r.prototype.getDefaultShape=function(){return new Zr},r.prototype.buildPath=function(a,t){for(var n=t.points,i=this.baseDimIdx,u=1-this.baseDimIdx,c=[],m=[],f=this.barWidth,p=0;p<n.length;p+=3)m[i]=f,m[u]=n[p+2],c[i]=n[p+i],c[u]=n[p+u],a.rect(c[0],c[1],m[0],m[1])},r}(oa);function ta(l,r,a,t){var n=l.getData(),i=n.getLayout("valueAxisHorizontal")?1:0,u=n.getLayout("largeDataIndices"),c=n.getLayout("size"),m=l.getModel("backgroundStyle"),f=n.getLayout("largeBackgroundPoints");if(f){var p=new ea({shape:{points:f},incremental:!!t,silent:!0,z2:0});p.baseDimIdx=i,p.largeDataIndices=u,p.barWidth=c,p.useStyle(m.getItemStyle()),r.add(p),a&&a.push(p)}var g=new ea({shape:{points:n.getLayout("largePoints")},incremental:!!t,ignoreCoarsePointer:!0,z2:1});g.baseDimIdx=i,g.largeDataIndices=u,g.barWidth=c,r.add(g),g.useStyle(n.getVisual("style")),g.style.stroke=null,kt(g).seriesIndex=l.seriesIndex,l.get("silent")||(g.on("mousedown",aa),g.on("mousemove",aa)),a&&a.push(g)}var aa=or(function(l){var r=this,a=Jr(r,l.offsetX,l.offsetY);kt(r).dataIndex=a>=0?a:null},30,!1);function Jr(l,r,a){for(var t=l.baseDimIdx,n=1-t,i=l.shape.points,u=l.largeDataIndices,c=[],m=[],f=l.barWidth,p=0,g=i.length/3;p<g;p++){var b=p*3;if(m[t]=f,m[n]=i[b+2],c[t]=i[b+t],c[n]=i[b+n],m[n]<0&&(c[n]+=m[n],m[n]=-m[n]),r>=c[0]&&r<=c[0]+m[0]&&a>=c[1]&&a<=c[1]+m[1])return u[p]}return-1}function Da(l,r,a){if(ga(a,"cartesian2d")){var t=r,n=a.getArea();return{x:l?t.x:n.x,y:l?n.y:t.y,width:l?t.width:n.width,height:l?n.height:t.height}}else{var n=a.getArea(),i=r;return{cx:n.cx,cy:n.cy,r0:l?n.r0:i.r0,r:l?n.r:i.r,startAngle:l?i.startAngle:0,endAngle:l?i.endAngle:Math.PI*2}}}function Kr(l,r,a){var t=l.type==="polar"?ct:ca;return new t({shape:Da(r,a,l),silent:!0,z2:0})}const Qr=zr;function el(l){l.registerChartView(Qr),l.registerSeriesModel(jr),l.registerLayout(l.PRIORITY.VISUAL.LAYOUT,Ct(cr,"bar")),l.registerLayout(l.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,ur("bar")),l.registerProcessor(l.PRIORITY.PROCESSOR.STATISTIC,dr("bar")),l.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(r,a){var t=r.componentType||"series";a.eachComponent({mainType:t,query:r},function(n){r.sortInfo&&n.axis.setCategorySortInfo(r.sortInfo)})})}var ra=Math.PI*2,ut=Math.PI/180;function wa(l,r){return mr(l.getBoxLayoutParams(),{width:r.getWidth(),height:r.getHeight()})}function Sa(l,r){var a=wa(l,r),t=l.get("center"),n=l.get("radius");Ke(n)||(n=[0,n]);var i=we(a.width,r.getWidth()),u=we(a.height,r.getHeight()),c=Math.min(i,u),m=we(n[0],c/2),f=we(n[1],c/2),p,g,b=l.coordinateSystem;if(b){var y=b.dataToPoint(t);p=y[0]||0,g=y[1]||0}else Ke(t)||(t=[t,t]),p=we(t[0],i)+a.x,g=we(t[1],u)+a.y;return{cx:p,cy:g,r0:m,r:f}}function tl(l,r,a){r.eachSeriesByType(l,function(t){var n=t.getData(),i=n.mapDimension("value"),u=wa(t,a),c=Sa(t,a),m=c.cx,f=c.cy,p=c.r,g=c.r0,b=-t.get("startAngle")*ut,y=t.get("endAngle"),h=t.get("padAngle")*ut;y=y==="auto"?b-ra:-y*ut;var k=t.get("minAngle")*ut,S=k+h,V=0;n.each(i,function(le){!isNaN(le)&&V++});var _=n.getSum(i),P=Math.PI/(_||V)*2,F=t.get("clockwise"),W=t.get("roseType"),C=t.get("stillShowZeroSum"),Y=n.getDataExtent(i);Y[0]=0;var A=F?1:-1,$=[b,y],q=A*h/2;pr($,!F),b=$[0],y=$[1];var N=Aa(t);N.startAngle=b,N.endAngle=y,N.clockwise=F;var re=Math.abs(y-b),J=re,se=0,B=b;if(n.setLayout({viewRect:u,r:p}),n.each(i,function(le,Z){var K;if(isNaN(le)){n.setItemLayout(Z,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:F,cx:m,cy:f,r0:g,r:W?NaN:p});return}W!=="area"?K=_===0&&C?P:le*P:K=re/V,K<S?(K=S,J-=S):se+=le;var oe=B+A*K,Q=0,ie=0;h>K?(Q=B+A*K/2,ie=Q):(Q=B+q,ie=oe-q),n.setItemLayout(Z,{angle:K,startAngle:Q,endAngle:ie,clockwise:F,cx:m,cy:f,r0:g,r:W?vr(le,Y,[g,p]):p}),B=oe}),J<ra&&V)if(J<=.001){var he=re/V;n.each(i,function(le,Z){if(!isNaN(le)){var K=n.getItemLayout(Z);K.angle=he;var oe=0,Q=0;he<h?(oe=b+A*(Z+1/2)*he,Q=oe):(oe=b+A*Z*he+q,Q=b+A*(Z+1)*he-q),K.startAngle=oe,K.endAngle=Q}})}else P=J/se,B=b,n.each(i,function(le,Z){if(!isNaN(le)){var K=n.getItemLayout(Z),oe=K.angle===S?S:le*P,Q=0,ie=0;oe<h?(Q=B+A*oe/2,ie=Q):(Q=B+q,ie=B+A*oe-q),K.startAngle=Q,K.endAngle=ie,B+=A*oe}})})}var Aa=ha();function al(l){return{seriesType:l,reset:function(r,a){var t=a.findComponents({mainType:"legend"});if(!(!t||!t.length)){var n=r.getData();n.filterSelf(function(i){for(var u=n.getName(i),c=0;c<t.length;c++)if(!t[c].isSelected(u))return!1;return!0})}}}}var rl=Math.PI/180;function la(l,r,a,t,n,i,u,c,m,f){if(l.length<2)return;function p(k){for(var S=k.rB,V=S*S,_=0;_<k.list.length;_++){var P=k.list[_],F=Math.abs(P.label.y-a),W=t+P.len,C=W*W,Y=Math.sqrt(Math.abs((1-F*F/V)*C)),A=r+(Y+P.len2)*n,$=A-P.label.x,q=P.targetTextWidth-$*n;xa(P,q,!0),P.label.x=A}}function g(k){for(var S={list:[],maxY:0},V={list:[],maxY:0},_=0;_<k.length;_++)if(k[_].labelAlignTo==="none"){var P=k[_],F=P.label.y>a?V:S,W=Math.abs(P.label.y-a);if(W>=F.maxY){var C=P.label.x-r-P.len2*n,Y=t+P.len,A=Math.abs(C)<Y?Math.sqrt(W*W/(1-C*C/Y/Y)):Y;F.rB=A,F.maxY=W}F.list.push(P)}p(S),p(V)}for(var b=l.length,y=0;y<b;y++)if(l[y].position==="outer"&&l[y].labelAlignTo==="labelLine"){var h=l[y].label.x-f;l[y].linePoints[1][0]+=h,l[y].label.x=f}fr(l,m,m+u)&&g(l)}function ll(l,r,a,t,n,i,u,c){for(var m=[],f=[],p=Number.MAX_VALUE,g=-Number.MAX_VALUE,b=0;b<l.length;b++){var y=l[b].label;wt(l[b])||(y.x<r?(p=Math.min(p,y.x),m.push(l[b])):(g=Math.max(g,y.x),f.push(l[b])))}for(var b=0;b<l.length;b++){var h=l[b];if(!wt(h)&&h.linePoints){if(h.labelStyleWidth!=null)continue;var y=h.label,k=h.linePoints,S=void 0;h.labelAlignTo==="edge"?y.x<r?S=k[2][0]-h.labelDistance-u-h.edgeDistance:S=u+n-h.edgeDistance-k[2][0]-h.labelDistance:h.labelAlignTo==="labelLine"?y.x<r?S=p-u-h.bleedMargin:S=u+n-g-h.bleedMargin:y.x<r?S=y.x-u-h.bleedMargin:S=u+n-y.x-h.bleedMargin,h.targetTextWidth=S,xa(h,S)}}la(f,r,a,t,1,n,i,u,c,g),la(m,r,a,t,-1,n,i,u,c,p);for(var b=0;b<l.length;b++){var h=l[b];if(!wt(h)&&h.linePoints){var y=h.label,k=h.linePoints,V=h.labelAlignTo==="edge",_=y.style.padding,P=_?_[1]+_[3]:0,F=y.style.backgroundColor?0:P,W=h.rect.width+F,C=k[1][0]-k[2][0];V?y.x<r?k[2][0]=u+h.edgeDistance+W+h.labelDistance:k[2][0]=u+n-h.edgeDistance-W-h.labelDistance:(y.x<r?k[2][0]=y.x+h.labelDistance:k[2][0]=y.x-h.labelDistance,k[1][0]=k[2][0]+C),k[1][1]=k[2][1]=y.y}}}function xa(l,r,a){if(a===void 0&&(a=!1),l.labelStyleWidth==null){var t=l.label,n=t.style,i=l.rect,u=n.backgroundColor,c=n.padding,m=c?c[1]+c[3]:0,f=n.overflow,p=i.width+(u?0:m);if(r<p||a){var g=i.height;if(f&&f.match("break")){t.setStyle("backgroundColor",null),t.setStyle("width",r-m);var b=t.getBoundingRect();t.setStyle("width",Math.ceil(b.width)),t.setStyle("backgroundColor",u)}else{var y=r-m,h=r<p?y:a?y>l.unconstrainedWidth?null:y:null;t.setStyle("width",h)}var k=t.getBoundingRect();i.width=k.width;var S=(t.style.margin||0)+2.1;i.height=k.height+S,i.y-=(i.height-g)/2}}}function wt(l){return l.position==="center"}function nl(l){var r=l.getData(),a=[],t,n,i=!1,u=(l.get("minShowLabelAngle")||0)*rl,c=r.getLayout("viewRect"),m=r.getLayout("r"),f=c.width,p=c.x,g=c.y,b=c.height;function y(C){C.ignore=!0}function h(C){if(!C.ignore)return!0;for(var Y in C.states)if(C.states[Y].ignore===!1)return!0;return!1}r.each(function(C){var Y=r.getItemGraphicEl(C),A=Y.shape,$=Y.getTextContent(),q=Y.getTextGuideLine(),N=r.getItemModel(C),re=N.getModel("label"),J=re.get("position")||N.get(["emphasis","label","position"]),se=re.get("distanceToLabelLine"),B=re.get("alignTo"),he=we(re.get("edgeDistance"),f),le=re.get("bleedMargin"),Z=N.getModel("labelLine"),K=Z.get("length");K=we(K,f);var oe=Z.get("length2");if(oe=we(oe,f),Math.abs(A.endAngle-A.startAngle)<u){Fe($.states,y),$.ignore=!0,q&&(Fe(q.states,y),q.ignore=!0);return}if(h($)){var Q=(A.startAngle+A.endAngle)/2,ie=Math.cos(Q),De=Math.sin(Q),Ve,Oe,Qe,je;t=A.cx,n=A.cy;var Se=J==="inside"||J==="inner";if(J==="center")Ve=A.cx,Oe=A.cy,je="center";else{var $e=(Se?(A.r+A.r0)/2*ie:A.r*ie)+t,Ye=(Se?(A.r+A.r0)/2*De:A.r*De)+n;if(Ve=$e+ie*3,Oe=Ye+De*3,!Se){var et=$e+ie*(K+m-A.r),ze=Ye+De*(K+m-A.r),tt=et+(ie<0?-1:1)*oe,at=ze;B==="edge"?Ve=ie<0?p+he:p+f-he:Ve=tt+(ie<0?-se:se),Oe=at,Qe=[[$e,Ye],[et,ze],[tt,at]]}je=Se?"center":B==="edge"?ie>0?"right":"left":ie>0?"left":"right"}var Re=Math.PI,Ae=0,xe=re.get("rotate");if(xt(xe))Ae=xe*(Re/180);else if(J==="center")Ae=0;else if(xe==="radial"||xe===!0){var pt=ie<0?-Q+Re:-Q;Ae=pt}else if(xe==="tangential"&&J!=="outside"&&J!=="outer"){var ke=Math.atan2(ie,De);ke<0&&(ke=Re*2+ke);var vt=De>0;vt&&(ke=Re+ke),Ae=ke-Re}if(i=!!Ae,$.x=Ve,$.y=Oe,$.rotation=Ae,$.setStyle({verticalAlign:"middle"}),Se){$.setStyle({align:je});var Be=$.states.select;Be&&(Be.x+=$.x,Be.y+=$.y)}else{var Ce=$.getBoundingRect().clone();Ce.applyTransform($.getComputedTransform());var Ue=($.style.margin||0)+2.1;Ce.y-=Ue/2,Ce.height+=Ue,a.push({label:$,labelLine:q,position:J,len:K,len2:oe,minTurnAngle:Z.get("minTurnAngle"),maxSurfaceAngle:Z.get("maxSurfaceAngle"),surfaceNormal:new me(ie,De),linePoints:Qe,textAlign:je,labelDistance:se,labelAlignTo:B,edgeDistance:he,bleedMargin:le,rect:Ce,unconstrainedWidth:Ce.width,labelStyleWidth:$.style.width})}Y.setTextConfig({inside:Se})}}),!i&&l.get("avoidLabelOverlap")&&ll(a,t,n,m,f,b,p,g);for(var k=0;k<a.length;k++){var S=a[k],V=S.label,_=S.labelLine,P=isNaN(V.x)||isNaN(V.y);if(V){V.setStyle({align:S.textAlign}),P&&(Fe(V.states,y),V.ignore=!0);var F=V.states.select;F&&(F.x+=V.x,F.y+=V.y)}if(_){var W=S.linePoints;P||!W?(Fe(_.states,y),_.ignore=!0):(Tr(W,S.minTurnAngle),Vr(W,S.surfaceNormal,S.maxSurfaceAngle),_.setShape({points:W}),V.__hostTarget.textGuideLineConfig={anchor:new me(W[0][0],W[0][1])})}}}var sl=function(l){Te(r,l);function r(a,t,n){var i=l.call(this)||this;i.z2=2;var u=new gr;return i.setTextContent(u),i.updateData(a,t,n,!0),i}return r.prototype.updateData=function(a,t,n,i){var u=this,c=a.hostModel,m=a.getItemModel(t),f=m.getModel("emphasis"),p=a.getItemLayout(t),g=be(Je(m.getModel("itemStyle"),p,!0),p);if(isNaN(g.startAngle)){u.setShape(g);return}if(i){u.setShape(g);var b=c.getShallow("animationType");c.ecModel.ssr?(Ne(u,{scaleX:0,scaleY:0},c,{dataIndex:t,isFrom:!0}),u.originX=g.cx,u.originY=g.cy):b==="scale"?(u.shape.r=p.r0,Ne(u,{shape:{r:p.r}},c,t)):n!=null?(u.setShape({startAngle:n,endAngle:n}),Ne(u,{shape:{startAngle:p.startAngle,endAngle:p.endAngle}},c,t)):(u.shape.endAngle=p.startAngle,Ee(u,{shape:{endAngle:p.endAngle}},c,t))}else ua(u),Ee(u,{shape:g},c,t);u.useStyle(a.getItemVisual(t,"style")),fa(u,m);var y=(p.startAngle+p.endAngle)/2,h=c.get("selectedOffset"),k=Math.cos(y)*h,S=Math.sin(y)*h,V=m.getShallow("cursor");V&&u.attr("cursor",V),this._updateLabel(c,a,t),u.ensureState("emphasis").shape=be({r:p.r+(f.get("scale")&&f.get("scaleSize")||0)},Je(f.getModel("itemStyle"),p)),be(u.ensureState("select"),{x:k,y:S,shape:Je(m.getModel(["select","itemStyle"]),p)}),be(u.ensureState("blur"),{shape:Je(m.getModel(["blur","itemStyle"]),p)});var _=u.getTextGuideLine(),P=u.getTextContent();_&&be(_.ensureState("select"),{x:k,y:S}),be(P.ensureState("select"),{x:k,y:S}),ma(this,f.get("focus"),f.get("blurScope"),f.get("disabled"))},r.prototype._updateLabel=function(a,t,n){var i=this,u=t.getItemModel(n),c=u.getModel("labelLine"),m=t.getItemVisual(n,"style"),f=m&&m.fill,p=m&&m.opacity;va(i,pa(u),{labelFetcher:t.hostModel,labelDataIndex:n,inheritColor:f,defaultOpacity:p,defaultText:a.getFormattedLabel(n,"normal")||t.getName(n)});var g=i.getTextContent();i.setTextConfig({position:null,rotation:null}),g.attr({z2:10});var b=a.get(["label","position"]);if(b!=="outside"&&b!=="outer")i.removeTextGuideLine();else{var y=this.getTextGuideLine();y||(y=new na,this.setTextGuideLine(y)),Nr(this,Er(u),{stroke:f,opacity:hr(c.get(["lineStyle","opacity"]),p,1)})}},r}(ct),ol=function(l){Te(r,l);function r(){var a=l!==null&&l.apply(this,arguments)||this;return a.ignoreLabelLineUpdate=!0,a}return r.prototype.render=function(a,t,n,i){var u=a.getData(),c=this._data,m=this.group,f;if(!c&&u.count()>0){for(var p=u.getItemLayout(0),g=1;isNaN(p&&p.startAngle)&&g<u.count();++g)p=u.getItemLayout(g);p&&(f=p.startAngle)}if(this._emptyCircleSector&&m.remove(this._emptyCircleSector),u.count()===0&&a.get("showEmptyCircle")){var b=Aa(a),y=new ct({shape:be(Sa(a,n),b)});y.useStyle(a.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=y,m.add(y)}u.diff(c).add(function(h){var k=new sl(u,h,f);u.setItemGraphicEl(h,k),m.add(k)}).update(function(h,k){var S=c.getItemGraphicEl(k);S.updateData(u,h,f),S.off("click"),m.add(S),u.setItemGraphicEl(h,S)}).remove(function(h){var k=c.getItemGraphicEl(h);St(k,a,h)}).execute(),nl(a),a.get("animationTypeUpdate")!=="expansion"&&(this._data=u)},r.prototype.dispose=function(){},r.prototype.containPoint=function(a,t){var n=t.getData(),i=n.getItemLayout(0);if(i){var u=a[0]-i.cx,c=a[1]-i.cy,m=Math.sqrt(u*u+c*c);return m<=i.r&&m>=i.r0}},r.type="pie",r}(da);const il=ol;function ul(l,r,a){r=Ke(r)&&{coordDimensions:r}||be({encodeDefine:l.getEncode()},r);var t=l.getSource(),n=yr(t,r).dimensions,i=new br(n,l);return i.initData(t,a),i}var dl=function(){function l(r,a){this._getDataWithEncodedVisual=r,this._getRawData=a}return l.prototype.getAllNames=function(){var r=this._getRawData();return r.mapArray(r.getName)},l.prototype.containName=function(r){var a=this._getRawData();return a.indexOfName(r)>=0},l.prototype.indexOfName=function(r){var a=this._getDataWithEncodedVisual();return a.indexOfName(r)},l.prototype.getItemVisual=function(r,a){var t=this._getDataWithEncodedVisual();return t.getItemVisual(r,a)},l}();const cl=dl;var pl=ha(),vl=function(l){Te(r,l);function r(){return l!==null&&l.apply(this,arguments)||this}return r.prototype.init=function(a){l.prototype.init.apply(this,arguments),this.legendVisualProvider=new cl(zt(this.getData,this),zt(this.getRawData,this)),this._defaultLabelLine(a)},r.prototype.mergeOption=function(){l.prototype.mergeOption.apply(this,arguments)},r.prototype.getInitialData=function(){return ul(this,{coordDimensions:["value"],encodeDefaulter:Ct(_r,this)})},r.prototype.getDataParams=function(a){var t=this.getData(),n=pl(t),i=n.seats;if(!i){var u=[];t.each(t.mapDimension("value"),function(m){u.push(m)}),i=n.seats=Dr(u,t.hostModel.get("percentPrecision"))}var c=l.prototype.getDataParams.call(this,a);return c.percent=i[a]||0,c.$vars.push("percent"),c},r.prototype._defaultLabelLine=function(a){wr(a,"labelLine",["show"]);var t=a.labelLine,n=a.emphasis.labelLine;t.show=t.show&&a.label.show,n.show=n.show&&a.emphasis.label.show},r.type="series.pie",r.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},r}(At);const ml=vl;function fl(l){return{seriesType:l,reset:function(r,a){var t=r.getData();t.filterSelf(function(n){var i=t.mapDimension("value"),u=t.get(i,n);return!(xt(u)&&!isNaN(u)&&u<0)})}}}function gl(l){l.registerChartView(il),l.registerSeriesModel(ml),Sr("pie",l.registerAction),l.registerLayout(Ct(tl,"pie")),l.registerProcessor(al("pie")),l.registerProcessor(fl("pie"))}const hl={class:"reports-page"},yl={class:"page-header"},bl={class:"header-actions"},_l={class:"stat-content"},Dl={class:"stat-value"},wl={class:"stat-content"},Sl={class:"stat-value"},Al={class:"stat-content"},xl={class:"stat-value"},kl={class:"stat-content"},Cl={class:"stat-value"},Il={class:"stat-content"},Ml={class:"stat-value"},Ll={class:"stat-content"},Pl={class:"stat-value"},Tl={class:"stat-content"},Vl={class:"stat-value"},Rl={class:"stat-content"},Nl={class:"stat-value"},El={class:"stat-content"},Ol={class:"stat-value"},jl={class:"stat-content"},Wl={class:"stat-value"},Hl={class:"card-header"},Fl={class:"record-count"},$l={class:"pagination"},Yl={class:"employee-detail-content"},zl={class:"summary-item"},Ul={class:"summary-value"},Bl={class:"summary-item"},Gl={class:"summary-value"},Xl={class:"summary-item"},ql={class:"summary-value"},Zl={class:"summary-item"},Jl={class:"summary-value"},Kl={class:"project-detail-content"},Ql={class:"summary-item"},en={class:"summary-value"},tn={class:"summary-item"},an={class:"summary-value"},rn={class:"summary-item"},ln={class:"summary-value"},nn={class:"summary-item"},sn={class:"summary-value"},on={class:"summary-item"},un={class:"summary-value"},dn={class:"summary-item"},cn={class:"summary-value"},pn={class:"dialog-footer"},vn=Oa({__name:"Reports",setup(l){Ar([xr,kr,gl,el,Cr,Ir,Mr,Lr]);const r=ne(!1),a=ne(!1),t=ne([]),n=ne([]),i=ne([]),u=ne([]),c=ne([]),m=ne(!1),f=ne([]),p=ne(!1),g=ne(!1),b=ne(!1),y=ne(!1),h=ne([]),k=ne([]),S=ne(null),V=ne(null),_=ne("daily"),P=ne(""),F=ne(!1),W=ne(!1),C=Xe({format:"excel",content:"filtered",fields:[],filename:"",includeSummary:!0,includeCharts:!1,pdfMethod:"html2canvas",pdfLanguage:"english"}),Y=Xe({totalEmployees:0,presentToday:0,absentToday:0,lateToday:0,averageWorkHours:0,totalOvertimeHours:0}),A=Xe({projectId:null,userId:null,status:""}),$=ne([]),q=Xe({page:1,size:20,total:0}),N=Xe({totalDays:0,normalDays:0,abnormalDays:0,totalHours:0}),re=Vt(()=>({title:{text:"出勤趋势",left:"center"},tooltip:{trigger:"axis"},legend:{data:["出勤人数","异常人数"],bottom:0},grid:{left:"3%",right:"4%",bottom:"10%",containLabel:!0},xAxis:{type:"category",data:se.value.dates},yAxis:{type:"value"},series:[{name:"出勤人数",type:"line",data:se.value.attendance,smooth:!0,itemStyle:{color:"#67C23A"}},{name:"异常人数",type:"line",data:se.value.abnormal,smooth:!0,itemStyle:{color:"#F56C6C"}}]})),J=Vt(()=>({title:{text:"出勤分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"出勤统计",type:"pie",radius:"50%",data:[{value:N.normalDays,name:"正常出勤",itemStyle:{color:"#67C23A"}},{value:N.abnormalDays,name:"异常记录",itemStyle:{color:"#F56C6C"}},{value:Y.absentToday,name:"缺勤",itemStyle:{color:"#909399"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]})),se=ne({dates:[],attendance:[],abnormal:[]}),B=v=>new Date(v).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),he=v=>new Date(v).toLocaleString("zh-CN"),le=v=>{if(!v)return"-";const e=Math.floor(v/60),I=v%60;return`${e}h${I}m`},Z=v=>{const{columns:e,data:I}=v,D=[];return e.forEach((T,x)=>{if(x===0){D[x]="合计";return}if(T.property==="totalWorkMinutes"){const G=I.reduce((H,R)=>H+(R.totalWorkMinutes||0),0);D[x]=le(G)}else if(T.property==="checkInCount"||T.property==="checkOutCount"){const G=I.reduce((H,R)=>H+(R[T.property]||0),0);D[x]=G.toString()}else D[x]=""}),D},K=async()=>{try{const v=await Ha();if(v.success){const e=v.data;Object.assign(Y,{totalEmployees:e.totalUsers||0,presentToday:e.todayCheckIns||0,absentToday:e.abnormalRecords||0,lateToday:Math.floor((e.abnormalRecords||0)*.6),averageWorkHours:8,totalOvertimeHours:0})}}catch(v){console.error("Load realtime data error:",v)}},oe=()=>{q.page=1,Q()},Q=async()=>{r.value=!0;try{_.value==="daily"?await ie():_.value==="monthly"?await De():_.value==="employee-project"&&await Ve()}finally{r.value=!1}},ie=async()=>{var I,D,T;const v={page:q.page,limit:q.size,projectId:A.projectId,userId:A.userId,startDate:(I=$.value)==null?void 0:I[0],endDate:(D=$.value)==null?void 0:D[1]};A.status==="normal"?v.isAbnormal=!1:A.status==="abnormal"&&(v.isAbnormal=!0);const e=await ht(v);if(e.success){t.value=e.data.records||[],q.total=((T=e.data.pagination)==null?void 0:T.total)||0;const x=await Fa(v);if(x.success){const G=x.data.summary;N.totalDays=G.totalDays||0,N.normalDays=G.normalDays||0,N.abnormalDays=G.abnormalDays||0,N.totalHours=Math.round((G.totalWorkMinutes||0)/60)}await Oe()}else ee.error(e.message||"加载报表数据失败")},De=async()=>{const[v,e]=P.value?P.value.split("-"):[new Date().getFullYear().toString(),(new Date().getMonth()+1).toString()],I={year:v,month:e,projectId:A.projectId,userId:A.userId},D=await Et(I);if(D.success){const T=D.data;n.value=T.employees||[],q.total=n.value.length,n.value=n.value.map(x=>({...x,department:x.userRole||"未分配",totalWorkHours:parseFloat(x.totalWorkHours)||0,avgWorkHours:parseFloat(x.avgWorkHours)||0,attendanceRate:parseFloat(x.attendanceRate)||0})),T.summary&&(N.totalDays=T.summary.totalAttendanceDays||0,N.normalDays=T.summary.totalAttendanceDays-T.summary.totalAbnormalDays||0,N.abnormalDays=T.summary.totalAbnormalDays||0,N.totalHours=Math.round(n.value.reduce((x,G)=>x+G.totalWorkHours,0)))}else ee.error(D.message||"加载月度数据失败")},Ve=async()=>{var I,D,T;const v={page:q.page,limit:q.size,projectId:A.projectId,userId:A.userId,startDate:(I=$.value)==null?void 0:I[0],endDate:(D=$.value)==null?void 0:D[1]},e=await Ot(v);if(e.success){const x=e.data.records||[];q.total=((T=e.data.pagination)==null?void 0:T.total)||0,i.value=x.map(E=>({...E,role:E.userRole||"员工",attendanceDays:E.totalDays||0,totalWorkHours:E.workHours||0,averageWorkHours:E.avgWorkHours||0,attendanceRate:parseFloat(E.attendanceRate)||0}));const G=i.value.reduce((E,U)=>E+U.attendanceDays,0),H=i.value.reduce((E,U)=>E+U.workDays,0),R=i.value.reduce((E,U)=>E+U.absentDays,0),O=i.value.reduce((E,U)=>E+U.totalWorkHours,0);N.totalDays=G,N.normalDays=H,N.abnormalDays=R,N.totalHours=Math.round(O)}else ee.error(e.message||"加载员工项目数据失败")},Oe=async v=>{a.value=!0;try{const e=[],I=[],D=[];for(let T=6;T>=0;T--){const x=new Date;x.setDate(x.getDate()-T),e.push(x.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})),I.push(Math.floor(Math.random()*20)+10),D.push(Math.floor(Math.random()*5))}se.value={dates:e,attendance:I,abnormal:D}}finally{a.value=!1}},Qe=()=>{A.projectId=null,A.userId=null,A.status="",$.value=[],P.value="",q.page=1,Q()},je=v=>{q.size=v,Q()},Se=v=>{q.page=v,Q()},$e=async v=>{try{const e=await jt({userId:v.userId,date:v.date});e.success?(f.value=e.data||[],m.value=!0):ee.error(e.message||"加载详细记录失败")}catch(e){console.error("Load detail records error:",e),ee.error("加载详细记录失败")}},Ye=async v=>{S.value=v,b.value=!0,p.value=!0;try{const[e,I]=P.value?P.value.split("-"):[new Date().getFullYear().toString(),(new Date().getMonth()+1).toString()],D=await $a({userId:v.userId,year:e,month:I});if(D.success){const T=D.data.dailyRecords||[];h.value=T.map(x=>{var R,O,E,U,L,ce;let G=null,H=null;if(x.pairs&&x.pairs.length>0&&(G=(O=(R=x.pairs[0])==null?void 0:R.checkIn)==null?void 0:O.createdAt,H=(U=(E=x.pairs[x.pairs.length-1])==null?void 0:E.checkOut)==null?void 0:U.createdAt),!G&&x.unpaired&&x.unpaired.length>0){const X=x.unpaired.filter(Ie=>Ie.type==="in");X.length>0&&(G=(L=X[0].record)==null?void 0:L.createdAt)}if(!H&&x.unpaired&&x.unpaired.length>0){const X=x.unpaired.filter(Ie=>Ie.type==="out");X.length>0&&(H=(ce=X[X.length-1].record)==null?void 0:ce.createdAt)}return{date:x.date,projectName:"多项目",firstCheckIn:G,lastCheckOut:H,checkInCount:x.checkInCount||0,checkOutCount:x.checkOutCount||0,totalWorkMinutes:x.totalWorkMinutes||0,isAbnormal:x.isAbnormal||!1,userId:D.data.userId}})}else ee.error(D.message||"加载员工详细记录失败"),h.value=[]}catch(e){console.error("Load employee daily records error:",e),ee.error("加载员工详细记录失败"),h.value=[]}finally{b.value=!1}},et=async v=>{var e,I;V.value=v,y.value=!0,g.value=!0;try{const D=await ht({userId:v.userId,projectId:v.projectId,startDate:(e=$.value)==null?void 0:e[0],endDate:(I=$.value)==null?void 0:I[1],page:1,limit:1e3});D.success?k.value=D.data.records||[]:(ee.error(D.message||"加载项目详细记录失败"),k.value=[])}catch(D){console.error("Load project daily records error:",D),ee.error("加载项目详细记录失败"),k.value=[]}finally{y.value=!1}},ze=async v=>{var e,I;try{const D=await jt({userId:v.userId||((e=S.value)==null?void 0:e.userId)||((I=V.value)==null?void 0:I.userId),date:v.date});D.success?(f.value=D.data||[],m.value=!0):ee.error(D.message||"加载详细打卡记录失败")}catch(D){console.error("Load day detail records error:",D),ee.error("加载详细打卡记录失败")}},tt=v=>{v==="custom"?Re():at(v)},at=async v=>{const e=Ue();W.value=!0;try{await xe({format:v,content:"filtered",fields:Ce(),filename:e,includeSummary:!0,includeCharts:v==="excel",pdfMethod:"html2canvas",pdfLanguage:"english"})}finally{W.value=!1}},Re=()=>{C.format="excel",C.content="filtered",C.fields=Ce(),C.filename=Ue(),C.includeSummary=!0,C.includeCharts=!1,C.pdfMethod="html2canvas",C.pdfLanguage="english",F.value=!0},Ae=async()=>{if(!C.filename.trim()){ee.warning("请输入文件名");return}if(C.fields.length===0){ee.warning("请至少选择一个导出字段");return}W.value=!0;try{await xe(C),F.value=!1}finally{W.value=!1}},xe=async v=>{var e,I;try{const D=await pt(v);if(!D.records||D.records.length===0){ee.warning("没有数据可导出");return}await ka(D,v);try{await Ya({type:_.value,format:v.format,recordCount:D.records.length,filters:{startDate:(e=$.value)==null?void 0:e[0],endDate:(I=$.value)==null?void 0:I[1],selectedMonth:P.value,projectId:A.projectId,userId:A.userId,status:A.status}})}catch(T){console.warn("Backend export API call failed:",T)}}catch(D){console.error("Export error:",D),ee.error("导出失败，请重试")}},pt=async v=>{var T,x,G,H;let e=[],I=null;if(v.content==="current")_.value==="daily"?e=t.value:_.value==="monthly"?e=n.value:_.value==="employee-project"&&(e=i.value);else if(v.content==="all"||v.content==="filtered"){if(_.value==="daily"){const R=await ht({projectId:A.projectId,userId:A.userId,startDate:(T=$.value)==null?void 0:T[0],endDate:(x=$.value)==null?void 0:x[1],page:1,limit:1e4});e=R.success?R.data.records||[]:[]}else if(_.value==="monthly"){const[R,O]=P.value?P.value.split("-"):[new Date().getFullYear().toString(),(new Date().getMonth()+1).toString()],E=await Et({year:R,month:O,projectId:A.projectId,userId:A.userId});e=E.success?E.data.employees||[]:[],I=E.success?E.data.summary:null}else if(_.value==="employee-project"){const R=await Ot({projectId:A.projectId,userId:A.userId,startDate:(G=$.value)==null?void 0:G[0],endDate:(H=$.value)==null?void 0:H[1],page:1,limit:1e4});e=R.success?R.data.records||[]:[]}}const D=e.map(R=>{const O={};return v.fields.forEach(E=>{O[E]=R[E]}),O});return{records:D,summary:v.includeSummary?I||N:null,metadata:{viewMode:_.value,exportTime:new Date().toISOString(),totalRecords:D.length,filters:{dateRange:$.value,selectedMonth:P.value,projectId:A.projectId,userId:A.userId,status:A.status}}}},ke=async()=>{var v;try{const e=await za();e.success?u.value=((v=e.data)==null?void 0:v.projects)||[]:ee.error(e.message||"加载工程列表失败")}catch(e){console.error("Load projects error:",e),ee.error("加载工程列表失败")}},vt=async()=>{var v;try{const e=await Ua();e.success?c.value=((v=e.data)==null?void 0:v.users)||[]:ee.error(e.message||"加载用户列表失败")}catch(e){console.error("Load users error:",e),ee.error("加载用户列表失败")}},Ce=()=>_.value==="daily"?["date","userName","projectName","firstCheckIn","lastCheckOut","totalWorkMinutes","isAbnormal"]:_.value==="monthly"?["userName","department","attendanceDays","totalWorkHours","attendanceRate"]:_.value==="employee-project"?["userName","projectName","clientName","attendanceDays","totalWorkHours","attendanceRate"]:[],Ue=v=>{const e=new Date,I=e.toISOString().split("T")[0],D=e.toTimeString().split(" ")[0].replace(/:/g,"");let T="";return _.value==="daily"?T="考勤明细":_.value==="monthly"?T="月度统计":_.value==="employee-project"&&(T="员工项目统计"),`${T}_${I}_${D}`},Be=(v,e)=>{const I=document.createElement("a");I.href=v,I.download=e,document.body.appendChild(I),I.click(),document.body.removeChild(I)},ka=async(v,e)=>{var I;console.log("开始前端导出:",{format:e.format,recordCount:(I=v.records)==null?void 0:I.length});try{if(e.format==="csv")mt(v,e);else if(e.format==="excel")await Ca(v,e);else if(e.format==="pdf")await Ia(v,e);else throw new Error(`不支持的导出格式: ${e.format}`)}catch(D){throw console.error("前端导出失败:",D),D}},mt=(v,e)=>{try{const I=v.records||[];if(I.length===0){ee.warning("没有数据可导出");return}console.log("CSV导出开始:",{recordCount:I.length,fields:e.fields});const T=[e.fields.map(H=>rt(H)).join(","),...I.map(H=>e.fields.map(R=>{let O=H[R]||"";return R==="totalWorkMinutes"?O=le(O):R==="isAbnormal"?O=O?"异常":"正常":R==="attendanceRate"?O=`${O}%`:R==="firstCheckIn"||R==="lastCheckOut"?O=O?B(O):"-":R==="date"&&(O=O?new Date(O).toLocaleDateString("zh-CN"):""),typeof O=="string"&&(O.includes(",")||O.includes('"')||O.includes(`
`))?O=`"${O.replace(/"/g,'""')}"`:O=`"${O}"`,O}).join(","))].join(`
`),x=new Blob(["\uFEFF"+T],{type:"text/csv;charset=utf-8;"}),G=URL.createObjectURL(x);Be(G,`${e.filename}.csv`),URL.revokeObjectURL(G),ee.success(`CSV导出成功！共导出 ${I.length} 条记录`)}catch(I){throw console.error("CSV导出失败:",I),ee.error("CSV导出失败"),I}},Ca=async(v,e)=>{var I;try{const D=await Ze(()=>import("./xlsx-6ed613d4.js"),[]),T=v.records||[];if(T.length===0){ee.warning("没有数据可导出");return}const x=D.utils.book_new(),H=[e.fields.map(E=>rt(E)),...T.map(E=>e.fields.map(U=>{let L=E[U]||"";return U==="totalWorkMinutes"?L=le(L):U==="isAbnormal"?L=L?"异常":"正常":U==="attendanceRate"?L=`${L}%`:U==="firstCheckIn"||U==="lastCheckOut"?L=L?B(L):"-":U==="date"&&(L=L?new Date(L).toLocaleDateString("zh-CN"):""),L}))],R=D.utils.aoa_to_sheet(H),O=e.fields.map(E=>E==="date"?{wch:12}:E==="userName"?{wch:10}:E==="projectName"?{wch:20}:E==="firstCheckIn"||E==="lastCheckOut"?{wch:15}:E==="totalWorkMinutes"?{wch:12}:{wch:10});if(R["!cols"]=O,D.utils.book_append_sheet(x,R,"考勤数据"),e.includeSummary&&v.summary){const E=[["统计项目","数值"],["总天数",v.summary.totalDays||0],["正常天数",v.summary.normalDays||0],["异常天数",v.summary.abnormalDays||0],["总工时",`${v.summary.totalHours||0}小时`]];_.value==="monthly"&&v.summary.totalEmployees&&(E.push(["总员工数",v.summary.totalEmployees]),E.push(["平均出勤率",`${v.summary.avgAttendanceRate||0}%`]));const U=D.utils.aoa_to_sheet(E);U["!cols"]=[{wch:15},{wch:15}],D.utils.book_append_sheet(x,U,"统计汇总")}if(v.metadata){const E=[["导出信息",""],["导出时间",new Date(v.metadata.exportTime).toLocaleString("zh-CN")],["数据类型",v.metadata.viewMode==="daily"?"按日统计":v.metadata.viewMode==="monthly"?"按月统计":"员工项目统计"],["记录总数",v.metadata.totalRecords],["",""],["筛选条件",""],["时间范围",((I=v.metadata.filters.dateRange)==null?void 0:I.join(" 至 "))||"全部"],["选择月份",v.metadata.filters.selectedMonth||"全部"],["工程筛选",v.metadata.filters.projectId?"已筛选":"全部"],["用户筛选",v.metadata.filters.userId?"已筛选":"全部"],["状态筛选",v.metadata.filters.status||"全部"]],U=D.utils.aoa_to_sheet(E);U["!cols"]=[{wch:12},{wch:25}],D.utils.book_append_sheet(x,U,"导出信息")}D.writeFile(x,`${e.filename}.xlsx`),ee.success("Excel导出成功！")}catch(D){console.error("Excel export error:",D),ee.error("Excel导出失败，已改为CSV格式"),mt(v,e)}},Ia=async(v,e)=>{var I;try{if(console.log("PDF导出开始:",{options:e,recordCount:(I=v.records)==null?void 0:I.length}),(v.records||[]).length===0){ee.warning("没有数据可导出");return}e.pdfMethod==="html2canvas"?await Ma(v,e):await Pa(v,e)}catch(D){console.error("PDF export error:",D),ee.error("PDF导出失败，已改为CSV格式"),mt(v,e)}},Ma=async(v,e)=>{const I=(await Ze(()=>import("./html2canvas.esm-e0a7d97b.js"),[])).default,D=(await Ze(()=>import("./jspdf.es.min-a9118a6f.js").then(G=>G.j),["assets/jspdf.es.min-a9118a6f.js","assets/index-fea52ff4.js","assets/index-b50506e3.css"])).default,T=La(v,e),x=document.createElement("div");x.innerHTML=T,x.style.position="absolute",x.style.left="-9999px",x.style.top="-9999px",x.style.width="1200px",x.style.backgroundColor="white",x.style.padding="20px",x.style.fontFamily='Arial, "Microsoft YaHei", "SimHei", sans-serif',document.body.appendChild(x);try{const G=await I(x,{scale:2,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff"}),H=new D({orientation:"landscape",unit:"mm",format:"a4"}),R=G.toDataURL("image/png"),O=277,E=G.height*O/G.width,U=190;let L=E,ce=10;for(H.addImage(R,"PNG",10,ce,O,E),L-=U;L>=0;)ce=L-E+10,H.addPage(),H.addImage(R,"PNG",10,ce,O,E),L-=U;H.save(`${e.filename}.pdf`),ee.success("PDF导出成功！（HTML转图片方式，完美支持中文）")}finally{document.body.removeChild(x)}},La=(v,e)=>{const I=v.records||[],T={daily:"考勤明细报表",monthly:"月度统计报表","employee-project":"员工项目统计报表"}[_.value]||"考勤报表",G=e.fields.map(O=>rt(O)).map(O=>`<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: center;">${O}</th>`).join(""),H=I.map(O=>`<tr>${e.fields.map(U=>{let L=O[U]||"";return U==="totalWorkMinutes"?L=le(L):U==="isAbnormal"?L=L?"异常":"正常":U==="attendanceRate"?L=`${L}%`:U==="firstCheckIn"||U==="lastCheckOut"?L=L?B(L):"-":U==="date"&&(L=L?new Date(L).toLocaleDateString("zh-CN"):""),`<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${L}</td>`}).join("")}</tr>`).join("");let R="";return e.includeSummary&&v.summary&&(R=`
      <div style="margin-top: 30px;">
        <h3 style="color: #333; margin-bottom: 15px;">统计汇总</h3>
        <table style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; width: 150px;"><strong>总天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${v.summary.totalDays||0}</td>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; width: 150px;"><strong>正常天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${v.summary.normalDays||0}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>异常天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${v.summary.abnormalDays||0}</td>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>总工时</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${v.summary.totalHours||0}小时</td>
          </tr>
        </table>
      </div>
    `),`
    <div style="font-family: Arial, 'Microsoft YaHei', 'SimHei', sans-serif; color: #333;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333; margin: 0; font-size: 24px;">${T}</h1>
        <p style="color: #666; margin: 10px 0; font-size: 14px;">导出时间: ${new Date().toLocaleString("zh-CN")}</p>
      </div>

      <table style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
        <thead>
          <tr>${G}</tr>
        </thead>
        <tbody>
          ${H}
        </tbody>
      </table>

      ${R}

      <div style="margin-top: 30px; text-align: center; color: #999; font-size: 12px;">
        <p>共 ${I.length} 条记录</p>
      </div>
    </div>
  `},Pa=async(v,e)=>{const I=(await Ze(()=>import("./jspdf.es.min-a9118a6f.js").then(L=>L.j),["assets/jspdf.es.min-a9118a6f.js","assets/index-fea52ff4.js","assets/index-b50506e3.css"])).default,D=v.records||[],T=new I({orientation:"landscape",unit:"mm",format:"a4"});T.setFont("helvetica");const x=e.pdfLanguage==="english";T.setFontSize(16);let G="";x?G={daily:"Daily Attendance Report",monthly:"Monthly Attendance Report","employee-project":"Employee Project Report"}[_.value]||"Attendance Report":G=_.value==="daily"?"考勤明细报表":_.value==="monthly"?"月度统计报表":"员工项目统计报表",T.text(G,20,20),T.setFontSize(10);const H=x?`Export Time: ${new Date().toISOString().replace("T"," ").substring(0,19)}`:`导出时间: ${new Date().toLocaleString("zh-CN")}`;T.text(H,20,30);const R=x?e.fields.map(L=>Ta(L)):e.fields.map(L=>rt(L)),O=D.map(L=>e.fields.map(ce=>{let X=L[ce]||"";return ce==="totalWorkMinutes"?X=le(X):ce==="isAbnormal"?X=x?X?"Abnormal":"Normal":X?"异常":"正常":ce==="attendanceRate"?X=`${X}%`:ce==="firstCheckIn"||ce==="lastCheckOut"?X=X?B(X):"-":ce==="date"&&(X=X?new Date(X).toLocaleDateString(x?"en-US":"zh-CN"):""),x&&typeof X=="string"&&(X=Va(X)),String(X)})),E=(await Ze(()=>import("./jspdf.plugin.autotable-9efb0360.js"),[])).default;if(E(T,{head:[R],body:O,startY:40,styles:{fontSize:8,cellPadding:2,font:"helvetica"},headStyles:{fillColor:[64,158,255],textColor:255,font:"helvetica",fontStyle:"bold"},alternateRowStyles:{fillColor:[245,245,245]},margin:{top:40,left:20,right:20}}),e.includeSummary&&v.summary){const L=T.lastAutoTable.finalY+20;T.setFontSize(12);const ce=x?"Summary Statistics":"统计汇总";T.text(ce,20,L);const X=x?[["Total Days",String(v.summary.totalDays||0)],["Normal Days",String(v.summary.normalDays||0)],["Abnormal Days",String(v.summary.abnormalDays||0)],["Total Hours",`${v.summary.totalHours||0}h`]]:[["总天数",String(v.summary.totalDays||0)],["正常天数",String(v.summary.normalDays||0)],["异常天数",String(v.summary.abnormalDays||0)],["总工时",`${v.summary.totalHours||0}小时`]];_.value==="monthly"&&v.summary.totalEmployees&&(x?(X.push(["Total Employees",String(v.summary.totalEmployees)]),X.push(["Avg Attendance Rate",`${v.summary.avgAttendanceRate||0}%`])):(X.push(["总员工数",String(v.summary.totalEmployees)]),X.push(["平均出勤率",`${v.summary.avgAttendanceRate||0}%`]))),E(T,{head:[x?["Item","Value"]:["统计项目","数值"]],body:X,startY:L+10,styles:{fontSize:10,cellPadding:3,font:"helvetica"},headStyles:{fillColor:[67,194,58],textColor:255,font:"helvetica",fontStyle:"bold"},margin:{left:20}})}T.save(`${e.filename}.pdf`);const U=x?"PDF导出成功！（文本模式-英文版本）":"PDF导出成功！（文本模式-中文版本，可能存在字体显示问题）";ee.success(U)},rt=v=>({date:"日期",userName:"姓名",projectName:"工程",firstCheckIn:"首次签到",lastCheckOut:"最后签退",checkInCount:"签到次数",checkOutCount:"签退次数",totalWorkMinutes:"工作时长",isAbnormal:"状态",department:"部门",attendanceDays:"出勤天数",absentDays:"缺勤天数",abnormalDays:"异常天数",totalWorkHours:"总工时",avgWorkHours:"平均工时",attendanceRate:"出勤率",clientName:"客户",role:"角色",averageWorkHours:"平均工时"})[v]||v,Ta=v=>({date:"Date",userName:"Name",projectName:"Project",firstCheckIn:"First Check-in",lastCheckOut:"Last Check-out",checkInCount:"Check-in Count",checkOutCount:"Check-out Count",totalWorkMinutes:"Work Duration",isAbnormal:"Status",department:"Department",attendanceDays:"Attendance Days",absentDays:"Absent Days",abnormalDays:"Abnormal Days",totalWorkHours:"Total Hours",avgWorkHours:"Avg Hours",attendanceRate:"Attendance Rate",clientName:"Client",role:"Role",averageWorkHours:"Avg Hours"})[v]||v,Va=v=>{if(!v||typeof v!="string")return v;const e={张经理:"Zhang Manager",李工程师:"Li Engineer",王主管:"Wang Supervisor",CBD商业中心建设:"CBD Commercial Center Construction",住宅小区项目:"Residential Community Project",办公楼建设:"Office Building Construction",工程部:"Engineering Dept",管理部:"Management Dept",财务部:"Finance Dept",人事部:"HR Dept",项目经理:"Project Manager",工程师:"Engineer",技术员:"Technician",管理员:"Administrator",员工:"Employee",恒大集团:"Evergrande Group",万科集团:"Vanke Group",碧桂园:"Country Garden",正常:"Normal",异常:"Abnormal",迟到:"Late",早退:"Early Leave",缺勤:"Absent"};if(e[v])return e[v];let I=v;return Object.entries(e).forEach(([D,T])=>{I=I.replace(new RegExp(D,"g"),T)}),/[\u4e00-\u9fa5]/.test(I)&&(I=I.replace(/[\u4e00-\u9fa5]/g,D=>`U${D.charCodeAt(0).toString(16).toUpperCase()}`)),I},Ra=()=>{const v=new Date;P.value=`${v.getFullYear()}-${String(v.getMonth()+1).padStart(2,"0")}`};return ja(()=>{Ra(),K(),ke(),vt(),Q(),setInterval(K,3e4)}),(v,e)=>{var Lt,Pt,Tt;const I=ae("el-icon"),D=ae("el-button"),T=ae("el-dropdown-item"),x=ae("el-dropdown-menu"),G=ae("el-dropdown"),H=ae("el-card"),R=ae("el-col"),O=ae("el-row"),E=ae("el-radio-button"),U=ae("el-radio-group"),L=ae("el-form-item"),ce=ae("el-date-picker"),X=ae("el-option"),Ie=ae("el-select"),It=ae("el-form"),j=ae("el-table-column"),We=ae("el-tag"),He=ae("el-table"),Na=ae("el-pagination"),lt=ae("el-dialog"),ye=ae("el-radio"),te=ae("el-checkbox"),ft=ae("el-checkbox-group"),Ea=ae("el-input"),Mt=ae("el-switch"),Ge=Wa("loading");return de(),gt("div",hl,[w("div",yl,[e[29]||(e[29]=w("h2",null,"考勤报表",-1)),w("div",bl,[s(G,{onCommand:tt,trigger:"click"},{dropdown:o(()=>[s(x,null,{default:o(()=>[s(T,{command:"excel"},{default:o(()=>[s(I,null,{default:o(()=>[s(Me(Ba))]),_:1}),e[25]||(e[25]=M(" 导出Excel "))]),_:1,__:[25]}),s(T,{command:"csv"},{default:o(()=>[s(I,null,{default:o(()=>[s(Me(Ga))]),_:1}),e[26]||(e[26]=M(" 导出CSV "))]),_:1,__:[26]}),s(T,{command:"pdf"},{default:o(()=>[s(I,null,{default:o(()=>[s(Me(Xa))]),_:1}),e[27]||(e[27]=M(" 导出PDF "))]),_:1,__:[27]}),s(T,{divided:"",command:"custom"},{default:o(()=>[s(I,null,{default:o(()=>[s(Me(qa))]),_:1}),e[28]||(e[28]=M(" 自定义导出 "))]),_:1,__:[28]})]),_:1})]),default:o(()=>[s(D,{type:"success",icon:Me(Za)},{default:o(()=>[e[24]||(e[24]=M(" 导出报表 ")),s(I,{class:"el-icon--right"},{default:o(()=>[s(Me(Ja))]),_:1})]),_:1,__:[24]},8,["icon"])]),_:1})])]),s(O,{gutter:20,class:"realtime-stats"},{default:o(()=>[s(R,{span:4},{default:o(()=>[s(H,{class:"stat-card realtime"},{default:o(()=>[w("div",_l,[e[30]||(e[30]=w("div",{class:"stat-icon"},"👥",-1)),w("div",Dl,z(Y.totalEmployees),1),e[31]||(e[31]=w("div",{class:"stat-label"},"总员工数",-1))])]),_:1})]),_:1}),s(R,{span:4},{default:o(()=>[s(H,{class:"stat-card realtime"},{default:o(()=>[w("div",wl,[e[32]||(e[32]=w("div",{class:"stat-icon"},"✅",-1)),w("div",Sl,z(Y.presentToday),1),e[33]||(e[33]=w("div",{class:"stat-label"},"今日出勤",-1))])]),_:1})]),_:1}),s(R,{span:4},{default:o(()=>[s(H,{class:"stat-card realtime"},{default:o(()=>[w("div",Al,[e[34]||(e[34]=w("div",{class:"stat-icon"},"❌",-1)),w("div",xl,z(Y.absentToday),1),e[35]||(e[35]=w("div",{class:"stat-label"},"今日缺勤",-1))])]),_:1})]),_:1}),s(R,{span:4},{default:o(()=>[s(H,{class:"stat-card realtime"},{default:o(()=>[w("div",kl,[e[36]||(e[36]=w("div",{class:"stat-icon"},"⏰",-1)),w("div",Cl,z(Y.lateToday),1),e[37]||(e[37]=w("div",{class:"stat-label"},"今日迟到",-1))])]),_:1})]),_:1}),s(R,{span:4},{default:o(()=>[s(H,{class:"stat-card realtime"},{default:o(()=>[w("div",Il,[e[38]||(e[38]=w("div",{class:"stat-icon"},"📊",-1)),w("div",Ml,z(Y.averageWorkHours)+"h",1),e[39]||(e[39]=w("div",{class:"stat-label"},"平均工时",-1))])]),_:1})]),_:1}),s(R,{span:4},{default:o(()=>[s(H,{class:"stat-card realtime"},{default:o(()=>[w("div",Ll,[e[40]||(e[40]=w("div",{class:"stat-icon"},"⏱️",-1)),w("div",Pl,z(Y.totalOvertimeHours)+"h",1),e[41]||(e[41]=w("div",{class:"stat-label"},"总加班时长",-1))])]),_:1})]),_:1})]),_:1}),s(H,{class:"filter-card"},{default:o(()=>[s(It,{model:A,inline:""},{default:o(()=>[s(L,{label:"视图模式"},{default:o(()=>[s(U,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=d=>_.value=d),onChange:oe},{default:o(()=>[s(E,{value:"daily"},{default:o(()=>e[42]||(e[42]=[M("按日统计")])),_:1,__:[42]}),s(E,{value:"monthly"},{default:o(()=>e[43]||(e[43]=[M("按月统计")])),_:1,__:[43]}),s(E,{value:"employee-project"},{default:o(()=>e[44]||(e[44]=[M("员工项目")])),_:1,__:[44]})]),_:1},8,["modelValue"])]),_:1}),_.value==="daily"?(de(),pe(L,{key:0,label:"时间范围"},{default:o(()=>[s(ce,{modelValue:$.value,"onUpdate:modelValue":e[1]||(e[1]=d=>$.value=d),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})):ge("",!0),_.value==="monthly"?(de(),pe(L,{key:1,label:"选择月份"},{default:o(()=>[s(ce,{modelValue:P.value,"onUpdate:modelValue":e[2]||(e[2]=d=>P.value=d),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM"},null,8,["modelValue"])]),_:1})):ge("",!0),_.value==="employee-project"?(de(),pe(L,{key:2,label:"时间范围"},{default:o(()=>[s(ce,{modelValue:$.value,"onUpdate:modelValue":e[3]||(e[3]=d=>$.value=d),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})):ge("",!0),s(L,{label:"工程"},{default:o(()=>[s(Ie,{modelValue:A.projectId,"onUpdate:modelValue":e[4]||(e[4]=d=>A.projectId=d),placeholder:"请选择工程",clearable:""},{default:o(()=>[(de(!0),gt(Rt,null,Nt(u.value,d=>(de(),pe(X,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(L,{label:"用户"},{default:o(()=>[s(Ie,{modelValue:A.userId,"onUpdate:modelValue":e[5]||(e[5]=d=>A.userId=d),placeholder:"请选择用户",clearable:"",filterable:""},{default:o(()=>[(de(!0),gt(Rt,null,Nt(c.value,d=>(de(),pe(X,{key:d.id,label:d.realName,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),_.value==="daily"?(de(),pe(L,{key:3,label:"状态"},{default:o(()=>[s(Ie,{modelValue:A.status,"onUpdate:modelValue":e[6]||(e[6]=d=>A.status=d),placeholder:"请选择状态",clearable:""},{default:o(()=>[s(X,{label:"全部",value:""}),s(X,{label:"正常",value:"normal"}),s(X,{label:"异常",value:"abnormal"})]),_:1},8,["modelValue"])]),_:1})):ge("",!0),s(L,null,{default:o(()=>[s(D,{type:"primary",onClick:Q},{default:o(()=>e[45]||(e[45]=[M("查询")])),_:1,__:[45]}),s(D,{onClick:Qe},{default:o(()=>e[46]||(e[46]=[M("重置")])),_:1,__:[46]})]),_:1})]),_:1},8,["model"])]),_:1}),_.value!=="employee-project"?(de(),pe(O,{key:0,gutter:20,class:"charts-row"},{default:o(()=>[s(R,{span:12},{default:o(()=>[s(H,null,{header:o(()=>e[47]||(e[47]=[w("span",null,"出勤趋势图",-1)])),default:o(()=>[s(Me(Ut),{class:"chart",option:re.value,loading:a.value,autoresize:""},null,8,["option","loading"])]),_:1})]),_:1}),s(R,{span:12},{default:o(()=>[s(H,null,{header:o(()=>e[48]||(e[48]=[w("span",null,"出勤分布",-1)])),default:o(()=>[s(Me(Ut),{class:"chart",option:J.value,loading:a.value,autoresize:""},null,8,["option","loading"])]),_:1})]),_:1})]),_:1})):ge("",!0),s(O,{gutter:20,class:"stats-row"},{default:o(()=>[s(R,{span:6},{default:o(()=>[s(H,{class:"stat-card"},{default:o(()=>[w("div",Tl,[w("div",Vl,z(N.totalDays),1),e[49]||(e[49]=w("div",{class:"stat-label"},"总天数",-1))])]),_:1})]),_:1}),s(R,{span:6},{default:o(()=>[s(H,{class:"stat-card"},{default:o(()=>[w("div",Rl,[w("div",Nl,z(N.normalDays),1),e[50]||(e[50]=w("div",{class:"stat-label"},"正常天数",-1))])]),_:1})]),_:1}),s(R,{span:6},{default:o(()=>[s(H,{class:"stat-card"},{default:o(()=>[w("div",El,[w("div",Ol,z(N.abnormalDays),1),e[51]||(e[51]=w("div",{class:"stat-label"},"异常天数",-1))])]),_:1})]),_:1}),s(R,{span:6},{default:o(()=>[s(H,{class:"stat-card"},{default:o(()=>[w("div",jl,[w("div",Wl,z(N.totalHours),1),e[52]||(e[52]=w("div",{class:"stat-label"},"总工时",-1))])]),_:1})]),_:1})]),_:1}),s(H,null,{header:o(()=>[w("div",Hl,[w("span",null,z(_.value==="daily"?"考勤详情":_.value==="monthly"?"月度统计":"员工项目统计"),1),w("span",Fl,"共 "+z(q.total)+" 条记录",1)])]),default:o(()=>[_.value==="daily"?qe((de(),pe(He,{key:0,data:t.value,style:{width:"100%"},"summary-method":Z,"show-summary":""},{default:o(()=>[s(j,{prop:"date",label:"日期",width:"120"}),s(j,{prop:"userName",label:"姓名",width:"100"}),s(j,{prop:"projectName",label:"工程",width:"150"}),s(j,{prop:"firstCheckIn",label:"首次签到",width:"120"},{default:o(({row:d})=>[M(z(d.firstCheckIn?B(d.firstCheckIn):"-"),1)]),_:1}),s(j,{prop:"lastCheckOut",label:"最后签退",width:"120"},{default:o(({row:d})=>[M(z(d.lastCheckOut?B(d.lastCheckOut):"-"),1)]),_:1}),s(j,{prop:"checkInCount",label:"签到次数",width:"100"}),s(j,{prop:"checkOutCount",label:"签退次数",width:"100"}),s(j,{prop:"totalWorkMinutes",label:"工作时长",width:"120"},{default:o(({row:d})=>[M(z(le(d.totalWorkMinutes)),1)]),_:1}),s(j,{prop:"isAbnormal",label:"状态",width:"80"},{default:o(({row:d})=>[s(We,{type:d.isAbnormal?"danger":"success"},{default:o(()=>[M(z(d.isAbnormal?"异常":"正常"),1)]),_:2},1032,["type"])]),_:1}),s(j,{label:"操作",width:"120"},{default:o(({row:d})=>[s(D,{text:"",type:"primary",onClick:nt=>$e(d)},{default:o(()=>e[53]||(e[53]=[M(" 查看详情 ")])),_:2,__:[53]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ge,r.value]]):ge("",!0),_.value==="monthly"?qe((de(),pe(He,{key:1,data:n.value,style:{width:"100%"}},{default:o(()=>[s(j,{prop:"userName",label:"姓名",width:"120"}),s(j,{prop:"department",label:"部门",width:"120"}),s(j,{prop:"attendanceDays",label:"出勤天数",width:"100"}),s(j,{prop:"absentDays",label:"缺勤天数",width:"100"}),s(j,{prop:"abnormalDays",label:"异常天数",width:"100"}),s(j,{prop:"totalWorkHours",label:"总工时",width:"100"},{default:o(({row:d})=>[M(z(d.totalWorkHours)+"h ",1)]),_:1}),s(j,{prop:"averageWorkHours",label:"平均工时",width:"100"},{default:o(({row:d})=>[M(z(d.averageWorkHours)+"h ",1)]),_:1}),s(j,{prop:"attendanceRate",label:"出勤率",width:"100"},{default:o(({row:d})=>[s(We,{type:d.attendanceRate>=90?"success":d.attendanceRate>=80?"warning":"danger"},{default:o(()=>[M(z(d.attendanceRate)+"% ",1)]),_:2},1032,["type"])]),_:1}),s(j,{label:"操作",width:"120"},{default:o(({row:d})=>[s(D,{text:"",type:"primary",onClick:nt=>Ye(d)},{default:o(()=>e[54]||(e[54]=[M(" 查看详情 ")])),_:2,__:[54]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ge,r.value]]):ge("",!0),_.value==="employee-project"?qe((de(),pe(He,{key:2,data:i.value,style:{width:"100%"}},{default:o(()=>[s(j,{prop:"userName",label:"姓名",width:"120"}),s(j,{prop:"projectName",label:"工程",width:"150"}),s(j,{prop:"clientName",label:"客户",width:"120"}),s(j,{prop:"role",label:"角色",width:"100"}),s(j,{prop:"attendanceDays",label:"出勤天数",width:"100"}),s(j,{prop:"totalWorkHours",label:"总工时",width:"100"},{default:o(({row:d})=>[M(z(d.totalWorkHours)+"h ",1)]),_:1}),s(j,{prop:"avgWorkHours",label:"平均工时",width:"100"},{default:o(({row:d})=>[M(z(d.avgWorkHours)+"h ",1)]),_:1}),s(j,{prop:"attendanceRate",label:"出勤率",width:"100"},{default:o(({row:d})=>[s(We,{type:d.attendanceRate>=90?"success":d.attendanceRate>=80?"warning":"danger"},{default:o(()=>[M(z(d.attendanceRate)+"% ",1)]),_:2},1032,["type"])]),_:1}),s(j,{label:"操作",width:"120"},{default:o(({row:d})=>[s(D,{text:"",type:"primary",onClick:nt=>et(d)},{default:o(()=>e[55]||(e[55]=[M(" 查看详情 ")])),_:2,__:[55]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ge,r.value]]):ge("",!0),w("div",$l,[s(Na,{"current-page":q.page,"onUpdate:currentPage":e[7]||(e[7]=d=>q.page=d),"page-size":q.size,"onUpdate:pageSize":e[8]||(e[8]=d=>q.size=d),"page-sizes":[10,20,50,100],total:q.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:je,onCurrentChange:Se},null,8,["current-page","page-size","total"])])]),_:1}),s(lt,{modelValue:m.value,"onUpdate:modelValue":e[9]||(e[9]=d=>m.value=d),title:"考勤详情",width:"800px"},{default:o(()=>[s(He,{data:f.value,style:{width:"100%"}},{default:o(()=>[s(j,{prop:"checkType",label:"类型",width:"80"},{default:o(({row:d})=>[s(We,{type:d.checkType==="in"?"success":"warning"},{default:o(()=>[M(z(d.checkType==="in"?"签到":"签退"),1)]),_:2},1032,["type"])]),_:1}),s(j,{prop:"createdAt",label:"时间",width:"160"},{default:o(({row:d})=>[M(z(he(d.createdAt)),1)]),_:1}),s(j,{prop:"locationName",label:"地点",width:"150"}),s(j,{prop:"address",label:"地址","min-width":"200"}),s(j,{prop:"distance",label:"距离",width:"80"},{default:o(({row:d})=>[M(z(d.distance?Math.round(d.distance)+"m":"-"),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),s(lt,{modelValue:p.value,"onUpdate:modelValue":e[10]||(e[10]=d=>p.value=d),title:`${(Lt=S.value)==null?void 0:Lt.userName} - ${P.value}月详细考勤记录`,width:"1200px",top:"5vh"},{default:o(()=>[w("div",Yl,[s(H,{class:"summary-card",shadow:"never"},{header:o(()=>e[56]||(e[56]=[w("span",null,"月度汇总",-1)])),default:o(()=>[s(O,{gutter:20},{default:o(()=>[s(R,{span:6},{default:o(()=>{var d;return[w("div",zl,[w("div",Ul,z(((d=S.value)==null?void 0:d.attendanceDays)||0),1),e[57]||(e[57]=w("div",{class:"summary-label"},"出勤天数",-1))])]}),_:1}),s(R,{span:6},{default:o(()=>{var d;return[w("div",Bl,[w("div",Gl,z(((d=S.value)==null?void 0:d.normalDays)||0),1),e[58]||(e[58]=w("div",{class:"summary-label"},"正常天数",-1))])]}),_:1}),s(R,{span:6},{default:o(()=>{var d;return[w("div",Xl,[w("div",ql,z(((d=S.value)==null?void 0:d.abnormalDays)||0),1),e[59]||(e[59]=w("div",{class:"summary-label"},"异常天数",-1))])]}),_:1}),s(R,{span:6},{default:o(()=>{var d;return[w("div",Zl,[w("div",Jl,z(((d=S.value)==null?void 0:d.totalWorkHours)||0)+"h",1),e[60]||(e[60]=w("div",{class:"summary-label"},"总工时",-1))])]}),_:1})]),_:1})]),_:1}),s(H,{class:"detail-card"},{header:o(()=>e[61]||(e[61]=[w("span",null,"每日详细记录",-1)])),default:o(()=>[qe((de(),pe(He,{data:h.value,style:{width:"100%"},"default-sort":{prop:"date",order:"descending"}},{default:o(()=>[s(j,{prop:"date",label:"日期",width:"120",sortable:""}),s(j,{prop:"projectName",label:"项目",width:"150"}),s(j,{prop:"firstCheckIn",label:"首次签到",width:"120"},{default:o(({row:d})=>[M(z(d.firstCheckIn?B(d.firstCheckIn):"-"),1)]),_:1}),s(j,{prop:"lastCheckOut",label:"最后签退",width:"120"},{default:o(({row:d})=>[M(z(d.lastCheckOut?B(d.lastCheckOut):"-"),1)]),_:1}),s(j,{prop:"checkInCount",label:"签到次数",width:"100"}),s(j,{prop:"checkOutCount",label:"签退次数",width:"100"}),s(j,{prop:"totalWorkMinutes",label:"工作时长",width:"120"},{default:o(({row:d})=>[M(z(le(d.totalWorkMinutes)),1)]),_:1}),s(j,{prop:"isAbnormal",label:"状态",width:"80"},{default:o(({row:d})=>[s(We,{type:d.isAbnormal?"danger":"success"},{default:o(()=>[M(z(d.isAbnormal?"异常":"正常"),1)]),_:2},1032,["type"])]),_:1}),s(j,{label:"操作",width:"100"},{default:o(({row:d})=>[s(D,{text:"",type:"primary",onClick:nt=>ze(d)},{default:o(()=>e[62]||(e[62]=[M(" 查看打卡 ")])),_:2,__:[62]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ge,b.value]])]),_:1})])]),_:1},8,["modelValue","title"]),s(lt,{modelValue:g.value,"onUpdate:modelValue":e[11]||(e[11]=d=>g.value=d),title:`${(Pt=V.value)==null?void 0:Pt.userName} - ${(Tt=V.value)==null?void 0:Tt.projectName} 项目详细记录`,width:"1200px",top:"5vh"},{default:o(()=>[w("div",Kl,[s(H,{class:"summary-card",shadow:"never"},{header:o(()=>e[63]||(e[63]=[w("span",null,"项目汇总",-1)])),default:o(()=>[s(O,{gutter:20},{default:o(()=>[s(R,{span:4},{default:o(()=>{var d;return[w("div",Ql,[w("div",en,z(((d=V.value)==null?void 0:d.attendanceDays)||0),1),e[64]||(e[64]=w("div",{class:"summary-label"},"出勤天数",-1))])]}),_:1}),s(R,{span:4},{default:o(()=>{var d;return[w("div",tn,[w("div",an,z(((d=V.value)==null?void 0:d.totalWorkHours)||0)+"h",1),e[65]||(e[65]=w("div",{class:"summary-label"},"总工时",-1))])]}),_:1}),s(R,{span:4},{default:o(()=>{var d;return[w("div",rn,[w("div",ln,z(((d=V.value)==null?void 0:d.averageWorkHours)||0)+"h",1),e[66]||(e[66]=w("div",{class:"summary-label"},"平均工时",-1))])]}),_:1}),s(R,{span:4},{default:o(()=>{var d;return[w("div",nn,[w("div",sn,z(((d=V.value)==null?void 0:d.attendanceRate)||0)+"%",1),e[67]||(e[67]=w("div",{class:"summary-label"},"出勤率",-1))])]}),_:1}),s(R,{span:4},{default:o(()=>{var d;return[w("div",on,[w("div",un,z(((d=V.value)==null?void 0:d.role)||"员工"),1),e[68]||(e[68]=w("div",{class:"summary-label"},"项目角色",-1))])]}),_:1}),s(R,{span:4},{default:o(()=>{var d;return[w("div",dn,[w("div",cn,z(((d=V.value)==null?void 0:d.clientName)||"未知"),1),e[69]||(e[69]=w("div",{class:"summary-label"},"客户",-1))])]}),_:1})]),_:1})]),_:1}),s(H,{class:"detail-card"},{header:o(()=>e[70]||(e[70]=[w("span",null,"项目考勤记录",-1)])),default:o(()=>[qe((de(),pe(He,{data:k.value,style:{width:"100%"},"default-sort":{prop:"date",order:"descending"}},{default:o(()=>[s(j,{prop:"date",label:"日期",width:"120",sortable:""}),s(j,{prop:"firstCheckIn",label:"首次签到",width:"120"},{default:o(({row:d})=>[M(z(d.firstCheckIn?B(d.firstCheckIn):"-"),1)]),_:1}),s(j,{prop:"lastCheckOut",label:"最后签退",width:"120"},{default:o(({row:d})=>[M(z(d.lastCheckOut?B(d.lastCheckOut):"-"),1)]),_:1}),s(j,{prop:"checkInCount",label:"签到次数",width:"100"}),s(j,{prop:"checkOutCount",label:"签退次数",width:"100"}),s(j,{prop:"totalWorkMinutes",label:"工作时长",width:"120"},{default:o(({row:d})=>[M(z(le(d.totalWorkMinutes)),1)]),_:1}),s(j,{prop:"isAbnormal",label:"状态",width:"80"},{default:o(({row:d})=>[s(We,{type:d.isAbnormal?"danger":"success"},{default:o(()=>[M(z(d.isAbnormal?"异常":"正常"),1)]),_:2},1032,["type"])]),_:1}),s(j,{label:"操作",width:"100"},{default:o(({row:d})=>[s(D,{text:"",type:"primary",onClick:nt=>ze(d)},{default:o(()=>e[71]||(e[71]=[M(" 查看打卡 ")])),_:2,__:[71]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ge,y.value]])]),_:1})])]),_:1},8,["modelValue","title"]),s(lt,{modelValue:F.value,"onUpdate:modelValue":e[23]||(e[23]=d=>F.value=d),title:"自定义导出设置",width:"600px"},{footer:o(()=>[w("div",pn,[s(D,{onClick:e[22]||(e[22]=d=>F.value=!1)},{default:o(()=>e[110]||(e[110]=[M("取消")])),_:1,__:[110]}),s(D,{type:"primary",onClick:Ae,loading:W.value},{default:o(()=>e[111]||(e[111]=[M(" 开始导出 ")])),_:1,__:[111]},8,["loading"])])]),default:o(()=>[s(It,{model:C,"label-width":"120px"},{default:o(()=>[s(L,{label:"导出格式"},{default:o(()=>[s(U,{modelValue:C.format,"onUpdate:modelValue":e[12]||(e[12]=d=>C.format=d)},{default:o(()=>[s(ye,{value:"excel"},{default:o(()=>e[72]||(e[72]=[M("Excel (.xlsx)")])),_:1,__:[72]}),s(ye,{value:"csv"},{default:o(()=>e[73]||(e[73]=[M("CSV (.csv)")])),_:1,__:[73]}),s(ye,{value:"pdf"},{default:o(()=>e[74]||(e[74]=[M("PDF (.pdf)")])),_:1,__:[74]})]),_:1},8,["modelValue"])]),_:1}),s(L,{label:"导出内容"},{default:o(()=>[s(U,{modelValue:C.content,"onUpdate:modelValue":e[13]||(e[13]=d=>C.content=d)},{default:o(()=>[s(ye,{value:"current"},{default:o(()=>e[75]||(e[75]=[M("当前页面数据")])),_:1,__:[75]}),s(ye,{value:"all"},{default:o(()=>e[76]||(e[76]=[M("全部数据")])),_:1,__:[76]}),s(ye,{value:"filtered"},{default:o(()=>e[77]||(e[77]=[M("筛选后的数据")])),_:1,__:[77]})]),_:1},8,["modelValue"])]),_:1}),_.value==="daily"?(de(),pe(L,{key:0,label:"包含字段"},{default:o(()=>[s(ft,{modelValue:C.fields,"onUpdate:modelValue":e[14]||(e[14]=d=>C.fields=d)},{default:o(()=>[s(te,{value:"date"},{default:o(()=>e[78]||(e[78]=[M("日期")])),_:1,__:[78]}),s(te,{value:"userName"},{default:o(()=>e[79]||(e[79]=[M("姓名")])),_:1,__:[79]}),s(te,{value:"projectName"},{default:o(()=>e[80]||(e[80]=[M("工程")])),_:1,__:[80]}),s(te,{value:"firstCheckIn"},{default:o(()=>e[81]||(e[81]=[M("首次签到")])),_:1,__:[81]}),s(te,{value:"lastCheckOut"},{default:o(()=>e[82]||(e[82]=[M("最后签退")])),_:1,__:[82]}),s(te,{value:"checkInCount"},{default:o(()=>e[83]||(e[83]=[M("签到次数")])),_:1,__:[83]}),s(te,{value:"checkOutCount"},{default:o(()=>e[84]||(e[84]=[M("签退次数")])),_:1,__:[84]}),s(te,{value:"totalWorkMinutes"},{default:o(()=>e[85]||(e[85]=[M("工作时长")])),_:1,__:[85]}),s(te,{value:"isAbnormal"},{default:o(()=>e[86]||(e[86]=[M("状态")])),_:1,__:[86]})]),_:1},8,["modelValue"])]),_:1})):ge("",!0),_.value==="monthly"?(de(),pe(L,{key:1,label:"包含字段"},{default:o(()=>[s(ft,{modelValue:C.fields,"onUpdate:modelValue":e[15]||(e[15]=d=>C.fields=d)},{default:o(()=>[s(te,{value:"userName"},{default:o(()=>e[87]||(e[87]=[M("姓名")])),_:1,__:[87]}),s(te,{value:"department"},{default:o(()=>e[88]||(e[88]=[M("部门")])),_:1,__:[88]}),s(te,{value:"attendanceDays"},{default:o(()=>e[89]||(e[89]=[M("出勤天数")])),_:1,__:[89]}),s(te,{value:"absentDays"},{default:o(()=>e[90]||(e[90]=[M("缺勤天数")])),_:1,__:[90]}),s(te,{value:"abnormalDays"},{default:o(()=>e[91]||(e[91]=[M("异常天数")])),_:1,__:[91]}),s(te,{value:"totalWorkHours"},{default:o(()=>e[92]||(e[92]=[M("总工时")])),_:1,__:[92]}),s(te,{value:"avgWorkHours"},{default:o(()=>e[93]||(e[93]=[M("平均工时")])),_:1,__:[93]}),s(te,{value:"attendanceRate"},{default:o(()=>e[94]||(e[94]=[M("出勤率")])),_:1,__:[94]})]),_:1},8,["modelValue"])]),_:1})):ge("",!0),_.value==="employee-project"?(de(),pe(L,{key:2,label:"包含字段"},{default:o(()=>[s(ft,{modelValue:C.fields,"onUpdate:modelValue":e[16]||(e[16]=d=>C.fields=d)},{default:o(()=>[s(te,{value:"userName"},{default:o(()=>e[95]||(e[95]=[M("姓名")])),_:1,__:[95]}),s(te,{value:"projectName"},{default:o(()=>e[96]||(e[96]=[M("工程")])),_:1,__:[96]}),s(te,{value:"clientName"},{default:o(()=>e[97]||(e[97]=[M("客户")])),_:1,__:[97]}),s(te,{value:"role"},{default:o(()=>e[98]||(e[98]=[M("角色")])),_:1,__:[98]}),s(te,{value:"attendanceDays"},{default:o(()=>e[99]||(e[99]=[M("出勤天数")])),_:1,__:[99]}),s(te,{value:"totalWorkHours"},{default:o(()=>e[100]||(e[100]=[M("总工时")])),_:1,__:[100]}),s(te,{value:"averageWorkHours"},{default:o(()=>e[101]||(e[101]=[M("平均工时")])),_:1,__:[101]}),s(te,{value:"attendanceRate"},{default:o(()=>e[102]||(e[102]=[M("出勤率")])),_:1,__:[102]})]),_:1},8,["modelValue"])]),_:1})):ge("",!0),s(L,{label:"文件名"},{default:o(()=>[s(Ea,{modelValue:C.filename,"onUpdate:modelValue":e[17]||(e[17]=d=>C.filename=d),placeholder:"请输入文件名"},{append:o(()=>[M("."+z(C.format==="excel"?"xlsx":C.format),1)]),_:1},8,["modelValue"])]),_:1}),s(L,{label:"包含汇总"},{default:o(()=>[s(Mt,{modelValue:C.includeSummary,"onUpdate:modelValue":e[18]||(e[18]=d=>C.includeSummary=d)},null,8,["modelValue"]),e[103]||(e[103]=w("span",{class:"form-tip"},"是否在导出文件中包含统计汇总信息",-1))]),_:1,__:[103]}),C.format==="excel"?(de(),pe(L,{key:3,label:"包含图表"},{default:o(()=>[s(Mt,{modelValue:C.includeCharts,"onUpdate:modelValue":e[19]||(e[19]=d=>C.includeCharts=d)},null,8,["modelValue"]),e[104]||(e[104]=w("span",{class:"form-tip"},"是否在Excel中包含图表（仅Excel格式支持）",-1))]),_:1,__:[104]})):ge("",!0),C.format==="pdf"?(de(),pe(L,{key:4,label:"PDF生成方式"},{default:o(()=>[s(U,{modelValue:C.pdfMethod,"onUpdate:modelValue":e[20]||(e[20]=d=>C.pdfMethod=d)},{default:o(()=>[s(ye,{value:"html2canvas"},{default:o(()=>e[105]||(e[105]=[M("HTML转图片（推荐，完美支持中文）")])),_:1,__:[105]}),s(ye,{value:"text"},{default:o(()=>e[106]||(e[106]=[M("文本模式（英文/中文可选）")])),_:1,__:[106]})]),_:1},8,["modelValue"]),e[107]||(e[107]=w("div",{class:"form-tip"}," HTML转图片方式可以完美显示中文，但文件较大。文本模式文件小但中文可能乱码。 ",-1))]),_:1,__:[107]})):ge("",!0),C.format==="pdf"&&C.pdfMethod==="text"?(de(),pe(L,{key:5,label:"PDF语言"},{default:o(()=>[s(U,{modelValue:C.pdfLanguage,"onUpdate:modelValue":e[21]||(e[21]=d=>C.pdfLanguage=d)},{default:o(()=>[s(ye,{value:"english"},{default:o(()=>e[108]||(e[108]=[M("英文（避免乱码）")])),_:1,__:[108]}),s(ye,{value:"chinese"},{default:o(()=>e[109]||(e[109]=[M("中文（可能乱码）")])),_:1,__:[109]})]),_:1},8,["modelValue"])]),_:1})):ge("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const hn=Pr(vn,[["__scopeId","data-v-7df297e1"]]);export{hn as default};
