import{r as x,a as A,p as ye,aH as P,E as r,b as n,M as ve,o as z,c as le,e as o,f as t,w as l,y as _,g as D,i,s as ae,q as B,x as se,z as H,l as ke,aI as Ie,m as Pe,aJ as Ve}from"./index-fea52ff4.js";import{_ as we}from"./_plugin-vue_export-helper-c27b6911.js";const xe={class:"security-management"},Ae={class:"stat-content"},ze={class:"stat-number"},Ce={class:"stat-content"},Le={class:"stat-number"},Ue={class:"stat-content"},Se={class:"stat-number"},Be={class:"stat-content"},Me={class:"stat-number"},Te={class:"tab-content"},he={class:"table-header"},De={class:"pagination"},$e={class:"tab-content"},Ee={class:"table-header"},Ne={key:0},je={class:"pagination"},Fe={class:"tab-content"},Re={__name:"SecurityManagement",setup(qe){const J=x("attempts"),$=x(!1),E=x(!1),f=A({totalAttempts:0,failedAttempts:0,blockedIPs:0,riskLevel:"低"}),N=x([]),y=A({search:"",success:"",riskLevel:""}),v=A({page:1,size:20,total:0}),G=x([]),L=x(new Set),j=A({search:""}),V=A({page:1,size:20,total:0}),U=x(!1),d=A({ipAddress:"",reason:"",blockType:"temporary",duration:1440}),p=A({ipMaxAttempts:10,ipLockMinutes:30,userMaxAttempts:5,userLockMinutes:15});ye(()=>{oe(),w(),k(),K()});const oe=async()=>{try{console.log("开始加载安全统计...");const s=await P.get("/security/login-stats");if(console.log("安全统计响应:",s),s.success){const e=s.data;console.log("安全统计数据:",e),f.totalAttempts=e.todayAttempts||0,f.failedAttempts=e.todayFailed||0,f.blockedIPs=e.blockedIPs||0;const m=e.todayAttempts>0?e.todayFailed/e.todayAttempts:0;m>.5?f.riskLevel="高":m>.3?f.riskLevel="中":f.riskLevel="低",console.log("更新后的安全统计:",f)}}catch(s){console.error("加载安全统计失败:",s)}},w=async()=>{$.value=!0;try{const s={page:v.page,size:v.size,search:y.search,success:y.success,riskLevel:y.riskLevel};console.log("开始加载登录记录，参数:",s);const e=await P.get("/security/login-attempts",{params:s});console.log("登录记录响应:",e),e.success&&(console.log("登录记录数据:",e.data),N.value=e.data.records,v.total=e.data.total,console.log("更新后的登录记录:",N.value),console.log("分页信息:",v))}catch(s){console.error("加载登录记录失败:",s),r.error("加载登录记录失败")}finally{$.value=!1}},k=async()=>{E.value=!0;try{const s={page:V.page,size:V.size,search:j.search},e=await P.get("/security/ip-blacklist",{params:s});e.success&&(G.value=e.data.records,V.total=e.data.total,L.value.clear(),e.data.records.forEach(m=>{me(m)&&L.value.add(m.ipAddress)}))}catch(s){console.error("加载IP黑名单失败:",s),r.error("加载IP黑名单失败")}finally{E.value=!1}},K=async()=>{try{const s=await P.get("/security/settings");s.success&&Object.assign(p,s.data)}catch(s){console.error("加载安全设置失败:",s)}},ne=async s=>{try{await H.confirm(`确定要封禁IP地址 ${s} 吗？`,"确认封禁",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await P.post("/security/block-ip",{ipAddress:s,reason:"从登录记录手动封禁",duration:1440});e.success?(r.success("IP封禁成功"),L.value.add(s),k()):r.error(e.message||"IP封禁失败")}catch(e){e!=="cancel"&&(console.error("封禁IP失败:",e),r.error("封禁IP失败"))}},Q=async s=>{try{await H.confirm(`确定要解封IP地址 ${s} 吗？`,"确认解封",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await P.post("/security/unlock-ip",{ipAddress:s});e.success?(r.success("IP解封成功"),L.value.delete(s),k()):r.error(e.message||"IP解封失败")}catch(e){e!=="cancel"&&(console.error("解封IP失败:",e),r.error("解封IP失败"))}},re=()=>{Object.assign(d,{ipAddress:"",reason:"",blockType:"temporary",duration:1440}),U.value=!0},ie=async()=>{if(!d.ipAddress||!d.reason){r.error("请填写完整信息");return}try{const s={ipAddress:d.ipAddress,reason:d.reason};d.blockType==="temporary"&&(s.duration=d.duration);const e=await P.post("/security/block-ip",s);e.data.success?(r.success("IP封禁成功"),U.value=!1,k()):r.error(e.data.message||"IP封禁失败")}catch(s){console.error("添加IP封禁失败:",s),r.error("添加IP封禁失败")}},de=async s=>{try{await H.confirm("确定要删除这条IP黑名单记录吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await P.delete(`/security/ip-blacklist/${s}`);e.data.success?(r.success("删除成功"),k()):r.error(e.data.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除IP黑名单失败:",e),r.error("删除失败"))}},ue=async()=>{var s;try{console.log("发送安全设置数据:",p);const e=await P.post("/security/settings",p);if(console.log("保存安全设置响应:",e),e&&e.success)r.success("安全设置保存成功");else{const m=(e==null?void 0:e.message)||"保存失败";r.error(m)}}catch(e){console.error("保存安全设置失败:",e),e.response?(console.error("错误响应:",e.response.data),r.error(((s=e.response.data)==null?void 0:s.message)||"保存安全设置失败")):r.error("保存安全设置失败")}},F=s=>s?new Date(s).toLocaleString("zh-CN"):"-",ce=s=>({low:"success",medium:"warning",high:"danger",critical:"danger"})[s]||"info",pe=s=>({low:"低风险",medium:"中风险",high:"高风险",critical:"严重"})[s]||"未知",me=s=>s.blockedUntil?new Date(s.blockedUntil)>new Date:!0,C=s=>L.value.has(s),_e=async s=>{try{C(s)?await Q(s):await ne(s)}catch(e){console.error("切换IP封禁状态失败:",e)}};return(s,e)=>{const m=n("el-icon"),S=n("el-card"),g=n("el-col"),R=n("el-row"),M=n("el-input"),u=n("el-option"),q=n("el-select"),b=n("el-button"),c=n("el-table-column"),T=n("el-tag"),W=n("el-table"),X=n("el-pagination"),O=n("el-tab-pane"),h=n("el-input-number"),I=n("el-form-item"),Y=n("el-form"),fe=n("el-tabs"),Z=n("el-radio"),ge=n("el-radio-group"),be=n("el-dialog"),ee=ve("loading");return z(),le("div",xe,[e[36]||(e[36]=o("div",{class:"page-header"},[o("h1",null,"安全管理"),o("p",null,"管理登录安全、IP封禁和用户安全设置")],-1)),t(R,{gutter:20,class:"stats-cards"},{default:l(()=>[t(g,{span:6},{default:l(()=>[t(S,{class:"stat-card"},{default:l(()=>[o("div",Ae,[o("div",ze,_(f.totalAttempts),1),e[19]||(e[19]=o("div",{class:"stat-label"},"今日登录尝试",-1))]),t(m,{class:"stat-icon"},{default:l(()=>[t(D(ke))]),_:1})]),_:1})]),_:1}),t(g,{span:6},{default:l(()=>[t(S,{class:"stat-card"},{default:l(()=>[o("div",Ce,[o("div",Le,_(f.failedAttempts),1),e[20]||(e[20]=o("div",{class:"stat-label"},"失败尝试",-1))]),t(m,{class:"stat-icon"},{default:l(()=>[t(D(Ie))]),_:1})]),_:1})]),_:1}),t(g,{span:6},{default:l(()=>[t(S,{class:"stat-card"},{default:l(()=>[o("div",Ue,[o("div",Se,_(f.blockedIPs),1),e[21]||(e[21]=o("div",{class:"stat-label"},"封禁IP数量",-1))]),t(m,{class:"stat-icon"},{default:l(()=>[t(D(Pe))]),_:1})]),_:1})]),_:1}),t(g,{span:6},{default:l(()=>[t(S,{class:"stat-card"},{default:l(()=>[o("div",Be,[o("div",Me,_(f.riskLevel),1),e[22]||(e[22]=o("div",{class:"stat-label"},"当前风险等级",-1))]),t(m,{class:"stat-icon"},{default:l(()=>[t(D(Ve))]),_:1})]),_:1})]),_:1})]),_:1}),t(fe,{modelValue:J.value,"onUpdate:modelValue":e[12]||(e[12]=a=>J.value=a),class:"security-tabs"},{default:l(()=>[t(O,{label:"登录记录",name:"attempts"},{default:l(()=>[o("div",Te,[o("div",he,[t(R,{gutter:20},{default:l(()=>[t(g,{span:8},{default:l(()=>[t(M,{modelValue:y.search,"onUpdate:modelValue":e[0]||(e[0]=a=>y.search=a),placeholder:"搜索IP或用户名","prefix-icon":"Search",onInput:w},null,8,["modelValue"])]),_:1}),t(g,{span:6},{default:l(()=>[t(q,{modelValue:y.success,"onUpdate:modelValue":e[1]||(e[1]=a=>y.success=a),placeholder:"登录状态",onChange:w},{default:l(()=>[t(u,{label:"全部",value:""}),t(u,{label:"成功",value:!0}),t(u,{label:"失败",value:!1})]),_:1},8,["modelValue"])]),_:1}),t(g,{span:6},{default:l(()=>[t(q,{modelValue:y.riskLevel,"onUpdate:modelValue":e[2]||(e[2]=a=>y.riskLevel=a),placeholder:"风险等级",onChange:w},{default:l(()=>[t(u,{label:"全部",value:""}),t(u,{label:"低风险",value:"low"}),t(u,{label:"中风险",value:"medium"}),t(u,{label:"高风险",value:"high"}),t(u,{label:"严重风险",value:"critical"})]),_:1},8,["modelValue"])]),_:1}),t(g,{span:4},{default:l(()=>[t(b,{type:"primary",onClick:w},{default:l(()=>e[23]||(e[23]=[i("刷新")])),_:1,__:[23]})]),_:1})]),_:1})]),ae((z(),B(W,{data:N.value,stripe:""},{default:l(()=>[t(c,{prop:"createdAt",label:"时间",width:"180"},{default:l(({row:a})=>[i(_(F(a.createdAt)),1)]),_:1}),t(c,{label:"IP地址",width:"180"},{default:l(({row:a})=>[o("span",null,_(a.ipAddress),1),t(b,{type:C(a.ipAddress)?"success":"danger",size:"small",text:"",style:{"margin-left":"8px",padding:"2px 6px"},onClick:te=>_e(a.ipAddress)},{default:l(()=>[i(_(C(a.ipAddress)?"解":"封"),1)]),_:2},1032,["type","onClick"])]),_:1}),t(c,{prop:"username",label:"用户名",width:"120"}),t(c,{prop:"success",label:"状态",width:"80"},{default:l(({row:a})=>[t(T,{type:a.success?"success":"danger"},{default:l(()=>[i(_(a.success?"成功":"失败"),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"riskLevel",label:"风险等级",width:"100"},{default:l(({row:a})=>[t(T,{type:ce(a.riskLevel)},{default:l(()=>[i(_(pe(a.riskLevel)),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"failureReason",label:"失败原因","min-width":"150"}),t(c,{prop:"userAgent",label:"用户代理","min-width":"200","show-overflow-tooltip":""})]),_:1},8,["data"])),[[ee,$.value]]),o("div",De,[t(X,{"current-page":v.page,"onUpdate:currentPage":e[3]||(e[3]=a=>v.page=a),"page-size":v.size,"onUpdate:pageSize":e[4]||(e[4]=a=>v.size=a),total:v.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:w,onCurrentChange:w},null,8,["current-page","page-size","total"])])])]),_:1}),t(O,{label:"IP黑名单",name:"blacklist"},{default:l(()=>[o("div",$e,[o("div",Ee,[t(R,{gutter:20},{default:l(()=>[t(g,{span:8},{default:l(()=>[t(M,{modelValue:j.search,"onUpdate:modelValue":e[5]||(e[5]=a=>j.search=a),placeholder:"搜索IP地址","prefix-icon":"Search",onInput:k},null,8,["modelValue"])]),_:1}),t(g,{span:8},{default:l(()=>[t(b,{type:"primary",onClick:re},{default:l(()=>e[24]||(e[24]=[i("添加IP封禁")])),_:1,__:[24]})]),_:1}),t(g,{span:8,style:{"text-align":"right"}},{default:l(()=>[t(b,{onClick:k},{default:l(()=>e[25]||(e[25]=[i("刷新")])),_:1,__:[25]})]),_:1})]),_:1})]),ae((z(),B(W,{data:G.value,stripe:""},{default:l(()=>[t(c,{prop:"ipAddress",label:"IP地址",width:"140"}),t(c,{prop:"reason",label:"封禁原因","min-width":"200"}),t(c,{prop:"blockedUntil",label:"封禁到期",width:"180"},{default:l(({row:a})=>[a.blockedUntil?(z(),le("span",Ne,_(F(a.blockedUntil)),1)):(z(),B(T,{key:1,type:"danger"},{default:l(()=>e[26]||(e[26]=[i("永久封禁")])),_:1,__:[26]}))]),_:1}),t(c,{prop:"createdAt",label:"创建时间",width:"180"},{default:l(({row:a})=>[i(_(F(a.createdAt)),1)]),_:1}),t(c,{label:"状态",width:"100"},{default:l(({row:a})=>[t(T,{type:C(a)?"danger":"success"},{default:l(()=>[i(_(C(a)?"已封禁":"已过期"),1)]),_:2},1032,["type"])]),_:1}),t(c,{label:"操作",width:"120"},{default:l(({row:a})=>[C(a)?(z(),B(b,{key:0,type:"success",size:"small",onClick:te=>Q(a.ipAddress)},{default:l(()=>e[27]||(e[27]=[i(" 解封 ")])),_:2,__:[27]},1032,["onClick"])):se("",!0),t(b,{type:"danger",size:"small",onClick:te=>de(a.id)},{default:l(()=>e[28]||(e[28]=[i(" 删除 ")])),_:2,__:[28]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ee,E.value]]),o("div",je,[t(X,{"current-page":V.page,"onUpdate:currentPage":e[6]||(e[6]=a=>V.page=a),"page-size":V.size,"onUpdate:pageSize":e[7]||(e[7]=a=>V.size=a),total:V.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:k},null,8,["current-page","page-size","total"])])])]),_:1}),t(O,{label:"安全设置",name:"settings"},{default:l(()=>[o("div",Fe,[t(S,null,{header:l(()=>e[29]||(e[29]=[o("span",null,"登录安全配置",-1)])),default:l(()=>[t(Y,{model:p,"label-width":"150px"},{default:l(()=>[t(I,{label:"IP最大尝试次数"},{default:l(()=>[t(h,{modelValue:p.ipMaxAttempts,"onUpdate:modelValue":e[8]||(e[8]=a=>p.ipMaxAttempts=a),min:1,max:50},null,8,["modelValue"])]),_:1}),t(I,{label:"IP锁定时间(分钟)"},{default:l(()=>[t(h,{modelValue:p.ipLockMinutes,"onUpdate:modelValue":e[9]||(e[9]=a=>p.ipLockMinutes=a),min:1,max:1440},null,8,["modelValue"])]),_:1}),t(I,{label:"用户最大尝试次数"},{default:l(()=>[t(h,{modelValue:p.userMaxAttempts,"onUpdate:modelValue":e[10]||(e[10]=a=>p.userMaxAttempts=a),min:1,max:20},null,8,["modelValue"])]),_:1}),t(I,{label:"用户锁定时间(分钟)"},{default:l(()=>[t(h,{modelValue:p.userLockMinutes,"onUpdate:modelValue":e[11]||(e[11]=a=>p.userLockMinutes=a),min:1,max:1440},null,8,["modelValue"])]),_:1}),t(I,null,{default:l(()=>[t(b,{type:"primary",onClick:ue},{default:l(()=>e[30]||(e[30]=[i("保存设置")])),_:1,__:[30]}),t(b,{onClick:K},{default:l(()=>e[31]||(e[31]=[i("重置")])),_:1,__:[31]})]),_:1})]),_:1},8,["model"])]),_:1})])]),_:1})]),_:1},8,["modelValue"]),t(be,{modelValue:U.value,"onUpdate:modelValue":e[18]||(e[18]=a=>U.value=a),title:"添加IP封禁",width:"500px"},{footer:l(()=>[t(b,{onClick:e[17]||(e[17]=a=>U.value=!1)},{default:l(()=>e[34]||(e[34]=[i("取消")])),_:1,__:[34]}),t(b,{type:"primary",onClick:ie},{default:l(()=>e[35]||(e[35]=[i("确认封禁")])),_:1,__:[35]})]),default:l(()=>[t(Y,{model:d,"label-width":"100px"},{default:l(()=>[t(I,{label:"IP地址",required:""},{default:l(()=>[t(M,{modelValue:d.ipAddress,"onUpdate:modelValue":e[13]||(e[13]=a=>d.ipAddress=a),placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),t(I,{label:"封禁原因",required:""},{default:l(()=>[t(M,{modelValue:d.reason,"onUpdate:modelValue":e[14]||(e[14]=a=>d.reason=a),type:"textarea",placeholder:"请输入封禁原因"},null,8,["modelValue"])]),_:1}),t(I,{label:"封禁类型"},{default:l(()=>[t(ge,{modelValue:d.blockType,"onUpdate:modelValue":e[15]||(e[15]=a=>d.blockType=a)},{default:l(()=>[t(Z,{label:"temporary"},{default:l(()=>e[32]||(e[32]=[i("临时封禁")])),_:1,__:[32]}),t(Z,{label:"permanent"},{default:l(()=>e[33]||(e[33]=[i("永久封禁")])),_:1,__:[33]})]),_:1},8,["modelValue"])]),_:1}),d.blockType==="temporary"?(z(),B(I,{key:0,label:"封禁时长"},{default:l(()=>[t(q,{modelValue:d.duration,"onUpdate:modelValue":e[16]||(e[16]=a=>d.duration=a),placeholder:"选择封禁时长"},{default:l(()=>[t(u,{label:"1小时",value:60}),t(u,{label:"6小时",value:360}),t(u,{label:"24小时",value:1440}),t(u,{label:"7天",value:10080}),t(u,{label:"30天",value:43200})]),_:1},8,["modelValue"])]),_:1})):se("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Je=we(Re,[["__scopeId","data-v-0a6f2fac"]]);export{Je as default};
