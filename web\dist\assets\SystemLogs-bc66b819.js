import{d as E,r as k,a as j,p as F,b as u,M as I,o as z,c as q,e as m,f as e,w as n,i as _,s as G,q as J,y as h,g as K,R as O,E as S}from"./index-fea52ff4.js";import{_ as Q}from"./_plugin-vue_export-helper-c27b6911.js";function W(w){const r=new Date(w),l=new Date().getTime()-r.getTime();return l<60*1e3?"刚刚":l<60*60*1e3?`${Math.floor(l/6e4)}分钟前`:l<24*60*60*1e3?`${Math.floor(l/36e5)}小时前`:l<7*24*60*60*1e3?`${Math.floor(l/864e5)}天前`:X(r)}function X(w){const r=new Date(w),b=r.getFullYear(),l=String(r.getMonth()+1).padStart(2,"0"),d=String(r.getDate()).padStart(2,"0"),c=String(r.getHours()).padStart(2,"0"),g=String(r.getMinutes()).padStart(2,"0"),V=String(r.getSeconds()).padStart(2,"0");return`${b}-${l}-${d} ${c}:${g}:${V}`}const Z={class:"system-logs"},ee={class:"filter-section"},le={class:"logs-section"},te={class:"card-header"},ae={class:"pagination-wrapper"},oe=E({__name:"SystemLogs",setup(w){const r=k(!1),b=k([]),l=j({username:"",module:"",action:"",level:"",search:""}),d=k([]),c=j({page:1,limit:20,total:0,totalPages:0}),g=async()=>{var s,t,y,p,a;try{r.value=!0;const i={page:c.page,limit:c.limit};l.username&&(i.username=l.username),l.module&&(i.module=l.module),l.action&&(i.action=l.action),l.level&&(i.level=l.level),l.search&&(i.search=l.search),d.value&&d.value.length===2&&(i.startDate=d.value[0],i.endDate=d.value[1]);const v=await O(i);v.success?(b.value=((s=v.data)==null?void 0:s.logs)||[],c.total=((y=(t=v.data)==null?void 0:t.pagination)==null?void 0:y.total)||0,c.totalPages=((a=(p=v.data)==null?void 0:p.pagination)==null?void 0:a.totalPages)||0):S.error(v.message||"获取日志失败")}catch(i){console.error("Load logs error:",i),S.error("获取日志失败")}finally{r.value=!1}},V=()=>{l.username="",l.module="",l.action="",l.level="",l.search="",d.value=[],c.page=1,g()},C=()=>{S.info("导出功能开发中...")},L=s=>({auth:"认证",user:"用户",project:"项目",location:"地点",checkin:"打卡",system:"系统",security:"安全"})[s]||s,T=s=>({auth:"primary",user:"success",project:"warning",location:"info",checkin:"primary",system:"info",security:"danger"})[s]||"info",Y=s=>({login:"登录",logout:"登出",create_user:"创建用户",update_user:"更新用户",delete_user:"删除用户",reset_password:"重置密码",create_project:"创建项目",update_project:"更新项目",delete_project:"删除项目",create_location:"创建地点",update_location:"更新地点",delete_location:"删除地点",checkin:"打卡",submit_checkin_application:"提交补卡申请",review_checkin_application:"审核补卡申请"})[s]||s,$=s=>({info:"信息",warning:"警告",error:"错误",critical:"严重"})[s]||s,U=s=>({info:"info",warning:"warning",error:"danger",critical:"danger"})[s]||"info";return F(()=>{g()}),(s,t)=>{const y=u("el-input"),p=u("el-form-item"),a=u("el-option"),i=u("el-select"),v=u("el-date-picker"),M=u("el-button"),N=u("el-form"),D=u("el-card"),H=u("Refresh"),P=u("el-icon"),f=u("el-table-column"),x=u("el-tag"),B=u("el-table"),R=u("el-pagination"),A=I("loading");return z(),q("div",Z,[t[13]||(t[13]=m("div",{class:"page-header"},[m("h1",null,"系统日志"),m("p",null,"查看和管理系统操作日志记录")],-1)),m("div",ee,[e(D,null,{default:n(()=>[e(N,{model:l,inline:""},{default:n(()=>[e(p,{label:"用户"},{default:n(()=>[e(y,{modelValue:l.username,"onUpdate:modelValue":t[0]||(t[0]=o=>l.username=o),placeholder:"输入用户名",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(p,{label:"模块"},{default:n(()=>[e(i,{modelValue:l.module,"onUpdate:modelValue":t[1]||(t[1]=o=>l.module=o),placeholder:"选择模块",clearable:"",style:{width:"120px"}},{default:n(()=>[e(a,{label:"认证",value:"auth"}),e(a,{label:"用户",value:"user"}),e(a,{label:"项目",value:"project"}),e(a,{label:"地点",value:"location"}),e(a,{label:"打卡",value:"checkin"}),e(a,{label:"系统",value:"system"}),e(a,{label:"安全",value:"security"})]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"操作"},{default:n(()=>[e(i,{modelValue:l.action,"onUpdate:modelValue":t[2]||(t[2]=o=>l.action=o),placeholder:"选择操作",clearable:"",style:{width:"150px"}},{default:n(()=>[e(a,{label:"登录",value:"login"}),e(a,{label:"登出",value:"logout"}),e(a,{label:"创建用户",value:"create_user"}),e(a,{label:"更新用户",value:"update_user"}),e(a,{label:"删除用户",value:"delete_user"}),e(a,{label:"创建项目",value:"create_project"}),e(a,{label:"更新项目",value:"update_project"}),e(a,{label:"删除项目",value:"delete_project"}),e(a,{label:"创建地点",value:"create_location"}),e(a,{label:"更新地点",value:"update_location"}),e(a,{label:"删除地点",value:"delete_location"}),e(a,{label:"打卡",value:"checkin"}),e(a,{label:"提交补卡申请",value:"submit_checkin_application"}),e(a,{label:"审核补卡申请",value:"review_checkin_application"})]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"级别"},{default:n(()=>[e(i,{modelValue:l.level,"onUpdate:modelValue":t[3]||(t[3]=o=>l.level=o),placeholder:"选择级别",clearable:"",style:{width:"120px"}},{default:n(()=>[e(a,{label:"信息",value:"info"}),e(a,{label:"警告",value:"warning"}),e(a,{label:"错误",value:"error"}),e(a,{label:"严重",value:"critical"})]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"时间范围"},{default:n(()=>[e(v,{modelValue:d.value,"onUpdate:modelValue":t[4]||(t[4]=o=>d.value=o),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"}},null,8,["modelValue"])]),_:1}),e(p,{label:"搜索"},{default:n(()=>[e(y,{modelValue:l.search,"onUpdate:modelValue":t[5]||(t[5]=o=>l.search=o),placeholder:"搜索描述内容",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(p,null,{default:n(()=>[e(M,{type:"primary",onClick:g,loading:r.value},{default:n(()=>t[8]||(t[8]=[_(" 查询 ")])),_:1,__:[8]},8,["loading"]),e(M,{onClick:V},{default:n(()=>t[9]||(t[9]=[_(" 重置 ")])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])]),_:1})]),m("div",le,[e(D,null,{header:n(()=>[m("div",te,[t[12]||(t[12]=m("span",null,"日志记录",-1)),m("div",null,[e(M,{size:"small",onClick:g},{default:n(()=>[e(P,null,{default:n(()=>[e(H)]),_:1}),t[10]||(t[10]=_(" 刷新 "))]),_:1,__:[10]}),e(M,{size:"small",type:"primary",onClick:C},{default:n(()=>t[11]||(t[11]=[_(" 导出 ")])),_:1,__:[11]})])])]),default:n(()=>[G((z(),J(B,{data:b.value,stripe:"",style:{width:"100%"}},{default:n(()=>[e(f,{prop:"id",label:"ID",width:"80"}),e(f,{label:"用户",width:"120"},{default:n(({row:o})=>[_(h(o.username||"系统"),1)]),_:1}),e(f,{label:"模块",width:"100"},{default:n(({row:o})=>[e(x,{type:T(o.module),size:"small"},{default:n(()=>[_(h(L(o.module)),1)]),_:2},1032,["type"])]),_:1}),e(f,{label:"操作",width:"150"},{default:n(({row:o})=>[_(h(Y(o.action)),1)]),_:1}),e(f,{prop:"description",label:"描述","min-width":"200","show-overflow-tooltip":""}),e(f,{label:"级别",width:"80"},{default:n(({row:o})=>[e(x,{type:U(o.level),size:"small"},{default:n(()=>[_(h($(o.level)),1)]),_:2},1032,["type"])]),_:1}),e(f,{prop:"ipAddress",label:"IP地址",width:"120"}),e(f,{label:"时间",width:"180"},{default:n(({row:o})=>[_(h(K(W)(o.createdAt)),1)]),_:1})]),_:1},8,["data"])),[[A,r.value]]),m("div",ae,[e(R,{"current-page":c.page,"onUpdate:currentPage":t[6]||(t[6]=o=>c.page=o),"page-size":c.limit,"onUpdate:pageSize":t[7]||(t[7]=o=>c.limit=o),"page-sizes":[20,50,100,200],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:g,onCurrentChange:g},null,8,["current-page","page-size","total"])])]),_:1})])])}}});const re=Q(oe,[["__scopeId","data-v-d6efe1ab"]]);export{re as default};
