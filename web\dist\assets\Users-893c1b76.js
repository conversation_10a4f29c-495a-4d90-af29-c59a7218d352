import{d as Y,r as w,a as h,p as Z,b as i,M as ee,o as N,c as ae,e as k,f as a,w as o,i as u,g as le,h as te,s as oe,q as D,y as V,x as se,a1 as re,E as c,z,a5 as ne,a6 as j,a7 as de,a8 as pe,T as ie}from"./index-fea52ff4.js";import{_ as ue}from"./_plugin-vue_export-helper-c27b6911.js";const me={class:"users-page"},ce={class:"page-header"},ge={class:"pagination"},fe=Y({__name:"Users",setup(_e){const C=w(!1),B=w([]),v=w(!1),g=w(null),p=h({search:"",role:"",department:""}),m=h({page:1,size:20,total:0}),r=h({username:"",password:"",realName:"",phone:"",email:"",department:"",role:"employee"}),E={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}]},R=l=>({admin:"danger",manager:"warning",employee:"primary"})[l]||"primary",q=l=>({admin:"管理员",manager:"经理",employee:"员工"})[l]||"未知",O=l=>new Date(l).toLocaleString("zh-CN"),U=()=>{m.page=1,y()},F=()=>{p.search="",p.role="",p.department="",U()},M=l=>{m.size=l,y()},P=l=>{m.page=l,y()},y=async()=>{var l,e,s;C.value=!0;try{const d={page:m.page,limit:m.size,search:p.search,role:p.role,active:p.active},n=await re(d);n.success?(B.value=((l=n.data)==null?void 0:l.users)||[],m.total=((s=(e=n.data)==null?void 0:e.pagination)==null?void 0:s.total)||0):c.error(n.message||"加载用户列表失败")}catch(d){console.error("Load users error:",d),c.error("加载用户列表失败")}finally{C.value=!1}},A=()=>{Object.assign(r,{username:"",password:"",realName:"",phone:"",email:"",department:"",role:"employee"}),g.value=null,v.value=!0},K=l=>{g.value=l,Object.assign(r,{username:l.username||"",realName:l.realName||"",phone:l.phone||"",email:l.email||"",department:l.department||"",role:l.role||"employee",password:""}),v.value=!0},L=async l=>{try{const{value:e}=await z.prompt(`请输入用户"${l.realName}"的新密码`,"重置密码",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"password"});if(e){const s=await ne(l.id,{password:e});s.success?c.success("密码重置成功"):c.error(s.message||"密码重置失败")}}catch{}},G=async l=>{const e=l.active?"禁用":"启用";try{await z.confirm(`确定要${e}用户"${l.realName}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=await j(l.id,{active:!l.active});s.success?(l.active=!l.active,c.success(`${e}成功`)):c.error(s.message||`${e}失败`)}catch{}},I=async l=>{try{await z.confirm(`确定要删除用户"${l.realName}"吗？此操作不可恢复！`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"});const e=await de(l.id);e.success?(c.success("删除成功"),y()):c.error(e.message||"删除失败")}catch{}},H=async()=>{try{const l=!!g.value,e=Object.fromEntries(Object.entries(r).filter(([_,b])=>["username","realName","role"].includes(_)?!0:b!=null&&b!==""));console.log("提交的用户数据:",e);const s=l?j:pe,d=l?[g.value.id,e]:[e],n=await s(...d);n.success?(c.success(l?"更新成功":"创建成功"),v.value=!1,y(),Object.assign(r,{username:"",realName:"",phone:"",email:"",department:"",role:"employee",password:""}),g.value=null):c.error(n.message||(l?"更新失败":"创建失败"))}catch(l){console.error("Submit user error:",l),c.error(g.value?"更新失败":"创建失败")}};return Z(()=>{y()}),(l,e)=>{const s=i("el-button"),d=i("el-input"),n=i("el-form-item"),_=i("el-option"),b=i("el-select"),T=i("el-form"),$=i("el-card"),f=i("el-table-column"),S=i("el-tag"),J=i("el-table"),Q=i("el-pagination"),W=i("el-dialog"),X=ee("loading");return N(),ae("div",me,[k("div",ce,[e[15]||(e[15]=k("h2",null,"用户管理",-1)),a(s,{type:"primary",icon:le(ie),onClick:A},{default:o(()=>e[14]||(e[14]=[u(" 添加用户 ")])),_:1,__:[14]},8,["icon"])]),a($,{class:"search-card"},{default:o(()=>[a(T,{model:p,inline:""},{default:o(()=>[a(n,{label:"用户名/姓名"},{default:o(()=>[a(d,{modelValue:p.search,"onUpdate:modelValue":e[0]||(e[0]=t=>p.search=t),placeholder:"请输入用户名或姓名",clearable:"",onKeyup:te(U,["enter"])},null,8,["modelValue"])]),_:1}),a(n,{label:"角色"},{default:o(()=>[a(b,{modelValue:p.role,"onUpdate:modelValue":e[1]||(e[1]=t=>p.role=t),placeholder:"请选择角色",clearable:""},{default:o(()=>[a(_,{label:"管理员",value:"admin"}),a(_,{label:"经理",value:"manager"}),a(_,{label:"员工",value:"employee"})]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"部门"},{default:o(()=>[a(d,{modelValue:p.department,"onUpdate:modelValue":e[2]||(e[2]=t=>p.department=t),placeholder:"请输入部门",clearable:""},null,8,["modelValue"])]),_:1}),a(n,null,{default:o(()=>[a(s,{type:"primary",onClick:U},{default:o(()=>e[16]||(e[16]=[u("搜索")])),_:1,__:[16]}),a(s,{onClick:F},{default:o(()=>e[17]||(e[17]=[u("重置")])),_:1,__:[17]})]),_:1})]),_:1},8,["model"])]),_:1}),a($,null,{default:o(()=>[oe((N(),D(J,{data:B.value,style:{width:"100%"}},{default:o(()=>[a(f,{prop:"username",label:"用户名",width:"120"}),a(f,{prop:"realName",label:"真实姓名",width:"120"}),a(f,{prop:"phone",label:"手机号",width:"130"}),a(f,{prop:"email",label:"邮箱","min-width":"180"}),a(f,{prop:"department",label:"部门",width:"120"}),a(f,{prop:"role",label:"角色",width:"100"},{default:o(({row:t})=>[a(S,{type:R(t.role)},{default:o(()=>[u(V(q(t.role)),1)]),_:2},1032,["type"])]),_:1}),a(f,{prop:"active",label:"状态",width:"80"},{default:o(({row:t})=>[a(S,{type:t.active?"success":"danger"},{default:o(()=>[u(V(t.active?"激活":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(f,{prop:"createdAt",label:"创建时间",width:"160"},{default:o(({row:t})=>[u(V(O(t.createdAt)),1)]),_:1}),a(f,{label:"操作",width:"280",fixed:"right"},{default:o(({row:t})=>[a(s,{text:"",type:"primary",onClick:x=>K(t)},{default:o(()=>e[18]||(e[18]=[u(" 编辑 ")])),_:2,__:[18]},1032,["onClick"]),a(s,{text:"",type:"warning",onClick:x=>L(t)},{default:o(()=>e[19]||(e[19]=[u(" 重置密码 ")])),_:2,__:[19]},1032,["onClick"]),a(s,{text:"",type:t.active?"danger":"success",onClick:x=>G(t)},{default:o(()=>[u(V(t.active?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),a(s,{text:"",type:"danger",onClick:x=>I(t)},{default:o(()=>e[20]||(e[20]=[u(" 删除 ")])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,C.value]]),k("div",ge,[a(Q,{"current-page":m.page,"onUpdate:currentPage":e[3]||(e[3]=t=>m.page=t),"page-size":m.size,"onUpdate:pageSize":e[4]||(e[4]=t=>m.size=t),"page-sizes":[10,20,50,100],total:m.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:M,onCurrentChange:P},null,8,["current-page","page-size","total"])])]),_:1}),a(W,{modelValue:v.value,"onUpdate:modelValue":e[13]||(e[13]=t=>v.value=t),title:g.value?"编辑用户":"添加用户",width:"500px"},{footer:o(()=>[a(s,{onClick:e[12]||(e[12]=t=>v.value=!1)},{default:o(()=>e[21]||(e[21]=[u("取消")])),_:1,__:[21]}),a(s,{type:"primary",onClick:H},{default:o(()=>e[22]||(e[22]=[u("确定")])),_:1,__:[22]})]),default:o(()=>[a(T,{ref:"userFormRef",model:r,rules:E,"label-width":"80px"},{default:o(()=>[a(n,{label:"用户名",prop:"username"},{default:o(()=>[a(d,{modelValue:r.username,"onUpdate:modelValue":e[5]||(e[5]=t=>r.username=t),placeholder:"请输入用户名",disabled:!!g.value},null,8,["modelValue","disabled"])]),_:1}),g.value?se("",!0):(N(),D(n,{key:0,label:"密码",prop:"password"},{default:o(()=>[a(d,{modelValue:r.password,"onUpdate:modelValue":e[6]||(e[6]=t=>r.password=t),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1})),a(n,{label:"真实姓名",prop:"realName"},{default:o(()=>[a(d,{modelValue:r.realName,"onUpdate:modelValue":e[7]||(e[7]=t=>r.realName=t),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),a(n,{label:"手机号"},{default:o(()=>[a(d,{modelValue:r.phone,"onUpdate:modelValue":e[8]||(e[8]=t=>r.phone=t),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),a(n,{label:"邮箱"},{default:o(()=>[a(d,{modelValue:r.email,"onUpdate:modelValue":e[9]||(e[9]=t=>r.email=t),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),a(n,{label:"部门"},{default:o(()=>[a(d,{modelValue:r.department,"onUpdate:modelValue":e[10]||(e[10]=t=>r.department=t),placeholder:"请输入部门"},null,8,["modelValue"])]),_:1}),a(n,{label:"角色",prop:"role"},{default:o(()=>[a(b,{modelValue:r.role,"onUpdate:modelValue":e[11]||(e[11]=t=>r.role=t),placeholder:"请选择角色"},{default:o(()=>[a(_,{label:"员工",value:"employee"}),a(_,{label:"经理",value:"manager"}),a(_,{label:"管理员",value:"admin"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});const be=ue(fe,[["__scopeId","data-v-f1ee65b3"]]);export{be as default};
