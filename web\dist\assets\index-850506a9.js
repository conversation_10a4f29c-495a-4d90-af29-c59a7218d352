import{d as em,ab as Wo,ay as La,az as rm,K as hi,aA as im,ac as Ia,aB as td,p as nm,aC as am,aD as om,aE as sm,g as lm,aF as um}from"./index-4256ff3d.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var ol=function(r,t){return ol=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])},ol(r,t)};function O(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");ol(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var fm=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),hm=function(){function r(){this.browser=new fm,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),Ze=new hm;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(Ze.wxa=!0,Ze.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?Ze.worker=!0:!Ze.hasGlobalWindow||"Deno"in window?(Ze.node=!0,Ze.svgSupported=!0):vm(navigator.userAgent,Ze);function vm(r,t){var e=t.browser,i=r.match(/Firefox\/([\d.]+)/),n=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(r);i&&(e.firefox=!0,e.version=i[1]),n&&(e.ie=!0,e.version=n[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),o&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in s||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}const Y=Ze;var hu=12,cm="sans-serif",Fr=hu+"px "+cm,dm=20,pm=100,gm="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function ym(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var i=String.fromCharCode(e+32),n=(r.charCodeAt(e)-dm)/pm;t[i]=n}return t}var mm=ym(gm),Li={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,i){if(!r){var n=Li.createCanvas();r=n&&n.getContext("2d")}if(r)return t!==i&&(t=r.font=i||Fr),r.measureText(e);e=e||"",i=i||Fr;var a=/((?:\d+)?\.?\d*)px/.exec(i),o=a&&+a[1]||hu,s=0;if(i.indexOf("mono")>=0)s=o*e.length;else for(var l=0;l<e.length;l++){var u=mm[e[l]];s+=u==null?o:u*o}return{width:s}}}(),loadImage:function(r,t,e){var i=new Image;return i.onload=t,i.onerror=e,i.src=r,i}},ed=ir(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),rd=ir(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),zn=Object.prototype.toString,go=Array.prototype,_m=go.forEach,Sm=go.filter,vu=go.slice,wm=go.map,Tf=(function(){}).constructor,Yn=Tf?Tf.prototype:null,cu="__proto__",bm=2311;function id(){return bm++}function du(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function J(r){if(r==null||typeof r!="object")return r;var t=r,e=zn.call(r);if(e==="[object Array]"){if(!fn(r)){t=[];for(var i=0,n=r.length;i<n;i++)t[i]=J(r[i])}}else if(rd[e]){if(!fn(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var i=0,n=r.length;i<n;i++)t[i]=r[i]}}}else if(!ed[e]&&!fn(r)&&!xn(r)){t={};for(var o in r)r.hasOwnProperty(o)&&o!==cu&&(t[o]=J(r[o]))}return t}function tt(r,t,e){if(!H(t)||!H(r))return e?J(t):r;for(var i in t)if(t.hasOwnProperty(i)&&i!==cu){var n=r[i],a=t[i];H(a)&&H(n)&&!N(a)&&!N(n)&&!xn(a)&&!xn(n)&&!Cf(a)&&!Cf(n)&&!fn(a)&&!fn(n)?tt(n,a,e):(e||!(i in r))&&(r[i]=J(t[i]))}return r}function k(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==cu&&(r[e]=t[e]);return r}function it(r,t,e){for(var i=ht(t),n=0,a=i.length;n<a;n++){var o=i[n];(e?t[o]!=null:r[o]==null)&&(r[o]=t[o])}return r}function at(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,i=r.length;e<i;e++)if(r[e]===t)return e}return-1}function xm(r,t){var e=r.prototype;function i(){}i.prototype=t.prototype,r.prototype=new i;for(var n in e)e.hasOwnProperty(n)&&(r.prototype[n]=e[n]);r.prototype.constructor=r,r.superClass=t}function xe(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&(e?t[a]!=null:r[a]==null)&&(r[a]=t[a])}else it(r,t,e)}function Gt(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function A(r,t,e){if(r&&t)if(r.forEach&&r.forEach===_m)r.forEach(t,e);else if(r.length===+r.length)for(var i=0,n=r.length;i<n;i++)t.call(e,r[i],i,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function V(r,t,e){if(!r)return[];if(!t)return pu(r);if(r.map&&r.map===wm)return r.map(t,e);for(var i=[],n=0,a=r.length;n<a;n++)i.push(t.call(e,r[n],n,r));return i}function ir(r,t,e,i){if(r&&t){for(var n=0,a=r.length;n<a;n++)e=t.call(i,e,r[n],n,r);return e}}function wt(r,t,e){if(!r)return[];if(!t)return pu(r);if(r.filter&&r.filter===Sm)return r.filter(t,e);for(var i=[],n=0,a=r.length;n<a;n++)t.call(e,r[n],n,r)&&i.push(r[n]);return i}function ht(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function Tm(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return function(){return r.apply(t,e.concat(vu.call(arguments)))}}var ft=Yn&&$(Yn.bind)?Yn.call.bind(Yn.bind):Tm;function mt(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(vu.call(arguments)))}}function N(r){return Array.isArray?Array.isArray(r):zn.call(r)==="[object Array]"}function $(r){return typeof r=="function"}function z(r){return typeof r=="string"}function sl(r){return zn.call(r)==="[object String]"}function vt(r){return typeof r=="number"}function H(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function Cf(r){return!!ed[zn.call(r)]}function Vt(r){return!!rd[zn.call(r)]}function xn(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function yo(r){return r.colorStops!=null}function Cm(r){return r.image!=null}function Xa(r){return r!==r}function Tn(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,i=r.length;e<i;e++)if(r[e]!=null)return r[e]}function Z(r,t){return r??t}function Pa(r,t,e){return r??t??e}function pu(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return vu.apply(r,t)}function nd(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function ke(r,t){if(!r)throw new Error(t)}function _e(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var ad="__ec_primitive__";function ll(r){r[ad]=!0}function fn(r){return r[ad]}var Dm=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return ht(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)},r}(),od=typeof Map=="function";function Mm(){return od?new Map:new Dm}var Am=function(){function r(t){var e=N(t);this.data=Mm();var i=this;t instanceof r?t.each(n):t&&A(t,n);function n(a,o){e?i.set(a,o):i.set(o,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(i,n){t.call(e,i,n)})},r.prototype.keys=function(){var t=this.data.keys();return od?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function X(r){return new Am(r)}function Lm(r,t){for(var e=new r.constructor(r.length+t.length),i=0;i<r.length;i++)e[i]=r[i];for(var n=r.length,i=0;i<t.length;i++)e[i+n]=t[i];return e}function mo(r,t){var e;if(Object.create)e=Object.create(r);else{var i=function(){};i.prototype=r,e=new i}return t&&k(e,t),e}function sd(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function zr(r,t){return r.hasOwnProperty(t)}function Ht(){}var Im=180/Math.PI;function Ii(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function Pm(r){return[r[0],r[1]]}function Df(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function Rm(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function Em(r){return Math.sqrt(km(r))}function km(r){return r[0]*r[0]+r[1]*r[1]}function Uo(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function Om(r,t){var e=Em(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function ul(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var Bm=ul;function Nm(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var mi=Nm;function PA(r,t,e,i){return r[0]=t[0]+i*(e[0]-t[0]),r[1]=t[1]+i*(e[1]-t[1]),r}function ie(r,t,e){var i=t[0],n=t[1];return r[0]=e[0]*i+e[2]*n+e[4],r[1]=e[1]*i+e[3]*n+e[5],r}function di(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function pi(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}var Kr=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),Fm=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Kr(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(a,o,t),this.handler.dispatchToElement(new Kr(e,t),"drag",t.event);var s=this.handler.findHover(i,n,e).target,l=this._dropTarget;this._dropTarget=s,e!==s&&(l&&s!==l&&this.handler.dispatchToElement(new Kr(l,t),"dragleave",t.event),s&&s!==l&&this.handler.dispatchToElement(new Kr(s,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Kr(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Kr(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}();const zm=Fm;var Hm=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(n=i,i=e,e=null),!i||!t)return this;var o=this._$eventProcessor;e!=null&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===i)return this;var l={h:i,query:e,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},u=a[t].length-1,f=a[t][u];return f&&f.callAtLast?a[t].splice(u,0,l):a[t].push(l),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],a=0,o=i[t].length;a<o;a++)i[t][a].h!==e&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},r.prototype.trigger=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=n.length,l=0;l<s;l++){var u=n[l];if(!(a&&a.filter&&u.query!=null&&!a.filter(t,u.query)))switch(o){case 0:u.h.call(u.ctx);break;case 1:u.h.call(u.ctx,e[0]);break;case 2:u.h.call(u.ctx,e[0],e[1]);break;default:u.h.apply(u.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=e[o-1],l=n.length,u=0;u<l;u++){var f=n[u];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,e[0]);break;case 2:f.h.call(s,e[0],e[1]);break;default:f.h.apply(s,e.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}();const Te=Hm;var Gm=Math.log(2);function fl(r,t,e,i,n,a){var o=i+"-"+n,s=r.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var l=Math.round(Math.log((1<<s)-1&~n)/Gm);return r[e][l]}for(var u=i|1<<e,f=e+1;i&1<<f;)f++;for(var h=0,c=0,v=0;c<s;c++){var d=1<<c;d&n||(h+=(v%2?-1:1)*r[e][c]*fl(r,t-1,f,u,n|d,a),v++)}return a[o]=h,h}function Mf(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],i={},n=fl(e,8,0,0,0,i);if(n!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*fl(e,7,o===0?1:0,1<<o,1<<s,i)/n*t[o];return function(l,u,f){var h=u*a[6]+f*a[7]+1;l[0]=(u*a[0]+f*a[1]+a[2])/h,l[1]=(u*a[3]+f*a[4]+a[5])/h}}}var Af="___zrEVENTSAVED",$o=[];function Vm(r,t,e,i,n){return hl($o,t,i,n,!0)&&hl(r,e,$o[0],$o[1])}function hl(r,t,e,i,n){if(t.getBoundingClientRect&&Y.domSupported&&!ld(t)){var a=t[Af]||(t[Af]={}),o=Wm(t,a),s=Um(o,a,n);if(s)return s(r,e,i),!0}return!1}function Wm(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,l=a%2,u=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",n[u]+":0",i[1-l]+":auto",n[1-u]+":auto",""].join("!important;"),r.appendChild(o),e.push(o)}return e}function Um(r,t,e){for(var i=e?"invTrans":"trans",n=t[i],a=t.srcCoords,o=[],s=[],l=!0,u=0;u<4;u++){var f=r[u].getBoundingClientRect(),h=2*u,c=f.left,v=f.top;o.push(c,v),l=l&&a&&c===a[h]&&v===a[h+1],s.push(r[u].offsetLeft,r[u].offsetTop)}return l&&n?n:(t.srcCoords=o,t[i]=e?Mf(s,o):Mf(o,s))}function ld(r){return r.nodeName.toUpperCase()==="CANVAS"}var $m=/([&<>"'])/g,Ym={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Nt(r){return r==null?"":(r+"").replace($m,function(t,e){return Ym[e]})}var Xm=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Yo=[],Zm=Y.browser.firefox&&+Y.browser.version.split(".")[0]<39;function vl(r,t,e,i){return e=e||{},i?Lf(r,t,e):Zm&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):Lf(r,t,e),e}function Lf(r,t,e){if(Y.domSupported&&r.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(ld(r)){var a=r.getBoundingClientRect();e.zrX=i-a.left,e.zrY=n-a.top;return}else if(hl(Yo,r,i,n)){e.zrX=Yo[0],e.zrY=Yo[1];return}}e.zrX=e.zrY=0}function gu(r){return r||window.event}function Kt(r,t,e){if(t=gu(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&vl(r,o,t,e)}else{vl(r,t,t,e);var a=qm(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&Xm.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function qm(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,i=r.deltaY;if(e==null||i==null)return t;var n=Math.abs(i!==0?i:e),a=i>0?-1:i<0?1:e>0?-1:1;return 3*n*a}function Km(r,t,e,i){r.addEventListener(t,e,i)}function Qm(r,t,e,i){r.removeEventListener(t,e,i)}var ud=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0},Jm=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,i){var n=t.touches;if(n){for(var a={points:[],touches:[],target:e,event:t},o=0,s=n.length;o<s;o++){var l=n[o],u=vl(i,l,{});a.points.push([u.zrX,u.zrY]),a.touches.push(l)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in Xo)if(Xo.hasOwnProperty(e)){var i=Xo[e](this._track,t);if(i)return i}},r}();function If(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function jm(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var Xo={pinch:function(r,t){var e=r.length;if(e){var i=(r[e-1]||{}).points,n=(r[e-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=If(i)/If(n);!isFinite(a)&&(a=1),t.pinchScale=a;var o=jm(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:r[0].target,event:t}}}}};function _i(){return[1,0,0,1,0,0]}function yu(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function t0(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function Si(r,t,e){var i=t[0]*e[0]+t[2]*e[1],n=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],o=t[1]*e[2]+t[3]*e[3],s=t[0]*e[4]+t[2]*e[5]+t[4],l=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=i,r[1]=n,r[2]=a,r[3]=o,r[4]=s,r[5]=l,r}function cl(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function mu(r,t,e,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],o=t[4],s=t[1],l=t[3],u=t[5],f=Math.sin(e),h=Math.cos(e);return r[0]=n*h+s*f,r[1]=-n*f+s*h,r[2]=a*h+l*f,r[3]=-a*f+h*l,r[4]=h*(o-i[0])+f*(u-i[1])+i[0],r[5]=h*(u-i[1])-f*(o-i[0])+i[1],r}function e0(r,t,e){var i=e[0],n=e[1];return r[0]=t[0]*i,r[1]=t[1]*n,r[2]=t[2]*i,r[3]=t[3]*n,r[4]=t[4]*i,r[5]=t[5]*n,r}function _u(r,t){var e=t[0],i=t[2],n=t[4],a=t[1],o=t[3],s=t[5],l=e*o-a*i;return l?(l=1/l,r[0]=o*l,r[1]=-a*l,r[2]=-i*l,r[3]=e*l,r[4]=(i*s-o*n)*l,r[5]=(a*n-e*s)*l,r):null}var r0=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,i){t.x=e,t.y=i},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},r.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},r.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},r.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},r.lerp=function(t,e,i,n){var a=1-n;t.x=a*e.x+n*i.x,t.y=a*e.y+n*i.y},r}();const ot=r0;var Xn=Math.min,Zn=Math.max,lr=new ot,ur=new ot,fr=new ot,hr=new ot,Oi=new ot,Bi=new ot,i0=function(){function r(t,e,i,n){i<0&&(t=t+i,i=-i),n<0&&(e=e+n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return r.prototype.union=function(t){var e=Xn(t.x,this.x),i=Xn(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Zn(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Zn(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,a=_i();return cl(a,a,[-e.x,-e.y]),e0(a,a,[i,n]),cl(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var i=this,n=i.x,a=i.x+i.width,o=i.y,s=i.y+i.height,l=t.x,u=t.x+t.width,f=t.y,h=t.y+t.height,c=!(a<l||u<n||s<f||h<o);if(e){var v=1/0,d=0,y=Math.abs(a-l),p=Math.abs(u-n),g=Math.abs(s-f),m=Math.abs(h-o),_=Math.min(y,p),S=Math.min(g,m);a<l||u<n?_>d&&(d=_,y<p?ot.set(Bi,-y,0):ot.set(Bi,p,0)):_<v&&(v=_,y<p?ot.set(Oi,y,0):ot.set(Oi,-p,0)),s<f||h<o?S>d&&(d=S,g<m?ot.set(Bi,0,-g):ot.set(Bi,0,m)):_<v&&(v=_,g<m?ot.set(Oi,0,g):ot.set(Oi,0,-m))}return e&&ot.copy(e,c?Oi:Bi),c},r.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,i){if(!i){t!==e&&r.copy(t,e);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],o=i[4],s=i[5];t.x=e.x*n+o,t.y=e.y*a+s,t.width=e.width*n,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}lr.x=fr.x=e.x,lr.y=hr.y=e.y,ur.x=hr.x=e.x+e.width,ur.y=fr.y=e.y+e.height,lr.transform(i),hr.transform(i),ur.transform(i),fr.transform(i),t.x=Xn(lr.x,ur.x,fr.x,hr.x),t.y=Xn(lr.y,ur.y,fr.y,hr.y);var l=Zn(lr.x,ur.x,fr.x,hr.x),u=Zn(lr.y,ur.y,fr.y,hr.y);t.width=l-t.x,t.height=u-t.y},r}();const et=i0;var fd="silent";function n0(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:a0}}function a0(){ud(this.event)}var o0=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(Te),Ni=function(){function r(t,e){this.x=t,this.y=e}return r}(),s0=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Zo=new et(0,0,0,0),hd=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this)||this;return s._hovered=new Ni(0,0),s.storage=e,s.painter=i,s.painterRoot=a,s._pointerSize=o,n=n||new o0,s.proxy=null,s.setHandlerProxy(n),s._draggingMgr=new zm(s),s}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(A(s0,function(i){e.on&&e.on(i,this[i],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var i=e.zrX,n=e.zrY,a=vd(this,i,n),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var l=this._hovered=a?new Ni(i,n):this.findHover(i,n),u=l.target,f=this.proxy;f.setCursor&&f.setCursor(u?u.cursor:"default"),s&&u!==s&&this.dispatchToElement(o,"mouseout",e),this.dispatchToElement(l,"mousemove",e),u&&u!==s&&this.dispatchToElement(l,"mouseover",e)},t.prototype.mouseout=function(e){var i=e.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new Ni(0,0)},t.prototype.dispatch=function(e,i){var n=this[e];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var i=this.proxy;i.setCursor&&i.setCursor(e)},t.prototype.dispatchToElement=function(e,i,n){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var o="on"+i,s=n0(i,e,n);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(i,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(l){typeof l[o]=="function"&&l[o].call(l,s),l.trigger&&l.trigger(i,s)}))}},t.prototype.findHover=function(e,i,n){var a=this.storage.getDisplayList(),o=new Ni(e,i);if(Pf(a,o,e,i,n),this._pointerSize&&!o.target){for(var s=[],l=this._pointerSize,u=l/2,f=new et(e-u,i-u,l,l),h=a.length-1;h>=0;h--){var c=a[h];c!==n&&!c.ignore&&!c.ignoreCoarsePointer&&(!c.parent||!c.parent.ignoreCoarsePointer)&&(Zo.copy(c.getBoundingRect()),c.transform&&Zo.applyTransform(c.transform),Zo.intersect(f)&&s.push(c))}if(s.length)for(var v=4,d=Math.PI/12,y=Math.PI*2,p=0;p<u;p+=v)for(var g=0;g<y;g+=d){var m=e+p*Math.cos(g),_=i+p*Math.sin(g);if(Pf(s,o,m,_,n),o.target)return o}}return o},t.prototype.processGesture=function(e,i){this._gestureMgr||(this._gestureMgr=new Jm);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var o=a.type;e.gestureEvent=o;var s=new Ni;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(Te);A(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){hd.prototype[r]=function(t){var e=t.zrX,i=t.zrY,n=vd(this,e,i),a,o;if((r!=="mouseup"||!n)&&(a=this.findHover(e,i),o=a.target),r==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(r==="mouseup")this._upEl=o;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||Bm(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function l0(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var i=r,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(t,e))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return n?fd:!0}return!1}function Pf(r,t,e,i,n){for(var a=r.length-1;a>=0;a--){var o=r[a],s=void 0;if(o!==n&&!o.ignore&&(s=l0(o,e,i))&&(!t.topTarget&&(t.topTarget=o),s!==fd)){t.target=o;break}}}function vd(r,t,e){var i=r.painter;return t<0||t>i.getWidth()||e<0||e>i.getHeight()}const u0=hd;var cd=32,Fi=7;function f0(r){for(var t=0;r>=cd;)t|=r&1,r>>=1;return r+t}function Rf(r,t,e,i){var n=t+1;if(n===e)return 1;if(i(r[n++],r[t])<0){for(;n<e&&i(r[n],r[n-1])<0;)n++;h0(r,t,n)}else for(;n<e&&i(r[n],r[n-1])>=0;)n++;return n-t}function h0(r,t,e){for(e--;t<e;){var i=r[t];r[t++]=r[e],r[e--]=i}}function Ef(r,t,e,i,n){for(i===t&&i++;i<e;i++){for(var a=r[i],o=t,s=i,l;o<s;)l=o+s>>>1,n(a,r[l])<0?s=l:o=l+1;var u=i-o;switch(u){case 3:r[o+3]=r[o+2];case 2:r[o+2]=r[o+1];case 1:r[o+1]=r[o];break;default:for(;u>0;)r[o+u]=r[o+u-1],u--}r[o]=a}}function qo(r,t,e,i,n,a){var o=0,s=0,l=1;if(a(r,t[e+n])>0){for(s=i-n;l<s&&a(r,t[e+n+l])>0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),o+=n,l+=n}else{for(s=n+1;l<s&&a(r,t[e+n-l])<=0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var u=o;o=n-l,l=n-u}for(o++;o<l;){var f=o+(l-o>>>1);a(r,t[e+f])>0?o=f+1:l=f}return l}function Ko(r,t,e,i,n,a){var o=0,s=0,l=1;if(a(r,t[e+n])<0){for(s=n+1;l<s&&a(r,t[e+n-l])<0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var u=o;o=n-l,l=n-u}else{for(s=i-n;l<s&&a(r,t[e+n+l])>=0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),o+=n,l+=n}for(o++;o<l;){var f=o+(l-o>>>1);a(r,t[e+f])<0?l=f:o=f+1}return l}function v0(r,t){var e=Fi,i,n,a=0,o=[];i=[],n=[];function s(v,d){i[a]=v,n[a]=d,a+=1}function l(){for(;a>1;){var v=a-2;if(v>=1&&n[v-1]<=n[v]+n[v+1]||v>=2&&n[v-2]<=n[v]+n[v-1])n[v-1]<n[v+1]&&v--;else if(n[v]>n[v+1])break;f(v)}}function u(){for(;a>1;){var v=a-2;v>0&&n[v-1]<n[v+1]&&v--,f(v)}}function f(v){var d=i[v],y=n[v],p=i[v+1],g=n[v+1];n[v]=y+g,v===a-3&&(i[v+1]=i[v+2],n[v+1]=n[v+2]),a--;var m=Ko(r[p],r,d,y,0,t);d+=m,y-=m,y!==0&&(g=qo(r[d+y-1],r,p,g,g-1,t),g!==0&&(y<=g?h(d,y,p,g):c(d,y,p,g)))}function h(v,d,y,p){var g=0;for(g=0;g<d;g++)o[g]=r[v+g];var m=0,_=y,S=v;if(r[S++]=r[_++],--p===0){for(g=0;g<d;g++)r[S+g]=o[m+g];return}if(d===1){for(g=0;g<p;g++)r[S+g]=r[_+g];r[S+p]=o[m];return}for(var b=e,w,x,C;;){w=0,x=0,C=!1;do if(t(r[_],o[m])<0){if(r[S++]=r[_++],x++,w=0,--p===0){C=!0;break}}else if(r[S++]=o[m++],w++,x=0,--d===1){C=!0;break}while((w|x)<b);if(C)break;do{if(w=Ko(r[_],o,m,d,0,t),w!==0){for(g=0;g<w;g++)r[S+g]=o[m+g];if(S+=w,m+=w,d-=w,d<=1){C=!0;break}}if(r[S++]=r[_++],--p===0){C=!0;break}if(x=qo(o[m],r,_,p,0,t),x!==0){for(g=0;g<x;g++)r[S+g]=r[_+g];if(S+=x,_+=x,p-=x,p===0){C=!0;break}}if(r[S++]=o[m++],--d===1){C=!0;break}b--}while(w>=Fi||x>=Fi);if(C)break;b<0&&(b=0),b+=2}if(e=b,e<1&&(e=1),d===1){for(g=0;g<p;g++)r[S+g]=r[_+g];r[S+p]=o[m]}else{if(d===0)throw new Error;for(g=0;g<d;g++)r[S+g]=o[m+g]}}function c(v,d,y,p){var g=0;for(g=0;g<p;g++)o[g]=r[y+g];var m=v+d-1,_=p-1,S=y+p-1,b=0,w=0;if(r[S--]=r[m--],--d===0){for(b=S-(p-1),g=0;g<p;g++)r[b+g]=o[g];return}if(p===1){for(S-=d,m-=d,w=S+1,b=m+1,g=d-1;g>=0;g--)r[w+g]=r[b+g];r[S]=o[_];return}for(var x=e;;){var C=0,T=0,D=!1;do if(t(o[_],r[m])<0){if(r[S--]=r[m--],C++,T=0,--d===0){D=!0;break}}else if(r[S--]=o[_--],T++,C=0,--p===1){D=!0;break}while((C|T)<x);if(D)break;do{if(C=d-Ko(o[_],r,v,d,d-1,t),C!==0){for(S-=C,m-=C,d-=C,w=S+1,b=m+1,g=C-1;g>=0;g--)r[w+g]=r[b+g];if(d===0){D=!0;break}}if(r[S--]=o[_--],--p===1){D=!0;break}if(T=p-qo(r[m],o,0,p,p-1,t),T!==0){for(S-=T,_-=T,p-=T,w=S+1,b=_+1,g=0;g<T;g++)r[w+g]=o[b+g];if(p<=1){D=!0;break}}if(r[S--]=r[m--],--d===0){D=!0;break}x--}while(C>=Fi||T>=Fi);if(D)break;x<0&&(x=0),x+=2}if(e=x,e<1&&(e=1),p===1){for(S-=d,m-=d,w=S+1,b=m+1,g=d-1;g>=0;g--)r[w+g]=r[b+g];r[S]=o[_]}else{if(p===0)throw new Error;for(b=S-(p-1),g=0;g<p;g++)r[b+g]=o[g]}}return{mergeRuns:l,forceMergeRuns:u,pushRun:s}}function Ra(r,t,e,i){e||(e=0),i||(i=r.length);var n=i-e;if(!(n<2)){var a=0;if(n<cd){a=Rf(r,e,i,t),Ef(r,e,i,e+a,t);return}var o=v0(r,t),s=f0(n);do{if(a=Rf(r,e,i,t),a<s){var l=n;l>s&&(l=s),Ef(r,e,e+l,e+a,t),a=l}o.pushRun(e,a),o.mergeRuns(),n-=a,e+=a}while(n!==0);o.forceMergeRuns()}}var $t=1,rn=2,vi=4,kf=!1;function Qo(){kf||(kf=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Of(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var c0=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Of}return r.prototype.traverse=function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(e),i},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,a=e.length;n<a;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,Ra(i,Of)},r.prototype._updateAndAddDisplayable=function(t,e,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e?e=e.slice():e=[];for(var a=n,o=t;a;)a.parent=o,a.updateTransform(),e.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),l=0;l<s.length;l++){var u=s[l];t.__dirty&&(u.__dirty|=$t),this._updateAndAddDisplayable(u,e,i)}t.__dirty=0}else{var f=t;e&&e.length?f.__clipPaths=e:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(Qo(),f.z=0),isNaN(f.z2)&&(Qo(),f.z2=0),isNaN(f.zlevel)&&(Qo(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,i);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,i);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,e,i)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);return}var n=at(this._roots,t);n>=0&&this._roots.splice(n,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}();const d0=c0;var dd;dd=Y.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};const dl=dd;var Ea={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)))},elasticOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/i)+1)},elasticInOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-Ea.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?Ea.bounceIn(r*2)*.5:Ea.bounceOut(r*2-1)*.5+.5}};const pd=Ea;var qn=Math.pow,je=Math.sqrt,Za=1e-8,gd=1e-4,Bf=je(3),Kn=1/3,me=Ii(),jt=Ii(),wi=Ii();function Qe(r){return r>-Za&&r<Za}function yd(r){return r>Za||r<-Za}function gt(r,t,e,i,n){var a=1-n;return a*a*(a*r+3*n*t)+n*n*(n*i+3*a*e)}function Nf(r,t,e,i,n){var a=1-n;return 3*(((t-r)*a+2*(e-t)*n)*a+(i-e)*n*n)}function qa(r,t,e,i,n,a){var o=i+3*(t-e)-r,s=3*(e-t*2+r),l=3*(t-r),u=r-n,f=s*s-3*o*l,h=s*l-9*o*u,c=l*l-3*s*u,v=0;if(Qe(f)&&Qe(h))if(Qe(s))a[0]=0;else{var d=-l/s;d>=0&&d<=1&&(a[v++]=d)}else{var y=h*h-4*f*c;if(Qe(y)){var p=h/f,d=-s/o+p,g=-p/2;d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g)}else if(y>0){var m=je(y),_=f*s+1.5*o*(-h+m),S=f*s+1.5*o*(-h-m);_<0?_=-qn(-_,Kn):_=qn(_,Kn),S<0?S=-qn(-S,Kn):S=qn(S,Kn);var d=(-s-(_+S))/(3*o);d>=0&&d<=1&&(a[v++]=d)}else{var b=(2*f*s-3*o*h)/(2*je(f*f*f)),w=Math.acos(b)/3,x=je(f),C=Math.cos(w),d=(-s-2*x*C)/(3*o),g=(-s+x*(C+Bf*Math.sin(w)))/(3*o),T=(-s+x*(C-Bf*Math.sin(w)))/(3*o);d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g),T>=0&&T<=1&&(a[v++]=T)}}return v}function md(r,t,e,i,n){var a=6*e-12*t+6*r,o=9*t+3*i-3*r-9*e,s=3*t-3*r,l=0;if(Qe(o)){if(yd(a)){var u=-s/a;u>=0&&u<=1&&(n[l++]=u)}}else{var f=a*a-4*o*s;if(Qe(f))n[0]=-a/(2*o);else if(f>0){var h=je(f),u=(-a+h)/(2*o),c=(-a-h)/(2*o);u>=0&&u<=1&&(n[l++]=u),c>=0&&c<=1&&(n[l++]=c)}}return l}function Ka(r,t,e,i,n,a){var o=(t-r)*n+r,s=(e-t)*n+t,l=(i-e)*n+e,u=(s-o)*n+o,f=(l-s)*n+s,h=(f-u)*n+u;a[0]=r,a[1]=o,a[2]=u,a[3]=h,a[4]=h,a[5]=f,a[6]=l,a[7]=i}function p0(r,t,e,i,n,a,o,s,l,u,f){var h,c=.005,v=1/0,d,y,p,g;me[0]=l,me[1]=u;for(var m=0;m<1;m+=.05)jt[0]=gt(r,e,n,o,m),jt[1]=gt(t,i,a,s,m),p=mi(me,jt),p<v&&(h=m,v=p);v=1/0;for(var _=0;_<32&&!(c<gd);_++)d=h-c,y=h+c,jt[0]=gt(r,e,n,o,d),jt[1]=gt(t,i,a,s,d),p=mi(jt,me),d>=0&&p<v?(h=d,v=p):(wi[0]=gt(r,e,n,o,y),wi[1]=gt(t,i,a,s,y),g=mi(wi,me),y<=1&&g<v?(h=y,v=g):c*=.5);return f&&(f[0]=gt(r,e,n,o,h),f[1]=gt(t,i,a,s,h)),je(v)}function g0(r,t,e,i,n,a,o,s,l){for(var u=r,f=t,h=0,c=1/l,v=1;v<=l;v++){var d=v*c,y=gt(r,e,n,o,d),p=gt(t,i,a,s,d),g=y-u,m=p-f;h+=Math.sqrt(g*g+m*m),u=y,f=p}return h}function Dt(r,t,e,i){var n=1-i;return n*(n*r+2*i*t)+i*i*e}function Ff(r,t,e,i){return 2*((1-i)*(t-r)+i*(e-t))}function y0(r,t,e,i,n){var a=r-2*t+e,o=2*(t-r),s=r-i,l=0;if(Qe(a)){if(yd(o)){var u=-s/o;u>=0&&u<=1&&(n[l++]=u)}}else{var f=o*o-4*a*s;if(Qe(f)){var u=-o/(2*a);u>=0&&u<=1&&(n[l++]=u)}else if(f>0){var h=je(f),u=(-o+h)/(2*a),c=(-o-h)/(2*a);u>=0&&u<=1&&(n[l++]=u),c>=0&&c<=1&&(n[l++]=c)}}return l}function _d(r,t,e){var i=r+e-2*t;return i===0?.5:(r-t)/i}function Qa(r,t,e,i,n){var a=(t-r)*i+r,o=(e-t)*i+t,s=(o-a)*i+a;n[0]=r,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=e}function m0(r,t,e,i,n,a,o,s,l){var u,f=.005,h=1/0;me[0]=o,me[1]=s;for(var c=0;c<1;c+=.05){jt[0]=Dt(r,e,n,c),jt[1]=Dt(t,i,a,c);var v=mi(me,jt);v<h&&(u=c,h=v)}h=1/0;for(var d=0;d<32&&!(f<gd);d++){var y=u-f,p=u+f;jt[0]=Dt(r,e,n,y),jt[1]=Dt(t,i,a,y);var v=mi(jt,me);if(y>=0&&v<h)u=y,h=v;else{wi[0]=Dt(r,e,n,p),wi[1]=Dt(t,i,a,p);var g=mi(wi,me);p<=1&&g<h?(u=p,h=g):f*=.5}}return l&&(l[0]=Dt(r,e,n,u),l[1]=Dt(t,i,a,u)),je(h)}function _0(r,t,e,i,n,a,o){for(var s=r,l=t,u=0,f=1/o,h=1;h<=o;h++){var c=h*f,v=Dt(r,e,n,c),d=Dt(t,i,a,c),y=v-s,p=d-l;u+=Math.sqrt(y*y+p*p),s=v,l=d}return u}var S0=/cubic-bezier\(([0-9,\.e ]+)\)/;function Sd(r){var t=r&&S0.exec(r);if(t){var e=t[1].split(","),i=+_e(e[0]),n=+_e(e[1]),a=+_e(e[2]),o=+_e(e[3]);if(isNaN(i+n+a+o))return;var s=[];return function(l){return l<=0?0:l>=1?1:qa(0,i,a,1,l,s)&&gt(0,n,o,1,s[0])}}}var w0=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ht,this.ondestroy=t.ondestroy||Ht,this.onrestart=t.onrestart||Ht,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var l=n%i;this._startTime=t-l,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=$(t)?t:pd[t]||Sd(t)},r}();const b0=w0;var wd=function(){function r(t){this.value=t}return r}(),x0=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new wd(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),T0=function(){function r(t){this._list=new x0,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var i=this._list,n=this._map,a=null;if(n[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var l=i.head;i.remove(l),delete n[l.key],a=l.value,this._lastRemovedEntry=l}s?s.value=e:s=new wd(e),s.key=t,i.insertEntry(s),n[t]=s}return a},r.prototype.get=function(t){var e=this._map[t],i=this._list;if(e!=null)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}();const Hn=T0;var zf={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function tr(r){return r=Math.round(r),r<0?0:r>255?255:r}function pl(r){return r<0?0:r>1?1:r}function Jo(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?tr(parseFloat(t)/100*255):tr(parseInt(t,10))}function hn(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?pl(parseFloat(t)/100):pl(parseFloat(t))}function jo(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function Qn(r,t,e){return r+(t-r)*e}function qt(r,t,e,i,n){return r[0]=t,r[1]=e,r[2]=i,r[3]=n,r}function gl(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var bd=new Hn(20),Jn=null;function Qr(r,t){Jn&&gl(Jn,t),Jn=bd.put(r,Jn||t.slice())}function Re(r,t){if(r){t=t||[];var e=bd.get(r);if(e)return gl(t,e);r=r+"";var i=r.replace(/ /g,"").toLowerCase();if(i in zf)return gl(t,zf[i]),Qr(r,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){qt(t,0,0,0,1);return}return qt(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),Qr(r,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){qt(t,0,0,0,1);return}return qt(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),Qr(r,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===n){var l=i.substr(0,o),u=i.substr(o+1,s-(o+1)).split(","),f=1;switch(l){case"rgba":if(u.length!==4)return u.length===3?qt(t,+u[0],+u[1],+u[2],1):qt(t,0,0,0,1);f=hn(u.pop());case"rgb":if(u.length>=3)return qt(t,Jo(u[0]),Jo(u[1]),Jo(u[2]),u.length===3?f:hn(u[3])),Qr(r,t),t;qt(t,0,0,0,1);return;case"hsla":if(u.length!==4){qt(t,0,0,0,1);return}return u[3]=hn(u[3]),Hf(u,t),Qr(r,t),t;case"hsl":if(u.length!==3){qt(t,0,0,0,1);return}return Hf(u,t),Qr(r,t),t;default:return}}qt(t,0,0,0,1)}}function Hf(r,t){var e=(parseFloat(r[0])%360+360)%360/360,i=hn(r[1]),n=hn(r[2]),a=n<=.5?n*(i+1):n+i-n*i,o=n*2-a;return t=t||[],qt(t,tr(jo(o,a,e+1/3)*255),tr(jo(o,a,e)*255),tr(jo(o,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function Gf(r,t){var e=Re(r);if(e){for(var i=0;i<3;i++)t<0?e[i]=e[i]*(1-t)|0:e[i]=(255-e[i])*t+e[i]|0,e[i]>255?e[i]=255:e[i]<0&&(e[i]=0);return _o(e,e.length===4?"rgba":"rgb")}}function C0(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=Re(t[n]),s=Re(t[a]),l=i-n,u=_o([tr(Qn(o[0],s[0],l)),tr(Qn(o[1],s[1],l)),tr(Qn(o[2],s[2],l)),pl(Qn(o[3],s[3],l))],"rgba");return e?{color:u,leftIndex:n,rightIndex:a,value:i}:u}}function _o(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function Ja(r,t){var e=Re(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}var Vf=new Hn(100);function Wf(r){if(z(r)){var t=Vf.get(r);return t||(t=Gf(r,-.1),Vf.put(r,t)),t}else if(yo(r)){var e=k({},r);return e.colorStops=V(r.colorStops,function(i){return{offset:i.offset,color:Gf(i.color,-.1)}}),e}return r}function D0(r){return r.type==="linear"}function M0(r){return r.type==="radial"}(function(){return Y.hasGlobalWindow&&$(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}})();var yl=Array.prototype.slice;function Ae(r,t,e){return(t-r)*e+r}function ts(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=Ae(t[a],e[a],i);return r}function A0(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=Ae(t[o][s],e[o][s],i)}return r}function jn(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=t[a]+e[a]*i;return r}function Uf(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=t[o][s]+e[o][s]*i}return r}function L0(r,t){for(var e=r.length,i=t.length,n=e>i?t:r,a=Math.min(e,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(e,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function I0(r,t,e){var i=r,n=t;if(!(!i.push||!n.push)){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var l=a;l<o;l++)i.push(e===1?n[l]:yl.call(n[l]))}for(var u=i[0]&&i[0].length,l=0;l<i.length;l++)if(e===1)isNaN(i[l])&&(i[l]=n[l]);else for(var f=0;f<u;f++)isNaN(i[l][f])&&(i[l][f]=n[l][f])}}function ka(r){if(Gt(r)){var t=r.length;if(Gt(r[0])){for(var e=[],i=0;i<t;i++)e.push(yl.call(r[i]));return e}return yl.call(r)}return r}function Oa(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function P0(r){return Gt(r&&r[0])?2:1}var ta=0,Ba=1,xd=2,nn=3,ml=4,_l=5,$f=6;function Yf(r){return r===ml||r===_l}function ea(r){return r===Ba||r===xd}var zi=[0,0,0,0],R0=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,i){this._needsSort=!0;var n=this.keyframes,a=n.length,o=!1,s=$f,l=e;if(Gt(e)){var u=P0(e);s=u,(u===1&&!vt(e[0])||u===2&&!vt(e[0][0]))&&(o=!0)}else if(vt(e)&&!Xa(e))s=ta;else if(z(e))if(!isNaN(+e))s=ta;else{var f=Re(e);f&&(l=f,s=nn)}else if(yo(e)){var h=k({},l);h.colorStops=V(e.colorStops,function(v){return{offset:v.offset,color:Re(v.color)}}),D0(e)?s=ml:M0(e)&&(s=_l),l=h}a===0?this.valType=s:(s!==this.valType||s===$f)&&(o=!0),this.discrete=this.discrete||o;var c={time:t,value:l,rawValue:e,percent:0};return i&&(c.easing=i,c.easingFunc=$(i)?i:pd[i]||Sd(i)),n.push(c),c},r.prototype.prepare=function(t,e){var i=this.keyframes;this._needsSort&&i.sort(function(y,p){return y.time-p.time});for(var n=this.valType,a=i.length,o=i[a-1],s=this.discrete,l=ea(n),u=Yf(n),f=0;f<a;f++){var h=i[f],c=h.value,v=o.value;h.percent=h.time/t,s||(l&&f!==a-1?I0(c,v,n):u&&L0(c.colorStops,v.colorStops))}if(!s&&n!==_l&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;for(var d=i[0].value,f=0;f<a;f++)n===ta?i[f].additiveValue=i[f].value-d:n===nn?i[f].additiveValue=jn([],i[f].value,d,-1):ea(n)&&(i[f].additiveValue=n===Ba?jn([],i[f].value,d,-1):Uf([],i[f].value,d,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,l=this.propName,u=a===nn,f,h=this._lastFr,c=Math.min,v,d;if(s===1)v=d=o[0];else{if(e<0)f=0;else if(e<this._lastFrP){var y=c(h+1,s-1);for(f=y;f>=0&&!(o[f].percent<=e);f--);f=c(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>e);f++);f=c(f-1,s-2)}d=o[f+1],v=o[f]}if(v&&d){this._lastFr=f,this._lastFrP=e;var p=d.percent-v.percent,g=p===0?1:c((e-v.percent)/p,1);d.easingFunc&&(g=d.easingFunc(g));var m=i?this._additiveValue:u?zi:t[l];if((ea(a)||u)&&!m&&(m=this._additiveValue=[]),this.discrete)t[l]=g<1?v.rawValue:d.rawValue;else if(ea(a))a===Ba?ts(m,v[n],d[n],g):A0(m,v[n],d[n],g);else if(Yf(a)){var _=v[n],S=d[n],b=a===ml;t[l]={type:b?"linear":"radial",x:Ae(_.x,S.x,g),y:Ae(_.y,S.y,g),colorStops:V(_.colorStops,function(x,C){var T=S.colorStops[C];return{offset:Ae(x.offset,T.offset,g),color:Oa(ts([],x.color,T.color,g))}}),global:S.global},b?(t[l].x2=Ae(_.x2,S.x2,g),t[l].y2=Ae(_.y2,S.y2,g)):t[l].r=Ae(_.r,S.r,g)}else if(u)ts(m,v[n],d[n],g),i||(t[l]=Oa(m));else{var w=Ae(v[n],d[n],g);i?this._additiveValue=w:t[l]=w}i&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,i=this.propName,n=this._additiveValue;e===ta?t[i]=t[i]+n:e===nn?(Re(t[i],zi),jn(zi,zi,n,1),t[i]=Oa(zi)):e===Ba?jn(t[i],t[i],n,1):e===xd&&Uf(t[i],t[i],n,1)},r}(),E0=function(){function r(t,e,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n){du("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,i){return this.whenWithKeys(t,e,ht(e),i)},r.prototype.whenWithKeys=function(t,e,i,n){for(var a=this._tracks,o=0;o<i.length;o++){var s=i[o],l=a[s];if(!l){l=a[s]=new R0(s);var u=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,c=h[h.length-1];u=c&&c.value,f.valType===nn&&u&&(u=Oa(u))}else u=this._target[s];if(u==null)continue;t>0&&l.addKeyframe(0,ka(u),n),this._trackKeys.push(s)}l.addKeyframe(t,ka(e[s]),n)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,i=0;i<e;i++)t[i].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var i=0;i<e.length;i++)e[i].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,i=0;i<e.length;i++)t[e[i]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],l=this._getAdditiveTrack(o),u=s.keyframes,f=u.length;if(s.prepare(n,l),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=u[f-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var c=new b0({life:n,loop:this._loop,delay:this._delay||0,onframe:function(v){e._started=2;var d=e._additiveAnimators;if(d){for(var y=!1,p=0;p<d.length;p++)if(d[p]._clip){y=!0;break}y||(e._additiveAnimators=null)}for(var p=0;p<i.length;p++)i[p].step(e._target,v);var g=e._onframeCbs;if(g)for(var p=0;p<g.length;p++)g[p](e._target,v)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return V(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var o=i[t[a]];o&&!o.isFinished()&&(e?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},r.prototype.saveTo=function(t,e,i){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var a=e[n],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,l=s[i?0:s.length-1];l&&(t[a]=ka(l.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||ht(t);for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}();const Su=E0;function gi(){return new Date().getTime()}var k0=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,e=e||{},i.stage=e.stage||{},i}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var i=e.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(e){if(e.animation){var i=e.prev,n=e.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var i=e.getClip();i&&this.removeClip(i),e.animation=null},t.prototype.update=function(e){for(var i=gi()-this._pausedTime,n=i-this._time,a=this._head;a;){var o=a.next,s=a.step(i,n);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=i,e||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function i(){e._running&&(dl(i),!e._paused&&e.update())}dl(i)},t.prototype.start=function(){this._running||(this._time=gi(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=gi(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=gi()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var i=e.next;e.prev=e.next=e.animation=null,e=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,i){i=i||{},this.start();var n=new Su(e,i.loop);return this.addAnimator(n),n},t}(Te);const O0=k0;var B0=300,es=Y.domSupported,rs=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=V(r,function(n){var a=n.replace("mouse","pointer");return e.hasOwnProperty(a)?a:n});return{mouse:r,touch:t,pointer:i}}(),Xf={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Zf=!1;function Sl(r){var t=r.pointerType;return t==="pen"||t==="touch"}function N0(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function is(r){r&&(r.zrByTouch=!0)}function F0(r,t){return Kt(r.dom,new z0(r,t),!0)}function Td(r,t){for(var e=t,i=!1;e&&e.nodeType!==9&&!(i=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return i}var z0=function(){function r(t,e){this.stopPropagation=Ht,this.stopImmediatePropagation=Ht,this.preventDefault=Ht,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),ue={mousedown:function(r){r=Kt(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=Kt(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=Kt(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=Kt(this.dom,r);var t=r.toElement||r.relatedTarget;Td(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){Zf=!0,r=Kt(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){Zf||(r=Kt(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=Kt(this.dom,r),is(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),ue.mousemove.call(this,r),ue.mousedown.call(this,r)},touchmove:function(r){r=Kt(this.dom,r),is(r),this.handler.processGesture(r,"change"),ue.mousemove.call(this,r)},touchend:function(r){r=Kt(this.dom,r),is(r),this.handler.processGesture(r,"end"),ue.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<B0&&ue.click.call(this,r)},pointerdown:function(r){ue.mousedown.call(this,r)},pointermove:function(r){Sl(r)||ue.mousemove.call(this,r)},pointerup:function(r){ue.mouseup.call(this,r)},pointerout:function(r){Sl(r)||ue.mouseout.call(this,r)}};A(["click","dblclick","contextmenu"],function(r){ue[r]=function(t){t=Kt(this.dom,t),this.trigger(r,t)}});var wl={pointermove:function(r){Sl(r)||wl.mousemove.call(this,r)},pointerup:function(r){wl.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function H0(r,t){var e=t.domHandlers;Y.pointerEventsSupported?A(rs.pointer,function(i){Na(t,i,function(n){e[i].call(r,n)})}):(Y.touchEventsSupported&&A(rs.touch,function(i){Na(t,i,function(n){e[i].call(r,n),N0(t)})}),A(rs.mouse,function(i){Na(t,i,function(n){n=gu(n),t.touching||e[i].call(r,n)})}))}function G0(r,t){Y.pointerEventsSupported?A(Xf.pointer,e):Y.touchEventsSupported||A(Xf.mouse,e);function e(i){function n(a){a=gu(a),Td(r,a.target)||(a=F0(r,a),t.domHandlers[i].call(r,a))}Na(t,i,n,{capture:!0})}}function Na(r,t,e,i){r.mounted[t]=e,r.listenerOpts[t]=i,Km(r.domTarget,t,e,i)}function ns(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&Qm(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var qf=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),V0=function(r){O(t,r);function t(e,i){var n=r.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=i,n._localHandlerScope=new qf(e,ue),es&&(n._globalHandlerScope=new qf(document,wl)),H0(n,n._localHandlerScope),n}return t.prototype.dispose=function(){ns(this._localHandlerScope),es&&ns(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,es&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var i=this._globalHandlerScope;e?G0(this,i):ns(i)}},t}(Te);const W0=V0;var Cd=1;Y.hasGlobalWindow&&(Cd=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var ja=Cd,bl=.4,xl="#333",Tl="#ccc",U0="#eee",Kf=yu,Qf=5e-5;function vr(r){return r>Qf||r<-Qf}var cr=[],Jr=[],as=_i(),os=Math.abs,$0=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return vr(this.rotation)||vr(this.x)||vr(this.y)||vr(this.scaleX-1)||vr(this.scaleY-1)||vr(this.skewX)||vr(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;if(!(e||t)){i&&(Kf(i),this.invTransform=null);return}i=i||_i(),e?this.getLocalTransform(i):Kf(i),t&&(e?Si(i,t,i):t0(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(cr);var i=cr[0]<0?-1:1,n=cr[1]<0?-1:1,a=((cr[0]-i)*e+i)/cr[0]||0,o=((cr[1]-n)*e+n)/cr[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||_i(),_u(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||_i(),Si(Jr,t.invTransform,e),e=Jr);var i=this.originX,n=this.originY;(i||n)&&(as[4]=i,as[5]=n,Si(Jr,e,as),Jr[4]-=i,Jr[5]-=n,e=Jr),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&ie(i,i,n),i},r.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&ie(i,i,n),i},r.prototype.getLineScale=function(){var t=this.transform;return t&&os(t[0]-1)>1e-10&&os(t[3]-1)>1e-10?Math.sqrt(os(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){Y0(this,t)},r.getLocalTransform=function(t,e){e=e||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,l=t.anchorY,u=t.rotation||0,f=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,v=t.skewY?Math.tan(-t.skewY):0;if(i||n||s||l){var d=i+s,y=n+l;e[4]=-d*a-c*y*o,e[5]=-y*o-v*d*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=v*a,e[2]=c*o,u&&mu(e,e,u),e[4]+=i+f,e[5]+=n+h,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),Cn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function Y0(r,t){for(var e=0;e<Cn.length;e++){var i=Cn[e];r[i]=t[i]}}const wu=$0;var Jf={};function Yt(r,t){t=t||Fr;var e=Jf[t];e||(e=Jf[t]=new Hn(500));var i=e.get(r);return i==null&&(i=Li.measureText(r,t).width,e.put(r,i)),i}function jf(r,t,e,i){var n=Yt(r,t),a=xu(t),o=an(0,n,e),s=ci(0,a,i),l=new et(o,s,n,a);return l}function bu(r,t,e,i){var n=((r||"")+"").split(`
`),a=n.length;if(a===1)return jf(n[0],t,e,i);for(var o=new et(0,0,0,0),s=0;s<n.length;s++){var l=jf(n[s],t,e,i);s===0?o.copy(l):o.union(l)}return o}function an(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function ci(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function xu(r){return Yt("国",r)}function Hr(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function Dd(r,t,e){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=e.height,o=e.width,s=a/2,l=e.x,u=e.y,f="left",h="top";if(i instanceof Array)l+=Hr(i[0],e.width),u+=Hr(i[1],e.height),f=null,h=null;else switch(i){case"left":l-=n,u+=s,f="right",h="middle";break;case"right":l+=n+o,u+=s,h="middle";break;case"top":l+=o/2,u-=n,f="center",h="bottom";break;case"bottom":l+=o/2,u+=a+n,f="center";break;case"inside":l+=o/2,u+=s,f="center",h="middle";break;case"insideLeft":l+=n,u+=s,h="middle";break;case"insideRight":l+=o-n,u+=s,f="right",h="middle";break;case"insideTop":l+=o/2,u+=n,f="center";break;case"insideBottom":l+=o/2,u+=a-n,f="center",h="bottom";break;case"insideTopLeft":l+=n,u+=n;break;case"insideTopRight":l+=o-n,u+=n,f="right";break;case"insideBottomLeft":l+=n,u+=a-n,h="bottom";break;case"insideBottomRight":l+=o-n,u+=a-n,f="right",h="bottom";break}return r=r||{},r.x=l,r.y=u,r.align=f,r.verticalAlign=h,r}var ss="__zr_normal__",ls=Cn.concat(["ignore"]),X0=ir(Cn,function(r,t){return r[t]=!0,r},{ignore:!1}),jr={},Z0=new et(0,0,0,0),Tu=function(){function r(t){this.id=id(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,i){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=e.innerTransformable,o=void 0,s=void 0,l=!1;a.parent=n?this:null;var u=!1;if(a.copyTransform(e),i.position!=null){var f=Z0;i.layoutRect?f.copy(i.layoutRect):f.copy(this.getBoundingRect()),n||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(jr,i,f):Dd(jr,i,f),a.x=jr.x,a.y=jr.y,o=jr.align,s=jr.verticalAlign;var h=i.origin;if(h&&i.rotation!=null){var c=void 0,v=void 0;h==="center"?(c=f.width*.5,v=f.height*.5):(c=Hr(h[0],f.width),v=Hr(h[1],f.height)),u=!0,a.originX=-a.x+c+(n?0:f.x),a.originY=-a.y+v+(n?0:f.y)}}i.rotation!=null&&(a.rotation=i.rotation);var d=i.offset;d&&(a.x+=d[0],a.y+=d[1],u||(a.originX=-d[0],a.originY=-d[1]));var y=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,p=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=void 0,m=void 0,_=void 0;y&&this.canBeInsideText()?(g=i.insideFill,m=i.insideStroke,(g==null||g==="auto")&&(g=this.getInsideTextFill()),(m==null||m==="auto")&&(m=this.getInsideTextStroke(g),_=!0)):(g=i.outsideFill,m=i.outsideStroke,(g==null||g==="auto")&&(g=this.getOutsideFill()),(m==null||m==="auto")&&(m=this.getOutsideStroke(g),_=!0)),g=g||"#000",(g!==p.fill||m!==p.stroke||_!==p.autoStroke||o!==p.align||s!==p.verticalAlign)&&(l=!0,p.fill=g,p.stroke=m,p.autoStroke=_,p.align=o,p.verticalAlign=s,e.setDefaultTextStyle(p)),e.__dirty|=$t,l&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Tl:xl},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),i=typeof e=="string"&&Re(e);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*n+(a?0:255)*(1-n);return i[3]=1,_o(i,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},k(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(H(t))for(var i=t,n=ht(i),a=0;a<n.length;a++){var o=n[a];this.attrKV(o,t[o])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==ss)){var o=n.targetName,s=o?e[o]:e;n.saveTo(s)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,ls)},r.prototype._savePrimaryToNormal=function(t,e,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(ss,!1,t)},r.prototype.useState=function(t,e,i,n){var a=t===ss,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,l=this.stateTransition;if(!(at(s,t)>=0&&(e||s.length===1))){var u;if(this.stateProxy&&!a&&(u=this.stateProxy(t)),u||(u=this.states&&this.states[t]),!u&&!a){du("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(u);var f=!!(u&&u.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,u,this._normalState,e,!i&&!this.__inHover&&l&&l.duration>0,l);var h=this._textContent,c=this._textGuide;return h&&h.useState(t,e,i,f),c&&c.useState(t,e,i,f),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~$t),u}}},r.prototype.useStates=function(t,e,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var l=0;l<o;l++)if(t[l]!==a[l]){s=!1;break}}if(s)return;for(var l=0;l<o;l++){var u=t[l],f=void 0;this.stateProxy&&(f=this.stateProxy(u,t)),f||(f=this.states[u]),f&&n.push(f)}var h=n[o-1],c=!!(h&&h.hoverLayer||i);c&&this._toggleHoverLayerFlag(!0);var v=this._mergeStates(n),d=this.stateTransition;this.saveCurrentToNormalState(v),this._applyStateObj(t.join(","),v,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var y=this._textContent,p=this._textGuide;y&&y.useStates(t,e,c),p&&p.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~$t)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=at(this.currentStates,t);if(e>=0){var i=this.currentStates.slice();i.splice(e,1),this.useStates(i)}},r.prototype.replaceState=function(t,e,i){var n=this.currentStates.slice(),a=at(n,t),o=at(n,e)>=0;a>=0?o?n.splice(a,1):n[a]=e:i&&!o&&n.push(e),this.useStates(n)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},i,n=0;n<t.length;n++){var a=t[n];k(e,a),a.textConfig&&(i=i||{},k(i,a.textConfig))}return i&&(e.textConfig=i),e},r.prototype._applyStateObj=function(t,e,i,n,a,o){var s=!(e&&n);e&&e.textConfig?(this.textConfig=k({},n?this.textConfig:i.textConfig),k(this.textConfig,e.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var l={},u=!1,f=0;f<ls.length;f++){var h=ls[f],c=a&&X0[h];e&&e[h]!=null?c?(u=!0,l[h]=e[h]):this[h]=e[h]:s&&i[h]!=null&&(c?(u=!0,l[h]=i[h]):this[h]=i[h])}if(!a)for(var f=0;f<this.animators.length;f++){var v=this.animators[f],d=v.targetName;v.getLoop()||v.__changeFinalValue(d?(e||i)[d]:e||i)}u&&this._transitionState(t,l,o)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new wu,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),k(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=$t;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,i=this._textGuide;e&&(e.__inHover=t),i&&(i.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,i){var n=t?this[t]:this,a=new Su(n,e,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(e)}).done(function(){var a=n.animators,o=at(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var i=this.animators,n=i.length,a=[],o=0;o<n;o++){var s=i[o];!t||t===s.scope?s.stop(e):a.push(s)}return this.animators=a,this},r.prototype.animateTo=function(t,e,i){us(this,t,e,i)},r.prototype.animateFrom=function(t,e,i){us(this,t,e,i,!0)},r.prototype._transitionState=function(t,e,i,n){for(var a=us(this,e,i,n),o=0;o<a.length;o++)a[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=$t;function e(i,n,a,o){Object.defineProperty(t,i,{get:function(){if(!this[n]){var l=this[n]=[];s(this,l)}return this[n]},set:function(l){this[a]=l[0],this[o]=l[1],this[n]=l,s(this,l)}});function s(l,u){Object.defineProperty(u,0,{get:function(){return l[a]},set:function(f){l[a]=f}}),Object.defineProperty(u,1,{get:function(){return l[o]},set:function(f){l[o]=f}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();xe(Tu,Te);xe(Tu,wu);function us(r,t,e,i,n){e=e||{};var a=[];Md(r,"",r,t,e,i,a,n);var o=a.length,s=!1,l=e.done,u=e.aborted,f=function(){s=!0,o--,o<=0&&(s?l&&l():u&&u())},h=function(){o--,o<=0&&(s?l&&l():u&&u())};o||l&&l(),a.length>0&&e.during&&a[0].during(function(d,y){e.during(y)});for(var c=0;c<a.length;c++){var v=a[c];f&&v.done(f),h&&v.aborted(h),e.force&&v.duration(e.duration),v.start(e.easing)}return a}function fs(r,t,e){for(var i=0;i<e;i++)r[i]=t[i]}function q0(r){return Gt(r[0])}function K0(r,t,e){if(Gt(t[e]))if(Gt(r[e])||(r[e]=[]),Vt(t[e])){var i=t[e].length;r[e].length!==i&&(r[e]=new t[e].constructor(i),fs(r[e],t[e],i))}else{var n=t[e],a=r[e],o=n.length;if(q0(n))for(var s=n[0].length,l=0;l<o;l++)a[l]?fs(a[l],n[l],s):a[l]=Array.prototype.slice.call(n[l]);else fs(a,n,o);a.length=n.length}else r[e]=t[e]}function Q0(r,t){return r===t||Gt(r)&&Gt(t)&&J0(r,t)}function J0(r,t){var e=r.length;if(e!==t.length)return!1;for(var i=0;i<e;i++)if(r[i]!==t[i])return!1;return!0}function Md(r,t,e,i,n,a,o,s){for(var l=ht(i),u=n.duration,f=n.delay,h=n.additive,c=n.setToFinal,v=!H(a),d=r.animators,y=[],p=0;p<l.length;p++){var g=l[p],m=i[g];if(m!=null&&e[g]!=null&&(v||a[g]))if(H(m)&&!Gt(m)&&!yo(m)){if(t){s||(e[g]=m,r.updateDuringAnimation(t));continue}Md(r,g,e[g],m,n,a&&a[g],o,s)}else y.push(g);else s||(e[g]=m,r.updateDuringAnimation(t),y.push(g))}var _=y.length;if(!h&&_)for(var S=0;S<d.length;S++){var b=d[S];if(b.targetName===t){var w=b.stopTracks(y);if(w){var x=at(d,b);d.splice(x,1)}}}if(n.force||(y=wt(y,function(M){return!Q0(i[M],e[M])}),_=y.length),_>0||n.force&&!o.length){var C=void 0,T=void 0,D=void 0;if(s){T={},c&&(C={});for(var S=0;S<_;S++){var g=y[S];T[g]=e[g],c?C[g]=i[g]:e[g]=i[g]}}else if(c){D={};for(var S=0;S<_;S++){var g=y[S];D[g]=ka(e[g]),K0(e,i,g)}}var b=new Su(e,!1,!1,h?wt(d,function(L){return L.targetName===t}):null);b.targetName=t,n.scope&&(b.scope=n.scope),c&&C&&b.whenWithKeys(0,C,y),D&&b.whenWithKeys(0,D,y),b.whenWithKeys(u??500,s?T:i,y).delay(f||0),r.addAnimator(b,t),o.push(b)}}const Ad=Tu;var Ld=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===e)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,i){if(e&&e!==this&&e.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,i){var n=at(this._children,e);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(e,i){var n=this._children,a=n[i];if(e&&e!==this&&e.parent!==this&&e!==a){n[i]=e,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var i=this.__zr;i&&i!==e.__zr&&e.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(e){var i=this.__zr,n=this._children,a=at(n,e);return a<0?this:(n.splice(a,1),e.parent=null,i&&e.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,i=this.__zr,n=0;n<e.length;n++){var a=e[n];i&&a.removeSelfFromZr(i),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,i){for(var n=this._children,a=0;a<n.length;a++){var o=n[a];e.call(i,o,a)}return this},t.prototype.traverse=function(e,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],o=e.call(i,a);a.isGroup&&!o&&a.traverse(e,i)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var i=new et(0,0,0,0),n=e||this._children,a=[],o=null,s=0;s<n.length;s++){var l=n[s];if(!(l.ignore||l.invisible)){var u=l.getBoundingRect(),f=l.getLocalTransform(a);f?(et.applyTransform(i,u,f),o=o||i.clone(),o.union(i)):(o=o||u.clone(),o.union(u))}}return o||i},t}(Ad);Ld.prototype.type="group";const Et=Ld;/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Fa={},Id={};function j0(r){delete Id[r]}function t_(r){if(!r)return!1;if(typeof r=="string")return Ja(r,1)<bl;if(r.colorStops){for(var t=r.colorStops,e=0,i=t.length,n=0;n<i;n++)e+=Ja(t[n].color,1);return e/=i,e<bl}return!1}var e_=function(){function r(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var a=new d0,o=i.renderer||"canvas";Fa[o]||(o=ht(Fa)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Fa[o](e,a,i,t),l=i.ssr||s.ssrOnly;this.storage=a,this.painter=s;var u=!Y.node&&!Y.worker&&!l?new W0(s.getViewportRoot(),s.root):null,f=i.useCoarsePointer,h=f==null||f==="auto"?Y.touchEventsSupported:!!f,c=44,v;h&&(v=Z(i.pointerSize,c)),this.handler=new u0(a,s,u,s.root,v),this.animation=new O0({stage:{update:l?null:function(){return n._flush(!0)}}}),l||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=t_(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,i=gi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=gi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,i){return this._disposed||this.handler.on(t,e,i),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Et&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,j0(this.id))},r}();function th(r,t){var e=new e_(id(),r,t);return Id[e.id]=e,e}function r_(r,t){Fa[r]=t}var eh=1e-4,Pd=20;function i_(r){return r.replace(/^\s+|\s+$/g,"")}function rh(r,t,e,i){var n=t[0],a=t[1],o=e[0],s=e[1],l=a-n,u=s-o;if(l===0)return u===0?o:(o+s)/2;if(i)if(l>0){if(r<=n)return o;if(r>=a)return s}else{if(r>=n)return o;if(r<=a)return s}else{if(r===n)return o;if(r===a)return s}return(r-n)/l*u+o}function Pt(r,t){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return z(r)?i_(r).match(/%$/)?parseFloat(r)/100*t:parseFloat(r):r==null?NaN:+r}function pt(r,t,e){return t==null&&(t=10),t=Math.min(Math.max(0,t),Pd),r=(+r).toFixed(t),e?r:+r}function Le(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var t=1,e=0;e<15;e++,t*=10)if(Math.round(r*t)/t===r)return e}return n_(r)}function n_(r){var t=r.toString().toLowerCase(),e=t.indexOf("e"),i=e>0?+t.slice(e+1):0,n=e>0?e:t.length,a=t.indexOf("."),o=a<0?0:n-1-a;return Math.max(0,o-i)}function a_(r,t){var e=Math.log,i=Math.LN10,n=Math.floor(e(r[1]-r[0])/i),a=Math.round(e(Math.abs(t[1]-t[0]))/i),o=Math.min(Math.max(-n+a,0),20);return isFinite(o)?o:20}function RA(r,t){var e=ir(r,function(v,d){return v+(isNaN(d)?0:d)},0);if(e===0)return[];for(var i=Math.pow(10,t),n=V(r,function(v){return(isNaN(v)?0:v)/e*i*100}),a=i*100,o=V(n,function(v){return Math.floor(v)}),s=ir(o,function(v,d){return v+d},0),l=V(n,function(v,d){return v-o[d]});s<a;){for(var u=Number.NEGATIVE_INFINITY,f=null,h=0,c=l.length;h<c;++h)l[h]>u&&(u=l[h],f=h);++o[f],l[f]=0,++s}return V(o,function(v){return v/i})}function o_(r,t){var e=Math.max(Le(r),Le(t)),i=r+t;return e>Pd?i:pt(i,e)}function Rd(r){var t=Math.PI*2;return(r%t+t)%t}function to(r){return r>-eh&&r<eh}var s_=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Oe(r){if(r instanceof Date)return r;if(z(r)){var t=s_.exec(r);if(!t)return new Date(NaN);if(t[8]){var e=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(e-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,e,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function l_(r){return Math.pow(10,Cu(r))}function Cu(r){if(r===0)return 0;var t=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,t)>=10&&t++,t}function Ed(r,t){var e=Cu(r),i=Math.pow(10,e),n=r/i,a;return t?n<1.5?a=1:n<2.5?a=2:n<4?a=3:n<7?a=5:a=10:n<1?a=1:n<2?a=2:n<3?a=3:n<5?a=5:a=10,r=a*i,e>=-20?+r.toFixed(e<0?-e:0):r}function eo(r){var t=parseFloat(r);return t==r&&(t!==0||!z(r)||r.indexOf("x")<=0)?t:NaN}function u_(r){return!isNaN(eo(r))}function kd(){return Math.round(Math.random()*9)}function Od(r,t){return t===0?r:Od(t,r%t)}function ih(r,t){return r==null?t:t==null?r:r*t/Od(r,t)}function Ft(r){throw new Error(r)}function nh(r,t,e){return(t-r)*e+r}var Bd="series\0",f_="\0_ec_\0";function Rt(r){return r instanceof Array?r:r==null?[]:[r]}function ah(r,t,e){if(r){r[t]=r[t]||{},r.emphasis=r.emphasis||{},r.emphasis[t]=r.emphasis[t]||{};for(var i=0,n=e.length;i<n;i++){var a=e[i];!r.emphasis[t].hasOwnProperty(a)&&r[t].hasOwnProperty(a)&&(r.emphasis[t][a]=r[t][a])}}}var oh=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function Gn(r){return H(r)&&!N(r)&&!(r instanceof Date)?r.value:r}function h_(r){return H(r)&&!(r instanceof Array)}function v_(r,t,e){var i=e==="normalMerge",n=e==="replaceMerge",a=e==="replaceAll";r=r||[],t=(t||[]).slice();var o=X();A(t,function(l,u){if(!H(l)){t[u]=null;return}});var s=c_(r,o,e);return(i||n)&&d_(s,r,o,t),i&&p_(s,t),i||n?g_(s,t,n):a&&y_(s,t),m_(s),s}function c_(r,t,e){var i=[];if(e==="replaceAll")return i;for(var n=0;n<r.length;n++){var a=r[n];a&&a.id!=null&&t.set(a.id,n),i.push({existing:e==="replaceMerge"||Dn(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return i}function d_(r,t,e,i){A(i,function(n,a){if(!(!n||n.id==null)){var o=vn(n.id),s=e.get(o);if(s!=null){var l=r[s];ke(!l.newOption,'Duplicated option on id "'+o+'".'),l.newOption=n,l.existing=t[s],i[a]=null}}})}function p_(r,t){A(t,function(e,i){if(!(!e||e.name==null))for(var n=0;n<r.length;n++){var a=r[n].existing;if(!r[n].newOption&&a&&(a.id==null||e.id==null)&&!Dn(e)&&!Dn(a)&&Nd("name",a,e)){r[n].newOption=e,t[i]=null;return}}})}function g_(r,t,e){A(t,function(i){if(i){for(var n,a=0;(n=r[a])&&(n.newOption||Dn(n.existing)||n.existing&&i.id!=null&&!Nd("id",i,n.existing));)a++;n?(n.newOption=i,n.brandNew=e):r.push({newOption:i,brandNew:e,existing:null,keyInfo:null}),a++}})}function y_(r,t){A(t,function(e){r.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}function m_(r){var t=X();A(r,function(e){var i=e.existing;i&&t.set(i.id,e)}),A(r,function(e){var i=e.newOption;ke(!i||i.id==null||!t.get(i.id)||t.get(i.id)===e,"id duplicates: "+(i&&i.id)),i&&i.id!=null&&t.set(i.id,e),!e.keyInfo&&(e.keyInfo={})}),A(r,function(e,i){var n=e.existing,a=e.newOption,o=e.keyInfo;if(H(a)){if(o.name=a.name!=null?vn(a.name):n?n.name:Bd+i,n)o.id=vn(n.id);else if(a.id!=null)o.id=vn(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,e)}})}function Nd(r,t,e){var i=we(t[r],null),n=we(e[r],null);return i!=null&&n!=null&&i===n}function vn(r){return we(r,"")}function we(r,t){return r==null?t:z(r)?r:vt(r)||sl(r)?r+"":t}function Du(r){var t=r.name;return!!(t&&t.indexOf(Bd))}function Dn(r){return r&&r.id!=null&&vn(r.id).indexOf(f_)===0}function __(r,t,e){A(r,function(i){var n=i.newOption;H(n)&&(i.keyInfo.mainType=t,i.keyInfo.subType=S_(t,n,i.existing,e))})}function S_(r,t,e,i){var n=t.type?t.type:e?e.subType:i.determineSubType(r,t);return n}function Gr(r,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return N(t.dataIndex)?V(t.dataIndex,function(e){return r.indexOfRawIndex(e)}):r.indexOfRawIndex(t.dataIndex);if(t.name!=null)return N(t.name)?V(t.name,function(e){return r.indexOfName(e)}):r.indexOfName(t.name)}function yt(){var r="__ec_inner_"+w_++;return function(t){return t[r]||(t[r]={})}}var w_=kd();function hs(r,t,e){var i=Mu(t,e),n=i.mainTypeSpecified,a=i.queryOptionMap,o=i.others,s=o,l=e?e.defaultMainType:null;return!n&&l&&a.set(l,{}),a.each(function(u,f){var h=Vn(r,f,u,{useDefault:l===f,enableAll:e&&e.enableAll!=null?e.enableAll:!0,enableNone:e&&e.enableNone!=null?e.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function Mu(r,t){var e;if(z(r)){var i={};i[r+"Index"]=0,e=i}else e=r;var n=X(),a={},o=!1;return A(e,function(s,l){if(l==="dataIndex"||l==="dataIndexInside"){a[l]=s;return}var u=l.match(/^(\w+)(Index|Id|Name)$/)||[],f=u[1],h=(u[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&at(t.includeMainTypes,f)<0)){o=o||!!f;var c=n.get(f)||n.set(f,{});c[h]=s}}),{mainTypeSpecified:o,queryOptionMap:n,others:a}}var he={useDefault:!0,enableAll:!1,enableNone:!1};function Vn(r,t,e,i){i=i||he;var n=e.index,a=e.id,o=e.name,s={models:null,specified:n!=null||a!=null||o!=null};if(!s.specified){var l=void 0;return s.models=i.useDefault&&(l=r.getComponent(t))?[l]:[],s}return n==="none"||n===!1?(ke(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(n==="all"&&(ke(i.enableAll,'`"all"` is not a valid value on index option.'),n=a=o=null),s.models=r.queryComponents({mainType:t,index:n,id:a,name:o}),s)}function Fd(r,t,e){r.setAttribute?r.setAttribute(t,e):r[t]=e}function b_(r,t){return r.getAttribute?r.getAttribute(t):r[t]}function x_(r){return r==="auto"?Y.domSupported?"html":"richText":r||"html"}function T_(r,t,e,i,n){var a=t==null||t==="auto";if(i==null)return i;if(vt(i)){var o=nh(e||0,i,n);return pt(o,a?Math.max(Le(e||0),Le(i)):t)}else{if(z(i))return n<1?e:i;for(var s=[],l=e,u=i,f=Math.max(l?l.length:0,u.length),h=0;h<f;++h){var c=r.getDimensionInfo(h);if(c&&c.type==="ordinal")s[h]=(n<1&&l?l:u)[h];else{var v=l&&l[h]?l[h]:0,d=u[h],o=nh(v,d,n);s[h]=pt(o,a?Math.max(Le(v),Le(d)):t)}}return s}}var C_=".",dr="___EC__COMPONENT__CONTAINER___",zd="___EC__EXTENDED_CLASS___";function Se(r){var t={main:"",sub:""};if(r){var e=r.split(C_);t.main=e[0]||"",t.sub=e[1]||""}return t}function D_(r){ke(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function M_(r){return!!(r&&r[zd])}function Au(r,t){r.$constructor=r,r.extend=function(e){var i=this,n;return A_(i)?n=function(a){O(o,a);function o(){return a.apply(this,arguments)||this}return o}(i):(n=function(){(e.$constructor||i).apply(this,arguments)},xm(n,this)),k(n.prototype,e),n[zd]=!0,n.extend=this.extend,n.superCall=P_,n.superApply=R_,n.superClass=i,n}}function A_(r){return $(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function Hd(r,t){r.extend=t.extend}var L_=Math.round(Math.random()*10);function I_(r){var t=["__\0is_clz",L_++].join("_");r.prototype[t]=!0,r.isInstance=function(e){return!!(e&&e[t])}}function P_(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return this.superClass.prototype[t].apply(r,e)}function R_(r,t,e){return this.superClass.prototype[t].apply(r,e)}function So(r){var t={};r.registerClass=function(i){var n=i.type||i.prototype.type;if(n){D_(n),i.prototype.type=n;var a=Se(n);if(!a.sub)t[a.main]=i;else if(a.sub!==dr){var o=e(a);o[a.sub]=i}}return i},r.getClass=function(i,n,a){var o=t[i];if(o&&o[dr]&&(o=n?o[n]:null),a&&!o)throw new Error(n?"Component "+i+"."+(n||"")+" is used but not imported.":i+".type should be specified.");return o},r.getClassesByMainType=function(i){var n=Se(i),a=[],o=t[n.main];return o&&o[dr]?A(o,function(s,l){l!==dr&&a.push(s)}):a.push(o),a},r.hasClass=function(i){var n=Se(i);return!!t[n.main]},r.getAllClassMainTypes=function(){var i=[];return A(t,function(n,a){i.push(a)}),i},r.hasSubTypes=function(i){var n=Se(i),a=t[n.main];return a&&a[dr]};function e(i){var n=t[i.main];return(!n||!n[dr])&&(n=t[i.main]={},n[dr]=!0),n}}function Mn(r,t){for(var e=0;e<r.length;e++)r[e][1]||(r[e][1]=r[e][0]);return t=t||!1,function(i,n,a){for(var o={},s=0;s<r.length;s++){var l=r[s][1];if(!(n&&at(n,l)>=0||a&&at(a,l)<0)){var u=i.getShallow(l,t);u!=null&&(o[r[s][0]]=u)}}return o}}var E_=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],k_=Mn(E_),O_=function(){function r(){}return r.prototype.getAreaStyle=function(t,e){return k_(this,t,e)},r}(),Cl=new Hn(50);function B_(r){if(typeof r=="string"){var t=Cl.get(r);return t&&t.image}else return r}function Gd(r,t,e,i,n){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=Cl.get(r),o={hostEl:e,cb:i,cbPayload:n};return a?(t=a.image,!wo(t)&&a.pending.push(o)):(t=Li.loadImage(r,sh,sh),t.__zrImageSrc=r,Cl.put(r,t.__cachedImgObj={image:t,pending:[o]})),t}else return r;else return t}function sh(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],i=e.cb;i&&i(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function wo(r){return r&&r.width&&r.height}var vs=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function N_(r,t,e,i,n,a){if(!e){r.text="",r.isTruncated=!1;return}var o=(t+"").split(`
`);a=Vd(e,i,n,a);for(var s=!1,l={},u=0,f=o.length;u<f;u++)Wd(l,o[u],a),o[u]=l.textLine,s=s||l.isTruncated;r.text=o.join(`
`),r.isTruncated=s}function Vd(r,t,e,i){i=i||{};var n=k({},i);n.font=t,e=Z(e,"..."),n.maxIterations=Z(i.maxIterations,2);var a=n.minChar=Z(i.minChar,0);n.cnCharWidth=Yt("国",t);var o=n.ascCharWidth=Yt("a",t);n.placeholder=Z(i.placeholder,"");for(var s=r=Math.max(0,r-1),l=0;l<a&&s>=o;l++)s-=o;var u=Yt(e,t);return u>s&&(e="",u=0),s=r-u,n.ellipsis=e,n.ellipsisWidth=u,n.contentWidth=s,n.containerWidth=r,n}function Wd(r,t,e){var i=e.containerWidth,n=e.font,a=e.contentWidth;if(!i){r.textLine="",r.isTruncated=!1;return}var o=Yt(t,n);if(o<=i){r.textLine=t,r.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var l=s===0?F_(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,l),o=Yt(t,n)}t===""&&(t=e.placeholder),r.textLine=t,r.isTruncated=!0}function F_(r,t,e,i){for(var n=0,a=0,o=r.length;a<o&&n<t;a++){var s=r.charCodeAt(a);n+=0<=s&&s<=127?e:i}return a}function z_(r,t){r!=null&&(r+="");var e=t.overflow,i=t.padding,n=t.font,a=e==="truncate",o=xu(n),s=Z(t.lineHeight,o),l=!!t.backgroundColor,u=t.lineOverflow==="truncate",f=!1,h=t.width,c;h!=null&&(e==="break"||e==="breakAll")?c=r?Ud(r,t.font,h,e==="breakAll",0).lines:[]:c=r?r.split(`
`):[];var v=c.length*s,d=Z(t.height,v);if(v>d&&u){var y=Math.floor(d/s);f=f||c.length>y,c=c.slice(0,y)}if(r&&a&&h!=null)for(var p=Vd(h,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),g={},m=0;m<c.length;m++)Wd(g,c[m],p),c[m]=g.textLine,f=f||g.isTruncated;for(var _=d,S=0,m=0;m<c.length;m++)S=Math.max(Yt(c[m],n),S);h==null&&(h=S);var b=S;return i&&(_+=i[0]+i[2],b+=i[1]+i[3],h+=i[1]+i[3]),l&&(b=h),{lines:c,height:d,outerWidth:b,outerHeight:_,lineHeight:s,calculatedLineHeight:o,contentWidth:S,contentHeight:v,width:h,isTruncated:f}}var H_=function(){function r(){}return r}(),lh=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),G_=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return r}();function V_(r,t){var e=new G_;if(r!=null&&(r+=""),!r)return e;for(var i=t.width,n=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,s=vs.lastIndex=0,l;(l=vs.exec(r))!=null;){var u=l.index;u>s&&cs(e,r.substring(s,u),t,o),cs(e,l[2],t,o,l[1]),s=vs.lastIndex}s<r.length&&cs(e,r.substring(s,r.length),t,o);var f=[],h=0,c=0,v=t.padding,d=a==="truncate",y=t.lineOverflow==="truncate",p={};function g(W,q,K){W.width=q,W.lineHeight=K,h+=K,c=Math.max(c,q)}t:for(var m=0;m<e.lines.length;m++){for(var _=e.lines[m],S=0,b=0,w=0;w<_.tokens.length;w++){var x=_.tokens[w],C=x.styleName&&t.rich[x.styleName]||{},T=x.textPadding=C.padding,D=T?T[1]+T[3]:0,M=x.font=C.font||t.font;x.contentHeight=xu(M);var L=Z(C.height,x.contentHeight);if(x.innerHeight=L,T&&(L+=T[0]+T[2]),x.height=L,x.lineHeight=Pa(C.lineHeight,t.lineHeight,L),x.align=C&&C.align||t.align,x.verticalAlign=C&&C.verticalAlign||"middle",y&&n!=null&&h+x.lineHeight>n){var I=e.lines.length;w>0?(_.tokens=_.tokens.slice(0,w),g(_,b,S),e.lines=e.lines.slice(0,m+1)):e.lines=e.lines.slice(0,m),e.isTruncated=e.isTruncated||e.lines.length<I;break t}var P=C.width,R=P==null||P==="auto";if(typeof P=="string"&&P.charAt(P.length-1)==="%")x.percentWidth=P,f.push(x),x.contentWidth=Yt(x.text,M);else{if(R){var E=C.backgroundColor,G=E&&E.image;G&&(G=B_(G),wo(G)&&(x.width=Math.max(x.width,G.width*L/G.height)))}var B=d&&i!=null?i-b:null;B!=null&&B<x.width?!R||B<D?(x.text="",x.width=x.contentWidth=0):(N_(p,x.text,B-D,M,t.ellipsis,{minChar:t.truncateMinChar}),x.text=p.text,e.isTruncated=e.isTruncated||p.isTruncated,x.width=x.contentWidth=Yt(x.text,M)):x.contentWidth=Yt(x.text,M)}x.width+=D,b+=x.width,C&&(S=Math.max(S,x.lineHeight))}g(_,b,S)}e.outerWidth=e.width=Z(i,c),e.outerHeight=e.height=Z(n,h),e.contentHeight=h,e.contentWidth=c,v&&(e.outerWidth+=v[1]+v[3],e.outerHeight+=v[0]+v[2]);for(var m=0;m<f.length;m++){var x=f[m],F=x.percentWidth;x.width=parseInt(F,10)/100*e.width}return e}function cs(r,t,e,i,n){var a=t==="",o=n&&e.rich[n]||{},s=r.lines,l=o.font||e.font,u=!1,f,h;if(i){var c=o.padding,v=c?c[1]+c[3]:0;if(o.width!=null&&o.width!=="auto"){var d=Hr(o.width,i.width)+v;s.length>0&&d+i.accumWidth>i.width&&(f=t.split(`
`),u=!0),i.accumWidth=d}else{var y=Ud(t,l,i.width,i.breakAll,i.accumWidth);i.accumWidth=y.accumWidth+v,h=y.linesWidths,f=y.lines}}else f=t.split(`
`);for(var p=0;p<f.length;p++){var g=f[p],m=new H_;if(m.styleName=n,m.text=g,m.isLineHolder=!g&&!a,typeof o.width=="number"?m.width=o.width:m.width=h?h[p]:Yt(g,l),!p&&!u){var _=(s[s.length-1]||(s[0]=new lh)).tokens,S=_.length;S===1&&_[0].isLineHolder?_[0]=m:(g||!S||a)&&_.push(m)}else s.push(new lh([m]))}}function W_(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var U_=ir(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function $_(r){return W_(r)?!!U_[r]:!0}function Ud(r,t,e,i,n){for(var a=[],o=[],s="",l="",u=0,f=0,h=0;h<r.length;h++){var c=r.charAt(h);if(c===`
`){l&&(s+=l,f+=u),a.push(s),o.push(f),s="",l="",u=0,f=0;continue}var v=Yt(c,t),d=i?!1:!$_(c);if(a.length?f+v>e:n+f+v>e){f?(s||l)&&(d?(s||(s=l,l="",u=0,f=u),a.push(s),o.push(f-u),l+=c,u+=v,s="",f=u):(l&&(s+=l,l="",u=0),a.push(s),o.push(f),s=c,f=v)):d?(a.push(l),o.push(u),l=c,u=v):(a.push(c),o.push(v));continue}f+=v,d?(l+=c,u+=v):(l&&(s+=l,l="",u=0),s+=c)}return!a.length&&!s&&(s=r,l="",u=0),l&&(s+=l),s&&(a.push(s),o.push(f)),a.length===1&&(f+=n),{accumWidth:f,lines:a,linesWidths:o}}var Dl="__zr_style_"+Math.round(Math.random()*10),kr={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},bo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};kr[Dl]=!0;var uh=["z","z2","invisible"],Y_=["invisible"],X_=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var i=ht(e),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,i,n,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&Z_(this,e,i)||o&&!o[0]&&!o[3])return!1;if(n&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var l=this.parent;l;){if(l.ignore)return!1;l=l.parent}return!0},t.prototype.contain=function(e,i){return this.rectContain(e,i)},t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.rectContain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,l=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new et(0,0,0,0)),i?et.applyTransform(e,n,i):e.copy(n),(o||s||l)&&(e.width+=o*2+Math.abs(s),e.height+=o*2+Math.abs(l),e.x=Math.min(e.x,e.x+s-o),e.y=Math.min(e.y,e.y+l-o));var u=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-u),e.y=Math.floor(e.y-u),e.width=Math.ceil(e.width+1+u*2),e.height=Math.ceil(e.height+1+u*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new et(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,i){e!=="style"?r.prototype.attrKV.call(this,e,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(e,i){return typeof e=="string"?this.style[e]=i:k(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=rn,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&rn)},t.prototype.styleUpdated=function(){this.__dirty&=~rn},t.prototype.createStyle=function(e){return mo(kr,e)},t.prototype.useStyle=function(e){e[Dl]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[Dl]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,i,uh)},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var l=!(i&&a),u;if(i&&i.style?o?a?u=i.style:(u=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(u,i.style)):(u=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(u,i.style)):l&&(u=n.style),u)if(o){var f=this.style;if(this.style=this.createStyle(l?{}:f),l)for(var h=ht(f),c=0;c<h.length;c++){var v=h[c];v in u&&(u[v]=u[v],this.style[v]=f[v])}for(var d=ht(u),c=0;c<d.length;c++){var v=d[c];this.style[v]=this.style[v]}this._transitionState(e,{style:u},s,this.getAnimationStyleProps())}else this.useStyle(u);for(var y=this.__inHover?Y_:uh,c=0;c<y.length;c++){var v=y[c];i&&i[v]!=null?this[v]=i[v]:l&&n[v]!=null&&(this[v]=n[v])}},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(e,i){return k(e,i),e},t.prototype.getAnimationStyleProps=function(){return bo},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=$t|rn}(),t}(Ad),ds=new et(0,0,0,0),ps=new et(0,0,0,0);function Z_(r,t,e){return ds.copy(r.getBoundingRect()),r.transform&&ds.applyTransform(r.transform),ps.width=t,ps.height=e,!ds.intersect(ps)}const Wn=X_;var te=Math.min,ee=Math.max,gs=Math.sin,ys=Math.cos,pr=Math.PI*2,ra=Ii(),ia=Ii(),na=Ii();function fh(r,t,e,i,n,a){n[0]=te(r,e),n[1]=te(t,i),a[0]=ee(r,e),a[1]=ee(t,i)}var hh=[],vh=[];function q_(r,t,e,i,n,a,o,s,l,u){var f=md,h=gt,c=f(r,e,n,o,hh);l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0;for(var v=0;v<c;v++){var d=h(r,e,n,o,hh[v]);l[0]=te(d,l[0]),u[0]=ee(d,u[0])}c=f(t,i,a,s,vh);for(var v=0;v<c;v++){var y=h(t,i,a,s,vh[v]);l[1]=te(y,l[1]),u[1]=ee(y,u[1])}l[0]=te(r,l[0]),u[0]=ee(r,u[0]),l[0]=te(o,l[0]),u[0]=ee(o,u[0]),l[1]=te(t,l[1]),u[1]=ee(t,u[1]),l[1]=te(s,l[1]),u[1]=ee(s,u[1])}function K_(r,t,e,i,n,a,o,s){var l=_d,u=Dt,f=ee(te(l(r,e,n),1),0),h=ee(te(l(t,i,a),1),0),c=u(r,e,n,f),v=u(t,i,a,h);o[0]=te(r,n,c),o[1]=te(t,a,v),s[0]=ee(r,n,c),s[1]=ee(t,a,v)}function Q_(r,t,e,i,n,a,o,s,l){var u=di,f=pi,h=Math.abs(n-a);if(h%pr<1e-4&&h>1e-4){s[0]=r-e,s[1]=t-i,l[0]=r+e,l[1]=t+i;return}if(ra[0]=ys(n)*e+r,ra[1]=gs(n)*i+t,ia[0]=ys(a)*e+r,ia[1]=gs(a)*i+t,u(s,ra,ia),f(l,ra,ia),n=n%pr,n<0&&(n=n+pr),a=a%pr,a<0&&(a=a+pr),n>a&&!o?a+=pr:n<a&&o&&(n+=pr),o){var c=a;a=n,n=c}for(var v=0;v<a;v+=Math.PI/2)v>n&&(na[0]=ys(v)*e+r,na[1]=gs(v)*i+t,u(s,na,s),f(l,na,l))}var j={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},gr=[],yr=[],de=[],ze=[],pe=[],ge=[],ms=Math.min,_s=Math.max,mr=Math.cos,_r=Math.sin,De=Math.abs,Ml=Math.PI,qe=Ml*2,Ss=typeof Float32Array<"u",Hi=[];function ws(r){var t=Math.round(r/Ml*1e8)/1e8;return t%2*Ml}function J_(r,t){var e=ws(r[0]);e<0&&(e+=qe);var i=e-r[0],n=r[1];n+=i,!t&&n-e>=qe?n=e+qe:t&&e-n>=qe?n=e-qe:!t&&e>n?n=e+(qe-ws(e-n)):t&&e<n&&(n=e-(qe-ws(n-e))),r[0]=e,r[1]=n}var Vr=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,i){i=i||0,i>0&&(this._ux=De(i/ja/t)||0,this._uy=De(i/ja/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(j.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var i=De(t-this._xi),n=De(e-this._yi),a=i>this._ux||n>this._uy;if(this.addData(j.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=i*i+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},r.prototype.bezierCurveTo=function(t,e,i,n,a,o){return this._drawPendingPt(),this.addData(j.C,t,e,i,n,a,o),this._ctx&&this._ctx.bezierCurveTo(t,e,i,n,a,o),this._xi=a,this._yi=o,this},r.prototype.quadraticCurveTo=function(t,e,i,n){return this._drawPendingPt(),this.addData(j.Q,t,e,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,i,n),this._xi=i,this._yi=n,this},r.prototype.arc=function(t,e,i,n,a,o){this._drawPendingPt(),Hi[0]=n,Hi[1]=a,J_(Hi,o),n=Hi[0],a=Hi[1];var s=a-n;return this.addData(j.A,t,e,i,i,n,s,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,a,o),this._xi=mr(a)*i+t,this._yi=_r(a)*i+e,this},r.prototype.arcTo=function(t,e,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,i,n,a),this},r.prototype.rect=function(t,e,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,i,n),this.addData(j.R,t,e,i,n),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(j.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&t.closePath(),this._xi=e,this._yi=i,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&Ss&&(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,a=0;a<e;a++)i+=t[a].len();Ss&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<e;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[n++]=o[s];this._len=n},r.prototype.addData=function(t,e,i,n,a,o,s,l,u){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,Ss&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){de[0]=de[1]=pe[0]=pe[1]=Number.MAX_VALUE,ze[0]=ze[1]=ge[0]=ge[1]=-Number.MAX_VALUE;var t=this.data,e=0,i=0,n=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],l=o===1;switch(l&&(e=t[o],i=t[o+1],n=e,a=i),s){case j.M:e=n=t[o++],i=a=t[o++],pe[0]=n,pe[1]=a,ge[0]=n,ge[1]=a;break;case j.L:fh(e,i,t[o],t[o+1],pe,ge),e=t[o++],i=t[o++];break;case j.C:q_(e,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],pe,ge),e=t[o++],i=t[o++];break;case j.Q:K_(e,i,t[o++],t[o++],t[o],t[o+1],pe,ge),e=t[o++],i=t[o++];break;case j.A:var u=t[o++],f=t[o++],h=t[o++],c=t[o++],v=t[o++],d=t[o++]+v;o+=1;var y=!t[o++];l&&(n=mr(v)*h+u,a=_r(v)*c+f),Q_(u,f,h,c,v,d,y,pe,ge),e=mr(d)*h+u,i=_r(d)*c+f;break;case j.R:n=e=t[o++],a=i=t[o++];var p=t[o++],g=t[o++];fh(n,a,n+p,a+g,pe,ge);break;case j.Z:e=n,i=a;break}di(de,de,pe),pi(ze,ze,ge)}return o===0&&(de[0]=de[1]=ze[0]=ze[1]=0),new et(de[0],de[1],ze[0]-de[0],ze[1]-de[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,i=this._ux,n=this._uy,a=0,o=0,s=0,l=0;this._pathSegLen||(this._pathSegLen=[]);for(var u=this._pathSegLen,f=0,h=0,c=0;c<e;){var v=t[c++],d=c===1;d&&(a=t[c],o=t[c+1],s=a,l=o);var y=-1;switch(v){case j.M:a=s=t[c++],o=l=t[c++];break;case j.L:{var p=t[c++],g=t[c++],m=p-a,_=g-o;(De(m)>i||De(_)>n||c===e-1)&&(y=Math.sqrt(m*m+_*_),a=p,o=g);break}case j.C:{var S=t[c++],b=t[c++],p=t[c++],g=t[c++],w=t[c++],x=t[c++];y=g0(a,o,S,b,p,g,w,x,10),a=w,o=x;break}case j.Q:{var S=t[c++],b=t[c++],p=t[c++],g=t[c++];y=_0(a,o,S,b,p,g,10),a=p,o=g;break}case j.A:var C=t[c++],T=t[c++],D=t[c++],M=t[c++],L=t[c++],I=t[c++],P=I+L;c+=1,d&&(s=mr(L)*D+C,l=_r(L)*M+T),y=_s(D,M)*ms(qe,Math.abs(I)),a=mr(P)*D+C,o=_r(P)*M+T;break;case j.R:{s=a=t[c++],l=o=t[c++];var R=t[c++],E=t[c++];y=R*2+E*2;break}case j.Z:{var m=s-a,_=l-o;y=Math.sqrt(m*m+_*_),a=s,o=l;break}}y>=0&&(u[h++]=y,f+=y)}return this._pathLen=f,f},r.prototype.rebuildPath=function(t,e){var i=this.data,n=this._ux,a=this._uy,o=this._len,s,l,u,f,h,c,v=e<1,d,y,p=0,g=0,m,_=0,S,b;if(!(v&&(this._pathSegLen||this._calculateLength(),d=this._pathSegLen,y=this._pathLen,m=e*y,!m)))t:for(var w=0;w<o;){var x=i[w++],C=w===1;switch(C&&(u=i[w],f=i[w+1],s=u,l=f),x!==j.L&&_>0&&(t.lineTo(S,b),_=0),x){case j.M:s=u=i[w++],l=f=i[w++],t.moveTo(u,f);break;case j.L:{h=i[w++],c=i[w++];var T=De(h-u),D=De(c-f);if(T>n||D>a){if(v){var M=d[g++];if(p+M>m){var L=(m-p)/M;t.lineTo(u*(1-L)+h*L,f*(1-L)+c*L);break t}p+=M}t.lineTo(h,c),u=h,f=c,_=0}else{var I=T*T+D*D;I>_&&(S=h,b=c,_=I)}break}case j.C:{var P=i[w++],R=i[w++],E=i[w++],G=i[w++],B=i[w++],F=i[w++];if(v){var M=d[g++];if(p+M>m){var L=(m-p)/M;Ka(u,P,E,B,L,gr),Ka(f,R,G,F,L,yr),t.bezierCurveTo(gr[1],yr[1],gr[2],yr[2],gr[3],yr[3]);break t}p+=M}t.bezierCurveTo(P,R,E,G,B,F),u=B,f=F;break}case j.Q:{var P=i[w++],R=i[w++],E=i[w++],G=i[w++];if(v){var M=d[g++];if(p+M>m){var L=(m-p)/M;Qa(u,P,E,L,gr),Qa(f,R,G,L,yr),t.quadraticCurveTo(gr[1],yr[1],gr[2],yr[2]);break t}p+=M}t.quadraticCurveTo(P,R,E,G),u=E,f=G;break}case j.A:var W=i[w++],q=i[w++],K=i[w++],nt=i[w++],lt=i[w++],ct=i[w++],ae=i[w++],or=!i[w++],qr=K>nt?K:nt,Ut=De(K-nt)>.001,_t=lt+ct,U=!1;if(v){var M=d[g++];p+M>m&&(_t=lt+ct*(m-p)/M,U=!0),p+=M}if(Ut&&t.ellipse?t.ellipse(W,q,K,nt,ae,lt,_t,or):t.arc(W,q,qr,lt,_t,or),U)break t;C&&(s=mr(lt)*K+W,l=_r(lt)*nt+q),u=mr(_t)*K+W,f=_r(_t)*nt+q;break;case j.R:s=u=i[w],l=f=i[w+1],h=i[w++],c=i[w++];var Q=i[w++],sr=i[w++];if(v){var M=d[g++];if(p+M>m){var Lt=m-p;t.moveTo(h,c),t.lineTo(h+ms(Lt,Q),c),Lt-=Q,Lt>0&&t.lineTo(h+Q,c+ms(Lt,sr)),Lt-=sr,Lt>0&&t.lineTo(h+_s(Q-Lt,0),c+sr),Lt-=Q,Lt>0&&t.lineTo(h,c+_s(sr-Lt,0));break t}p+=M}t.rect(h,c,Q,sr);break;case j.Z:if(v){var M=d[g++];if(p+M>m){var L=(m-p)/M;t.lineTo(u*(1-L)+s*L,f*(1-L)+l*L);break t}p+=M}t.closePath(),u=s,f=l}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=j,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();function ti(r,t,e,i,n,a,o){if(n===0)return!1;var s=n,l=0,u=r;if(o>t+s&&o>i+s||o<t-s&&o<i-s||a>r+s&&a>e+s||a<r-s&&a<e-s)return!1;if(r!==e)l=(t-i)/(r-e),u=(r*i-e*t)/(r-e);else return Math.abs(a-r)<=s/2;var f=l*a-o+u,h=f*f/(l*l+1);return h<=s/2*s/2}function j_(r,t,e,i,n,a,o,s,l,u,f){if(l===0)return!1;var h=l;if(f>t+h&&f>i+h&&f>a+h&&f>s+h||f<t-h&&f<i-h&&f<a-h&&f<s-h||u>r+h&&u>e+h&&u>n+h&&u>o+h||u<r-h&&u<e-h&&u<n-h&&u<o-h)return!1;var c=p0(r,t,e,i,n,a,o,s,u,f,null);return c<=h/2}function t1(r,t,e,i,n,a,o,s,l){if(o===0)return!1;var u=o;if(l>t+u&&l>i+u&&l>a+u||l<t-u&&l<i-u&&l<a-u||s>r+u&&s>e+u&&s>n+u||s<r-u&&s<e-u&&s<n-u)return!1;var f=m0(r,t,e,i,n,a,s,l,null);return f<=u/2}var ch=Math.PI*2;function aa(r){return r%=ch,r<0&&(r+=ch),r}var Gi=Math.PI*2;function e1(r,t,e,i,n,a,o,s,l){if(o===0)return!1;var u=o;s-=r,l-=t;var f=Math.sqrt(s*s+l*l);if(f-u>e||f+u<e)return!1;if(Math.abs(i-n)%Gi<1e-4)return!0;if(a){var h=i;i=aa(n),n=aa(h)}else i=aa(i),n=aa(n);i>n&&(n+=Gi);var c=Math.atan2(l,s);return c<0&&(c+=Gi),c>=i&&c<=n||c+Gi>=i&&c+Gi<=n}function Sr(r,t,e,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var o=(a-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var l=o*(e-r)+r;return l===n?1/0:l>n?s:0}var He=Vr.CMD,wr=Math.PI*2,r1=1e-4;function i1(r,t){return Math.abs(r-t)<r1}var It=[-1,-1,-1],Jt=[-1,-1];function n1(){var r=Jt[0];Jt[0]=Jt[1],Jt[1]=r}function a1(r,t,e,i,n,a,o,s,l,u){if(u>t&&u>i&&u>a&&u>s||u<t&&u<i&&u<a&&u<s)return 0;var f=qa(t,i,a,s,u,It);if(f===0)return 0;for(var h=0,c=-1,v=void 0,d=void 0,y=0;y<f;y++){var p=It[y],g=p===0||p===1?.5:1,m=gt(r,e,n,o,p);m<l||(c<0&&(c=md(t,i,a,s,Jt),Jt[1]<Jt[0]&&c>1&&n1(),v=gt(t,i,a,s,Jt[0]),c>1&&(d=gt(t,i,a,s,Jt[1]))),c===2?p<Jt[0]?h+=v<t?g:-g:p<Jt[1]?h+=d<v?g:-g:h+=s<d?g:-g:p<Jt[0]?h+=v<t?g:-g:h+=s<v?g:-g)}return h}function o1(r,t,e,i,n,a,o,s){if(s>t&&s>i&&s>a||s<t&&s<i&&s<a)return 0;var l=y0(t,i,a,s,It);if(l===0)return 0;var u=_d(t,i,a);if(u>=0&&u<=1){for(var f=0,h=Dt(t,i,a,u),c=0;c<l;c++){var v=It[c]===0||It[c]===1?.5:1,d=Dt(r,e,n,It[c]);d<o||(It[c]<u?f+=h<t?v:-v:f+=a<h?v:-v)}return f}else{var v=It[0]===0||It[0]===1?.5:1,d=Dt(r,e,n,It[0]);return d<o?0:a<t?v:-v}}function s1(r,t,e,i,n,a,o,s){if(s-=t,s>e||s<-e)return 0;var l=Math.sqrt(e*e-s*s);It[0]=-l,It[1]=l;var u=Math.abs(i-n);if(u<1e-4)return 0;if(u>=wr-1e-4){i=0,n=wr;var f=a?1:-1;return o>=It[0]+r&&o<=It[1]+r?f:0}if(i>n){var h=i;i=n,n=h}i<0&&(i+=wr,n+=wr);for(var c=0,v=0;v<2;v++){var d=It[v];if(d+r>o){var y=Math.atan2(s,d),f=a?1:-1;y<0&&(y=wr+y),(y>=i&&y<=n||y+wr>=i&&y+wr<=n)&&(y>Math.PI/2&&y<Math.PI*1.5&&(f=-f),c+=f)}}return c}function $d(r,t,e,i,n){for(var a=r.data,o=r.len(),s=0,l=0,u=0,f=0,h=0,c,v,d=0;d<o;){var y=a[d++],p=d===1;switch(y===He.M&&d>1&&(e||(s+=Sr(l,u,f,h,i,n))),p&&(l=a[d],u=a[d+1],f=l,h=u),y){case He.M:f=a[d++],h=a[d++],l=f,u=h;break;case He.L:if(e){if(ti(l,u,a[d],a[d+1],t,i,n))return!0}else s+=Sr(l,u,a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case He.C:if(e){if(j_(l,u,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=a1(l,u,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case He.Q:if(e){if(t1(l,u,a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=o1(l,u,a[d++],a[d++],a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case He.A:var g=a[d++],m=a[d++],_=a[d++],S=a[d++],b=a[d++],w=a[d++];d+=1;var x=!!(1-a[d++]);c=Math.cos(b)*_+g,v=Math.sin(b)*S+m,p?(f=c,h=v):s+=Sr(l,u,c,v,i,n);var C=(i-g)*S/_+g;if(e){if(e1(g,m,S,b,b+w,x,t,C,n))return!0}else s+=s1(g,m,S,b,b+w,x,C,n);l=Math.cos(b+w)*_+g,u=Math.sin(b+w)*S+m;break;case He.R:f=l=a[d++],h=u=a[d++];var T=a[d++],D=a[d++];if(c=f+T,v=h+D,e){if(ti(f,h,c,h,t,i,n)||ti(c,h,c,v,t,i,n)||ti(c,v,f,v,t,i,n)||ti(f,v,f,h,t,i,n))return!0}else s+=Sr(c,h,c,v,i,n),s+=Sr(f,v,f,h,i,n);break;case He.Z:if(e){if(ti(l,u,f,h,t,i,n))return!0}else s+=Sr(l,u,f,h,i,n);l=f,u=h;break}}return!e&&!i1(u,h)&&(s+=Sr(l,u,f,h,i,n)||0),s!==0}function l1(r,t,e){return $d(r,0,!1,t,e)}function u1(r,t,e,i){return $d(r,t,!0,e,i)}var Yd=it({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},kr),f1={style:it({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},bo.style)},bs=Cn.concat(["invisible","culling","z","z2","zlevel","parent"]),h1=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(l){e.buildPath(l,e.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<bs.length;++s)n[bs[s]]=this[bs[s]];n.__dirty|=$t}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var i=ht(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var o=i[a],s=e[o];o==="style"?this.style?k(this.style,s):this.useStyle(s):o==="shape"?k(this.shape,s):r.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(z(e)){var i=Ja(e,0);return i>.5?xl:i>.2?U0:Tl}else if(e)return Tl}return xl},t.prototype.getInsideTextStroke=function(e){var i=this.style.fill;if(z(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),o=Ja(e,0)<bl;if(a===o)return i}},t.prototype.buildPath=function(e,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=~vi},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new Vr(!1)},t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return!(i==null||i==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,i=this.style,n=!e;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&vi)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),e=o.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||n){s.copy(e);var l=i.strokeNoScale?this.getLineScale():1,u=i.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;u=Math.max(u,f??4)}l>1e-10&&(s.width+=u/l,s.height+=u/l,s.x-=u/l/2,s.y-=u/l/2)}return s}return e},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect(),o=this.style;if(e=n[0],i=n[1],a.contain(e,i)){var s=this.path;if(this.hasStroke()){var l=o.lineWidth,u=o.strokeNoScale?this.getLineScale():1;if(u>1e-10&&(this.hasFill()||(l=Math.max(l,this.strokeContainThreshold)),u1(s,l/u,e,i)))return!0}if(this.hasFill())return l1(s,e,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=vi,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,i){e==="shape"?this.setShape(i):r.prototype.attrKV.call(this,e,i)},t.prototype.setShape=function(e,i){var n=this.shape;return n||(n=this.shape={}),typeof e=="string"?n[e]=i:k(n,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&vi)},t.prototype.createStyle=function(e){return mo(Yd,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.shape&&!i.shape&&(i.shape=k({},this.shape))},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var l=!(i&&a),u;if(i&&i.shape?o?a?u=i.shape:(u=k({},n.shape),k(u,i.shape)):(u=k({},a?this.shape:n.shape),k(u,i.shape)):l&&(u=n.shape),u)if(o){this.shape=k({},this.shape);for(var f={},h=ht(u),c=0;c<h.length;c++){var v=h[c];typeof u[v]=="object"?this.shape[v]=u[v]:f[v]=u[v]}this._transitionState(e,{shape:f},s)}else this.shape=u,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return f1},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var i=function(a){O(o,a);function o(s){var l=a.call(this,s)||this;return e.init&&e.init.call(l,s),l}return o.prototype.getDefaultStyle=function(){return J(e.style)},o.prototype.getDefaultShape=function(){return J(e.shape)},o}(t);for(var n in e)typeof e[n]=="function"&&(i.prototype[n]=e[n]);return i},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=$t|rn|vi}(),t}(Wn);const st=h1;var v1=it({strokeFirst:!0,font:Fr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Yd),Xd=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return i!=null&&i!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(e){return mo(v1,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var i=e.text;i!=null?i+="":i="";var n=bu(i,e.font,e.textAlign,e.textBaseline);if(n.x+=e.x||0,n.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(Wn);Xd.prototype.type="tspan";const Al=Xd;var c1=it({x:0,y:0},kr),d1={style:it({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},bo.style)};function p1(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var Zd=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return mo(c1,e)},t.prototype._getSize=function(e){var i=this.style,n=i[e];if(n!=null)return n;var a=p1(i.image)?i.image:this.__image;if(!a)return 0;var o=e==="width"?"height":"width",s=i[o];return s==null?a[e]:a[e]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return d1},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new et(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(Wn);Zd.prototype.type="image";const Yr=Zd;function g1(r,t){var e=t.x,i=t.y,n=t.width,a=t.height,o=t.r,s,l,u,f;n<0&&(e=e+n,n=-n),a<0&&(i=i+a,a=-a),typeof o=="number"?s=l=u=f=o:o instanceof Array?o.length===1?s=l=u=f=o[0]:o.length===2?(s=u=o[0],l=f=o[1]):o.length===3?(s=o[0],l=f=o[1],u=o[2]):(s=o[0],l=o[1],u=o[2],f=o[3]):s=l=u=f=0;var h;s+l>n&&(h=s+l,s*=n/h,l*=n/h),u+f>n&&(h=u+f,u*=n/h,f*=n/h),l+u>a&&(h=l+u,l*=a/h,u*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),r.moveTo(e+s,i),r.lineTo(e+n-l,i),l!==0&&r.arc(e+n-l,i+l,l,-Math.PI/2,0),r.lineTo(e+n,i+a-u),u!==0&&r.arc(e+n-u,i+a-u,u,0,Math.PI/2),r.lineTo(e+f,i+a),f!==0&&r.arc(e+f,i+a-f,f,Math.PI/2,Math.PI),r.lineTo(e,i+s),s!==0&&r.arc(e+s,i+s,s,Math.PI,Math.PI*1.5)}var yi=Math.round;function qd(r,t,e){if(t){var i=t.x1,n=t.x2,a=t.y1,o=t.y2;r.x1=i,r.x2=n,r.y1=a,r.y2=o;var s=e&&e.lineWidth;return s&&(yi(i*2)===yi(n*2)&&(r.x1=r.x2=Pr(i,s,!0)),yi(a*2)===yi(o*2)&&(r.y1=r.y2=Pr(a,s,!0))),r}}function Kd(r,t,e){if(t){var i=t.x,n=t.y,a=t.width,o=t.height;r.x=i,r.y=n,r.width=a,r.height=o;var s=e&&e.lineWidth;return s&&(r.x=Pr(i,s,!0),r.y=Pr(n,s,!0),r.width=Math.max(Pr(i+a,s,!1)-r.x,a===0?0:1),r.height=Math.max(Pr(n+o,s,!1)-r.y,o===0?0:1)),r}}function Pr(r,t,e){if(!t)return r;var i=yi(r*2);return(i+yi(t))%2===0?i/2:(i+(e?1:-1))/2}var y1=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),m1={},Qd=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new y1},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var l=Kd(m1,i,this.style);n=l.x,a=l.y,o=l.width,s=l.height,l.r=i.r,i=l}else n=i.x,a=i.y,o=i.width,s=i.height;i.r?g1(e,i):e.rect(n,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(st);Qd.prototype.type="rect";const bt=Qd;var dh={fill:"#000"},ph=2,_1={style:it({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},bo.style)},Jd=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=dh,i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var i=this._children[e];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var i=this.innerTransformable;return i?i.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,T1(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new et(0,0,0,0),i=this._children,n=[],a=null,o=0;o<i.length;o++){var s=i[o],l=s.getBoundingRect(),u=s.getLocalTransform(n);u?(e.copy(l),e.applyTransform(u),a=a||e.clone(),a.union(e)):(a=a||l.clone(),a.union(l))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||dh},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,i){if(!i)return e;var n=i.rich,a=e.rich||n&&{};return k(e,i),n&&a?(this._mergeRich(a,n),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,i){for(var n=ht(i),a=0;a<n.length;a++){var o=n[a];e[o]=e[o]||{},k(e[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return _1},t.prototype._getOrCreateChild=function(e){var i=this._children[this._childCursor];return(!i||!(i instanceof e))&&(i=new e),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var e=this.style,i=e.font||Fr,n=e.padding,a=bh(e),o=z_(a,e),s=xs(e),l=!!e.backgroundColor,u=o.outerHeight,f=o.outerWidth,h=o.contentWidth,c=o.lines,v=o.lineHeight,d=this._defaultStyle;this.isTruncated=!!o.isTruncated;var y=e.x||0,p=e.y||0,g=e.align||d.align||"left",m=e.verticalAlign||d.verticalAlign||"top",_=y,S=ci(p,o.contentHeight,m);if(s||n){var b=an(y,f,g),w=ci(p,u,m);s&&this._renderBackground(e,e,b,w,f,u)}S+=v/2,n&&(_=wh(y,g,n),m==="top"?S+=n[0]:m==="bottom"&&(S-=n[2]));for(var x=0,C=!1,T=Sh("fill"in e?e.fill:(C=!0,d.fill)),D=_h("stroke"in e?e.stroke:!l&&(!d.autoStroke||C)?(x=ph,d.stroke):null),M=e.textShadowBlur>0,L=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),I=o.calculatedLineHeight,P=0;P<c.length;P++){var R=this._getOrCreateChild(Al),E=R.createStyle();R.useStyle(E),E.text=c[P],E.x=_,E.y=S,g&&(E.textAlign=g),E.textBaseline="middle",E.opacity=e.opacity,E.strokeFirst=!0,M&&(E.shadowBlur=e.textShadowBlur||0,E.shadowColor=e.textShadowColor||"transparent",E.shadowOffsetX=e.textShadowOffsetX||0,E.shadowOffsetY=e.textShadowOffsetY||0),E.stroke=D,E.fill=T,D&&(E.lineWidth=e.lineWidth||x,E.lineDash=e.lineDash,E.lineDashOffset=e.lineDashOffset||0),E.font=i,yh(E,e),S+=v,L&&R.setBoundingRect(new et(an(E.x,h,E.textAlign),ci(E.y,I,E.textBaseline),h,I))}},t.prototype._updateRichTexts=function(){var e=this.style,i=bh(e),n=V_(i,e),a=n.width,o=n.outerWidth,s=n.outerHeight,l=e.padding,u=e.x||0,f=e.y||0,h=this._defaultStyle,c=e.align||h.align,v=e.verticalAlign||h.verticalAlign;this.isTruncated=!!n.isTruncated;var d=an(u,o,c),y=ci(f,s,v),p=d,g=y;l&&(p+=l[3],g+=l[0]);var m=p+a;xs(e)&&this._renderBackground(e,e,d,y,o,s);for(var _=!!e.backgroundColor,S=0;S<n.lines.length;S++){for(var b=n.lines[S],w=b.tokens,x=w.length,C=b.lineHeight,T=b.width,D=0,M=p,L=m,I=x-1,P=void 0;D<x&&(P=w[D],!P.align||P.align==="left");)this._placeToken(P,e,C,g,M,"left",_),T-=P.width,M+=P.width,D++;for(;I>=0&&(P=w[I],P.align==="right");)this._placeToken(P,e,C,g,L,"right",_),T-=P.width,L-=P.width,I--;for(M+=(a-(M-p)-(m-L)-T)/2;D<=I;)P=w[D],this._placeToken(P,e,C,g,M+P.width/2,"center",_),M+=P.width,D++;g+=C}},t.prototype._placeToken=function(e,i,n,a,o,s,l){var u=i.rich[e.styleName]||{};u.text=e.text;var f=e.verticalAlign,h=a+n/2;f==="top"?h=a+e.height/2:f==="bottom"&&(h=a+n-e.height/2);var c=!e.isLineHolder&&xs(u);c&&this._renderBackground(u,i,s==="right"?o-e.width:s==="center"?o-e.width/2:o,h-e.height/2,e.width,e.height);var v=!!u.backgroundColor,d=e.textPadding;d&&(o=wh(o,s,d),h-=e.height/2-d[0]-e.innerHeight/2);var y=this._getOrCreateChild(Al),p=y.createStyle();y.useStyle(p);var g=this._defaultStyle,m=!1,_=0,S=Sh("fill"in u?u.fill:"fill"in i?i.fill:(m=!0,g.fill)),b=_h("stroke"in u?u.stroke:"stroke"in i?i.stroke:!v&&!l&&(!g.autoStroke||m)?(_=ph,g.stroke):null),w=u.textShadowBlur>0||i.textShadowBlur>0;p.text=e.text,p.x=o,p.y=h,w&&(p.shadowBlur=u.textShadowBlur||i.textShadowBlur||0,p.shadowColor=u.textShadowColor||i.textShadowColor||"transparent",p.shadowOffsetX=u.textShadowOffsetX||i.textShadowOffsetX||0,p.shadowOffsetY=u.textShadowOffsetY||i.textShadowOffsetY||0),p.textAlign=s,p.textBaseline="middle",p.font=e.font||Fr,p.opacity=Pa(u.opacity,i.opacity,1),yh(p,u),b&&(p.lineWidth=Pa(u.lineWidth,i.lineWidth,_),p.lineDash=Z(u.lineDash,i.lineDash),p.lineDashOffset=i.lineDashOffset||0,p.stroke=b),S&&(p.fill=S);var x=e.contentWidth,C=e.contentHeight;y.setBoundingRect(new et(an(p.x,x,p.textAlign),ci(p.y,C,p.textBaseline),x,C))},t.prototype._renderBackground=function(e,i,n,a,o,s){var l=e.backgroundColor,u=e.borderWidth,f=e.borderColor,h=l&&l.image,c=l&&!h,v=e.borderRadius,d=this,y,p;if(c||e.lineHeight||u&&f){y=this._getOrCreateChild(bt),y.useStyle(y.createStyle()),y.style.fill=null;var g=y.shape;g.x=n,g.y=a,g.width=o,g.height=s,g.r=v,y.dirtyShape()}if(c){var m=y.style;m.fill=l||null,m.fillOpacity=Z(e.fillOpacity,1)}else if(h){p=this._getOrCreateChild(Yr),p.onload=function(){d.dirtyStyle()};var _=p.style;_.image=l.image,_.x=n,_.y=a,_.width=o,_.height=s}if(u&&f){var m=y.style;m.lineWidth=u,m.stroke=f,m.strokeOpacity=Z(e.strokeOpacity,1),m.lineDash=e.borderDash,m.lineDashOffset=e.borderDashOffset||0,y.strokeContainThreshold=0,y.hasFill()&&y.hasStroke()&&(m.strokeFirst=!0,m.lineWidth*=2)}var S=(y||p).style;S.shadowBlur=e.shadowBlur||0,S.shadowColor=e.shadowColor||"transparent",S.shadowOffsetX=e.shadowOffsetX||0,S.shadowOffsetY=e.shadowOffsetY||0,S.opacity=Pa(e.opacity,i.opacity,1)},t.makeFont=function(e){var i="";return x1(e)&&(i=[e.fontStyle,e.fontWeight,b1(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),i&&_e(i)||e.textFont||e.font},t}(Wn),S1={left:!0,right:1,center:1},w1={top:1,bottom:1,middle:1},gh=["fontStyle","fontWeight","fontSize","fontFamily"];function b1(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?hu+"px":r+"px"}function yh(r,t){for(var e=0;e<gh.length;e++){var i=gh[e],n=t[i];n!=null&&(r[i]=n)}}function x1(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function T1(r){return mh(r),A(r.rich,mh),r}function mh(r){if(r){r.font=Jd.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||S1[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||w1[e]?e:"top";var i=r.padding;i&&(r.padding=nd(r.padding))}}function _h(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function Sh(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function wh(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function bh(r){var t=r.text;return t!=null&&(t+=""),t}function xs(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}const kt=Jd;var rt=yt(),C1=function(r,t,e,i){if(i){var n=rt(i);n.dataIndex=e,n.dataType=t,n.seriesIndex=r,n.ssrType="chart",i.type==="group"&&i.traverse(function(a){var o=rt(a);o.seriesIndex=r,o.dataIndex=e,o.dataType=t,o.ssrType="chart"})}},xh=1,Th={},jd=yt(),Lu=yt(),Iu=0,xo=1,To=2,be=["emphasis","blur","select"],Ch=["normal","emphasis","blur","select"],D1=10,M1=9,Or="highlight",za="downplay",cn="select",Ha="unselect",dn="toggleSelect";function ei(r){return r!=null&&r!=="none"}function Co(r,t,e){r.onHoverStateChange&&(r.hoverState||0)!==e&&r.onHoverStateChange(t),r.hoverState=e}function tp(r){Co(r,"emphasis",To)}function ep(r){r.hoverState===To&&Co(r,"normal",Iu)}function Pu(r){Co(r,"blur",xo)}function rp(r){r.hoverState===xo&&Co(r,"normal",Iu)}function A1(r){r.selected=!0}function L1(r){r.selected=!1}function Dh(r,t,e){t(r,e)}function Ne(r,t,e){Dh(r,t,e),r.isGroup&&r.traverse(function(i){Dh(i,t,e)})}function Mh(r,t){switch(t){case"emphasis":r.hoverState=To;break;case"normal":r.hoverState=Iu;break;case"blur":r.hoverState=xo;break;case"select":r.selected=!0}}function I1(r,t,e,i){for(var n=r.style,a={},o=0;o<t.length;o++){var s=t[o],l=n[s];a[s]=l??(i&&i[s])}for(var o=0;o<r.animators.length;o++){var u=r.animators[o];u.__fromStateTransition&&u.__fromStateTransition.indexOf(e)<0&&u.targetName==="style"&&u.saveTo(a,t)}return a}function P1(r,t,e,i){var n=e&&at(e,"select")>=0,a=!1;if(r instanceof st){var o=jd(r),s=n&&o.selectFill||o.normalFill,l=n&&o.selectStroke||o.normalStroke;if(ei(s)||ei(l)){i=i||{};var u=i.style||{};u.fill==="inherit"?(a=!0,i=k({},i),u=k({},u),u.fill=s):!ei(u.fill)&&ei(s)?(a=!0,i=k({},i),u=k({},u),u.fill=Wf(s)):!ei(u.stroke)&&ei(l)&&(a||(i=k({},i),u=k({},u)),u.stroke=Wf(l)),i.style=u}}if(i&&i.z2==null){a||(i=k({},i));var f=r.z2EmphasisLift;i.z2=r.z2+(f??D1)}return i}function R1(r,t,e){if(e&&e.z2==null){e=k({},e);var i=r.z2SelectLift;e.z2=r.z2+(i??M1)}return e}function E1(r,t,e){var i=at(r.currentStates,t)>=0,n=r.style.opacity,a=i?null:I1(r,["opacity"],t,{opacity:1});e=e||{};var o=e.style||{};return o.opacity==null&&(e=k({},e),o=k({opacity:i?n:a.opacity*.1},o),e.style=o),e}function Ts(r,t){var e=this.states[r];if(this.style){if(r==="emphasis")return P1(this,r,t,e);if(r==="blur")return E1(this,r,e);if(r==="select")return R1(this,r,e)}return e}function k1(r){r.stateProxy=Ts;var t=r.getTextContent(),e=r.getTextGuideLine();t&&(t.stateProxy=Ts),e&&(e.stateProxy=Ts)}function Ah(r,t){!op(r,t)&&!r.__highByOuter&&Ne(r,tp)}function Lh(r,t){!op(r,t)&&!r.__highByOuter&&Ne(r,ep)}function ro(r,t){r.__highByOuter|=1<<(t||0),Ne(r,tp)}function io(r,t){!(r.__highByOuter&=~(1<<(t||0)))&&Ne(r,ep)}function O1(r){Ne(r,Pu)}function ip(r){Ne(r,rp)}function np(r){Ne(r,A1)}function ap(r){Ne(r,L1)}function op(r,t){return r.__highDownSilentOnTouch&&t.zrByTouch}function sp(r){var t=r.getModel(),e=[],i=[];t.eachComponent(function(n,a){var o=Lu(a),s=n==="series",l=s?r.getViewOfSeriesModel(a):r.getViewOfComponentModel(a);!s&&i.push(l),o.isBlured&&(l.group.traverse(function(u){rp(u)}),s&&e.push(a)),o.isBlured=!1}),A(i,function(n){n&&n.toggleBlurSeries&&n.toggleBlurSeries(e,!1,t)})}function Ll(r,t,e,i){var n=i.getModel();e=e||"coordinateSystem";function a(u,f){for(var h=0;h<f.length;h++){var c=u.getItemGraphicEl(f[h]);c&&ip(c)}}if(r!=null&&!(!t||t==="none")){var o=n.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var l=[];n.eachSeries(function(u){var f=o===u,h=u.coordinateSystem;h&&h.master&&(h=h.master);var c=h&&s?h===s:f;if(!(e==="series"&&!f||e==="coordinateSystem"&&!c||t==="series"&&f)){var v=i.getViewOfSeriesModel(u);if(v.group.traverse(function(p){p.__highByOuter&&f&&t==="self"||Pu(p)}),Gt(t))a(u.getData(),t);else if(H(t))for(var d=ht(t),y=0;y<d.length;y++)a(u.getData(d[y]),t[d[y]]);l.push(u),Lu(u).isBlured=!0}}),n.eachComponent(function(u,f){if(u!=="series"){var h=i.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(l,!0,n)}})}}function Il(r,t,e){if(!(r==null||t==null)){var i=e.getModel().getComponent(r,t);if(i){Lu(i).isBlured=!0;var n=e.getViewOfComponentModel(i);!n||!n.focusBlurEnabled||n.group.traverse(function(a){Pu(a)})}}}function B1(r,t,e){var i=r.seriesIndex,n=r.getData(t.dataType);if(n){var a=Gr(n,t);a=(N(a)?a[0]:a)||0;var o=n.getItemGraphicEl(a);if(!o)for(var s=n.count(),l=0;!o&&l<s;)o=n.getItemGraphicEl(l++);if(o){var u=rt(o);Ll(i,u.focus,u.blurScope,e)}else{var f=r.get(["emphasis","focus"]),h=r.get(["emphasis","blurScope"]);f!=null&&Ll(i,f,h,e)}}}function Ru(r,t,e,i){var n={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||t==null||e==null)return n;var a=i.getModel().getComponent(r,t);if(!a)return n;var o=i.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return n;for(var s=o.findHighDownDispatchers(e),l,u=0;u<s.length;u++)if(rt(s[u]).focus==="self"){l=!0;break}return{focusSelf:l,dispatchers:s}}function N1(r,t,e){var i=rt(r),n=Ru(i.componentMainType,i.componentIndex,i.componentHighDownName,e),a=n.dispatchers,o=n.focusSelf;a?(o&&Il(i.componentMainType,i.componentIndex,e),A(a,function(s){return Ah(s,t)})):(Ll(i.seriesIndex,i.focus,i.blurScope,e),i.focus==="self"&&Il(i.componentMainType,i.componentIndex,e),Ah(r,t))}function F1(r,t,e){sp(e);var i=rt(r),n=Ru(i.componentMainType,i.componentIndex,i.componentHighDownName,e).dispatchers;n?A(n,function(a){return Lh(a,t)}):Lh(r,t)}function z1(r,t,e){if(kl(t)){var i=t.dataType,n=r.getData(i),a=Gr(n,t);N(a)||(a=[a]),r[t.type===dn?"toggleSelect":t.type===cn?"select":"unselect"](a,i)}}function Ih(r){var t=r.getAllData();A(t,function(e){var i=e.data,n=e.type;i.eachItemGraphicEl(function(a,o){r.isSelected(o,n)?np(a):ap(a)})})}function H1(r){var t=[];return r.eachSeries(function(e){var i=e.getAllData();A(i,function(n){n.data;var a=n.type,o=e.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:e.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function Pl(r,t,e){lp(r,!0),Ne(r,k1),V1(r,t,e)}function G1(r){lp(r,!1)}function Rl(r,t,e,i){i?G1(r):Pl(r,t,e)}function V1(r,t,e){var i=rt(r);t!=null?(i.focus=t,i.blurScope=e):i.focus&&(i.focus=null)}var Ph=["emphasis","blur","select"],W1={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Rh(r,t,e,i){e=e||"itemStyle";for(var n=0;n<Ph.length;n++){var a=Ph[n],o=t.getModel([a,e]),s=r.ensureState(a);s.style=i?i(o):o[W1[e]]()}}function lp(r,t){var e=t===!1,i=r;r.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!e||i.__highDownDispatcher)&&(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!e)}function El(r){return!!(r&&r.__highDownDispatcher)}function U1(r){var t=Th[r];return t==null&&xh<=32&&(t=Th[r]=xh++),t}function kl(r){var t=r.type;return t===cn||t===Ha||t===dn}function Eh(r){var t=r.type;return t===Or||t===za}function $1(r){var t=jd(r);t.normalFill=r.style.fill,t.normalStroke=r.style.stroke;var e=r.states.select||{};t.selectFill=e.style&&e.style.fill||null,t.selectStroke=e.style&&e.style.stroke||null}var ri=Vr.CMD,Y1=[[],[],[]],kh=Math.sqrt,X1=Math.atan2;function Z1(r,t){if(t){var e=r.data,i=r.len(),n,a,o,s,l,u,f=ri.M,h=ri.C,c=ri.L,v=ri.R,d=ri.A,y=ri.Q;for(o=0,s=0;o<i;){switch(n=e[o++],s=o,a=0,n){case f:a=1;break;case c:a=1;break;case h:a=3;break;case y:a=2;break;case d:var p=t[4],g=t[5],m=kh(t[0]*t[0]+t[1]*t[1]),_=kh(t[2]*t[2]+t[3]*t[3]),S=X1(-t[1]/_,t[0]/m);e[o]*=m,e[o++]+=p,e[o]*=_,e[o++]+=g,e[o++]*=m,e[o++]*=_,e[o++]+=S,e[o++]+=S,o+=2,s=o;break;case v:u[0]=e[o++],u[1]=e[o++],ie(u,u,t),e[s++]=u[0],e[s++]=u[1],u[0]+=e[o++],u[1]+=e[o++],ie(u,u,t),e[s++]=u[0],e[s++]=u[1]}for(l=0;l<a;l++){var b=Y1[l];b[0]=e[o++],b[1]=e[o++],ie(b,b,t),e[s++]=b[0],e[s++]=b[1]}}r.increaseVersion()}}var Cs=Math.sqrt,oa=Math.sin,sa=Math.cos,Vi=Math.PI;function Oh(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function Ol(r,t){return(r[0]*t[0]+r[1]*t[1])/(Oh(r)*Oh(t))}function Bh(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(Ol(r,t))}function Nh(r,t,e,i,n,a,o,s,l,u,f){var h=l*(Vi/180),c=sa(h)*(r-e)/2+oa(h)*(t-i)/2,v=-1*oa(h)*(r-e)/2+sa(h)*(t-i)/2,d=c*c/(o*o)+v*v/(s*s);d>1&&(o*=Cs(d),s*=Cs(d));var y=(n===a?-1:1)*Cs((o*o*(s*s)-o*o*(v*v)-s*s*(c*c))/(o*o*(v*v)+s*s*(c*c)))||0,p=y*o*v/s,g=y*-s*c/o,m=(r+e)/2+sa(h)*p-oa(h)*g,_=(t+i)/2+oa(h)*p+sa(h)*g,S=Bh([1,0],[(c-p)/o,(v-g)/s]),b=[(c-p)/o,(v-g)/s],w=[(-1*c-p)/o,(-1*v-g)/s],x=Bh(b,w);if(Ol(b,w)<=-1&&(x=Vi),Ol(b,w)>=1&&(x=0),x<0){var C=Math.round(x/Vi*1e6)/1e6;x=Vi*2+C%2*Vi}f.addData(u,m,_,o,s,S,x,h,a)}var q1=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,K1=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Q1(r){var t=new Vr;if(!r)return t;var e=0,i=0,n=e,a=i,o,s=Vr.CMD,l=r.match(q1);if(!l)return t;for(var u=0;u<l.length;u++){for(var f=l[u],h=f.charAt(0),c=void 0,v=f.match(K1)||[],d=v.length,y=0;y<d;y++)v[y]=parseFloat(v[y]);for(var p=0;p<d;){var g=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,C=e,T=i,D=void 0,M=void 0;switch(h){case"l":e+=v[p++],i+=v[p++],c=s.L,t.addData(c,e,i);break;case"L":e=v[p++],i=v[p++],c=s.L,t.addData(c,e,i);break;case"m":e+=v[p++],i+=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="l";break;case"M":e=v[p++],i=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="L";break;case"h":e+=v[p++],c=s.L,t.addData(c,e,i);break;case"H":e=v[p++],c=s.L,t.addData(c,e,i);break;case"v":i+=v[p++],c=s.L,t.addData(c,e,i);break;case"V":i=v[p++],c=s.L,t.addData(c,e,i);break;case"C":c=s.C,t.addData(c,v[p++],v[p++],v[p++],v[p++],v[p++],v[p++]),e=v[p-2],i=v[p-1];break;case"c":c=s.C,t.addData(c,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i),e+=v[p-2],i+=v[p-1];break;case"S":g=e,m=i,D=t.len(),M=t.data,o===s.C&&(g+=e-M[D-4],m+=i-M[D-3]),c=s.C,C=v[p++],T=v[p++],e=v[p++],i=v[p++],t.addData(c,g,m,C,T,e,i);break;case"s":g=e,m=i,D=t.len(),M=t.data,o===s.C&&(g+=e-M[D-4],m+=i-M[D-3]),c=s.C,C=e+v[p++],T=i+v[p++],e+=v[p++],i+=v[p++],t.addData(c,g,m,C,T,e,i);break;case"Q":C=v[p++],T=v[p++],e=v[p++],i=v[p++],c=s.Q,t.addData(c,C,T,e,i);break;case"q":C=v[p++]+e,T=v[p++]+i,e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,C,T,e,i);break;case"T":g=e,m=i,D=t.len(),M=t.data,o===s.Q&&(g+=e-M[D-4],m+=i-M[D-3]),e=v[p++],i=v[p++],c=s.Q,t.addData(c,g,m,e,i);break;case"t":g=e,m=i,D=t.len(),M=t.data,o===s.Q&&(g+=e-M[D-4],m+=i-M[D-3]),e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,g,m,e,i);break;case"A":_=v[p++],S=v[p++],b=v[p++],w=v[p++],x=v[p++],C=e,T=i,e=v[p++],i=v[p++],c=s.A,Nh(C,T,e,i,w,x,_,S,b,c,t);break;case"a":_=v[p++],S=v[p++],b=v[p++],w=v[p++],x=v[p++],C=e,T=i,e+=v[p++],i+=v[p++],c=s.A,Nh(C,T,e,i,w,x,_,S,b,c,t);break}}(h==="z"||h==="Z")&&(c=s.Z,t.addData(c),e=n,i=a),o=c}return t.toStatic(),t}var up=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(st);function fp(r){return r.setData!=null}function hp(r,t){var e=Q1(r),i=k({},t);return i.buildPath=function(n){if(fp(n)){n.setData(e.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;e.rebuildPath(a,1)}},i.applyTransform=function(n){Z1(e,n),this.dirtyShape()},i}function J1(r,t){return new up(hp(r,t))}function j1(r,t){var e=hp(r,t),i=function(n){O(a,n);function a(o){var s=n.call(this,o)||this;return s.applyTransform=e.applyTransform,s.buildPath=e.buildPath,s}return a}(up);return i}function tS(r,t){for(var e=[],i=r.length,n=0;n<i;n++){var a=r[n];e.push(a.getUpdatedPathProxy(!0))}var o=new st(t);return o.createPathProxy(),o.buildPath=function(s){if(fp(s)){s.appendPath(e);var l=s.getContext();l&&s.rebuildPath(l,1)}},o}var eS=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),vp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new eS},t.prototype.buildPath=function(e,i){e.moveTo(i.cx+i.r,i.cy),e.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(st);vp.prototype.type="circle";const Eu=vp;var rS=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),cp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new rS},t.prototype.buildPath=function(e,i){var n=.5522848,a=i.cx,o=i.cy,s=i.rx,l=i.ry,u=s*n,f=l*n;e.moveTo(a-s,o),e.bezierCurveTo(a-s,o-f,a-u,o-l,a,o-l),e.bezierCurveTo(a+u,o-l,a+s,o-f,a+s,o),e.bezierCurveTo(a+s,o+f,a+u,o+l,a,o+l),e.bezierCurveTo(a-u,o+l,a-s,o+f,a-s,o),e.closePath()},t}(st);cp.prototype.type="ellipse";const dp=cp;var pp=Math.PI,Ds=pp*2,br=Math.sin,ii=Math.cos,iS=Math.acos,xt=Math.atan2,Fh=Math.abs,pn=Math.sqrt,on=Math.max,ye=Math.min,le=1e-4;function nS(r,t,e,i,n,a,o,s){var l=e-r,u=i-t,f=o-n,h=s-a,c=h*l-f*u;if(!(c*c<le))return c=(f*(t-a)-h*(r-n))/c,[r+c*l,t+c*u]}function la(r,t,e,i,n,a,o){var s=r-e,l=t-i,u=(o?a:-a)/pn(s*s+l*l),f=u*l,h=-u*s,c=r+f,v=t+h,d=e+f,y=i+h,p=(c+d)/2,g=(v+y)/2,m=d-c,_=y-v,S=m*m+_*_,b=n-a,w=c*y-d*v,x=(_<0?-1:1)*pn(on(0,b*b*S-w*w)),C=(w*_-m*x)/S,T=(-w*m-_*x)/S,D=(w*_+m*x)/S,M=(-w*m+_*x)/S,L=C-p,I=T-g,P=D-p,R=M-g;return L*L+I*I>P*P+R*R&&(C=D,T=M),{cx:C,cy:T,x0:-f,y0:-h,x1:C*(n/b-1),y1:T*(n/b-1)}}function aS(r){var t;if(N(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function oS(r,t){var e,i=on(t.r,0),n=on(t.r0||0,0),a=i>0,o=n>0;if(!(!a&&!o)){if(a||(i=n,n=0),n>i){var s=i;i=n,n=s}var l=t.startAngle,u=t.endAngle;if(!(isNaN(l)||isNaN(u))){var f=t.cx,h=t.cy,c=!!t.clockwise,v=Fh(u-l),d=v>Ds&&v%Ds;if(d>le&&(v=d),!(i>le))r.moveTo(f,h);else if(v>Ds-le)r.moveTo(f+i*ii(l),h+i*br(l)),r.arc(f,h,i,l,u,!c),n>le&&(r.moveTo(f+n*ii(u),h+n*br(u)),r.arc(f,h,n,u,l,c));else{var y=void 0,p=void 0,g=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,C=void 0,T=void 0,D=void 0,M=void 0,L=void 0,I=void 0,P=void 0,R=i*ii(l),E=i*br(l),G=n*ii(u),B=n*br(u),F=v>le;if(F){var W=t.cornerRadius;W&&(e=aS(W),y=e[0],p=e[1],g=e[2],m=e[3]);var q=Fh(i-n)/2;if(_=ye(q,g),S=ye(q,m),b=ye(q,y),w=ye(q,p),T=x=on(_,S),D=C=on(b,w),(x>le||C>le)&&(M=i*ii(u),L=i*br(u),I=n*ii(l),P=n*br(l),v<pp)){var K=nS(R,E,I,P,M,L,G,B);if(K){var nt=R-K[0],lt=E-K[1],ct=M-K[0],ae=L-K[1],or=1/br(iS((nt*ct+lt*ae)/(pn(nt*nt+lt*lt)*pn(ct*ct+ae*ae)))/2),qr=pn(K[0]*K[0]+K[1]*K[1]);T=ye(x,(i-qr)/(or+1)),D=ye(C,(n-qr)/(or-1))}}}if(!F)r.moveTo(f+R,h+E);else if(T>le){var Ut=ye(g,T),_t=ye(m,T),U=la(I,P,R,E,i,Ut,c),Q=la(M,L,G,B,i,_t,c);r.moveTo(f+U.cx+U.x0,h+U.cy+U.y0),T<x&&Ut===_t?r.arc(f+U.cx,h+U.cy,T,xt(U.y0,U.x0),xt(Q.y0,Q.x0),!c):(Ut>0&&r.arc(f+U.cx,h+U.cy,Ut,xt(U.y0,U.x0),xt(U.y1,U.x1),!c),r.arc(f,h,i,xt(U.cy+U.y1,U.cx+U.x1),xt(Q.cy+Q.y1,Q.cx+Q.x1),!c),_t>0&&r.arc(f+Q.cx,h+Q.cy,_t,xt(Q.y1,Q.x1),xt(Q.y0,Q.x0),!c))}else r.moveTo(f+R,h+E),r.arc(f,h,i,l,u,!c);if(!(n>le)||!F)r.lineTo(f+G,h+B);else if(D>le){var Ut=ye(y,D),_t=ye(p,D),U=la(G,B,M,L,n,-_t,c),Q=la(R,E,I,P,n,-Ut,c);r.lineTo(f+U.cx+U.x0,h+U.cy+U.y0),D<C&&Ut===_t?r.arc(f+U.cx,h+U.cy,D,xt(U.y0,U.x0),xt(Q.y0,Q.x0),!c):(_t>0&&r.arc(f+U.cx,h+U.cy,_t,xt(U.y0,U.x0),xt(U.y1,U.x1),!c),r.arc(f,h,n,xt(U.cy+U.y1,U.cx+U.x1),xt(Q.cy+Q.y1,Q.cx+Q.x1),c),Ut>0&&r.arc(f+Q.cx,h+Q.cy,Ut,xt(Q.y1,Q.x1),xt(Q.y0,Q.x0),!c))}else r.lineTo(f+G,h+B),r.arc(f,h,n,u,l,c)}r.closePath()}}}var sS=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),gp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new sS},t.prototype.buildPath=function(e,i){oS(e,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(st);gp.prototype.type="sector";const ku=gp;var lS=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),yp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new lS},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.PI*2;e.moveTo(n+i.r,a),e.arc(n,a,i.r,0,o,!1),e.moveTo(n+i.r0,a),e.arc(n,a,i.r0,0,o,!0)},t}(st);yp.prototype.type="ring";const mp=yp;function uS(r,t,e,i){var n=[],a=[],o=[],s=[],l,u,f,h;if(i){f=[1/0,1/0],h=[-1/0,-1/0];for(var c=0,v=r.length;c<v;c++)di(f,f,r[c]),pi(h,h,r[c]);di(f,f,i[0]),pi(h,h,i[1])}for(var c=0,v=r.length;c<v;c++){var d=r[c];if(e)l=r[c?c-1:v-1],u=r[(c+1)%v];else if(c===0||c===v-1){n.push(Pm(r[c]));continue}else l=r[c-1],u=r[c+1];Rm(a,u,l),Uo(a,a,t);var y=ul(d,l),p=ul(d,u),g=y+p;g!==0&&(y/=g,p/=g),Uo(o,a,-y),Uo(s,a,p);var m=Df([],d,o),_=Df([],d,s);i&&(pi(m,m,f),di(m,m,h),pi(_,_,f),di(_,_,h)),n.push(m),n.push(_)}return e&&n.push(n.shift()),n}function _p(r,t,e){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=uS(n,i,e,t.smoothConstraint);r.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;s<(e?o:o-1);s++){var l=a[s*2],u=a[s*2+1],f=n[(s+1)%o];r.bezierCurveTo(l[0],l[1],u[0],u[1],f[0],f[1])}}else{r.moveTo(n[0][0],n[0][1]);for(var s=1,h=n.length;s<h;s++)r.lineTo(n[s][0],n[s][1])}e&&r.closePath()}}var fS=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),Sp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new fS},t.prototype.buildPath=function(e,i){_p(e,i,!0)},t}(st);Sp.prototype.type="polygon";const wp=Sp;var hS=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),bp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new hS},t.prototype.buildPath=function(e,i){_p(e,i,!1)},t}(st);bp.prototype.type="polyline";const xp=bp;var vS={},cS=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),Tp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new cS},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var l=qd(vS,i,this.style);n=l.x1,a=l.y1,o=l.x2,s=l.y2}else n=i.x1,a=i.y1,o=i.x2,s=i.y2;var u=i.percent;u!==0&&(e.moveTo(n,a),u<1&&(o=n*(1-u)+o*u,s=a*(1-u)+s*u),e.lineTo(o,s))},t.prototype.pointAt=function(e){var i=this.shape;return[i.x1*(1-e)+i.x2*e,i.y1*(1-e)+i.y2*e]},t}(st);Tp.prototype.type="line";const Wr=Tp;var Ot=[],dS=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function zh(r,t,e){var i=r.cpx2,n=r.cpy2;return i!=null||n!=null?[(e?Nf:gt)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?Nf:gt)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?Ff:Dt)(r.x1,r.cpx1,r.x2,t),(e?Ff:Dt)(r.y1,r.cpy1,r.y2,t)]}var Cp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new dS},t.prototype.buildPath=function(e,i){var n=i.x1,a=i.y1,o=i.x2,s=i.y2,l=i.cpx1,u=i.cpy1,f=i.cpx2,h=i.cpy2,c=i.percent;c!==0&&(e.moveTo(n,a),f==null||h==null?(c<1&&(Qa(n,l,o,c,Ot),l=Ot[1],o=Ot[2],Qa(a,u,s,c,Ot),u=Ot[1],s=Ot[2]),e.quadraticCurveTo(l,u,o,s)):(c<1&&(Ka(n,l,f,o,c,Ot),l=Ot[1],f=Ot[2],o=Ot[3],Ka(a,u,h,s,c,Ot),u=Ot[1],h=Ot[2],s=Ot[3]),e.bezierCurveTo(l,u,f,h,o,s)))},t.prototype.pointAt=function(e){return zh(this.shape,e,!1)},t.prototype.tangentAt=function(e){var i=zh(this.shape,e,!0);return Om(i,i)},t}(st);Cp.prototype.type="bezier-curve";const Dp=Cp;var pS=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),Mp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new pS},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.max(i.r,0),s=i.startAngle,l=i.endAngle,u=i.clockwise,f=Math.cos(s),h=Math.sin(s);e.moveTo(f*o+n,h*o+a),e.arc(n,a,o,s,l,!u)},t}(st);Mp.prototype.type="arc";const Ou=Mp;var gS=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,i=this.shapeChanged(),n=0;n<e.length;n++)i=i||e[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<e.length;n++)e[n].path||e[n].createPathProxy(),e[n].path.setScale(i[0],i[1],e[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(e,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],i=0;i<e.length;i++)e[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),st.prototype.getBoundingRect.call(this)},t}(st);const yS=gS;var mS=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}();const Ap=mS;var _S=function(r){O(t,r);function t(e,i,n,a,o,s){var l=r.call(this,o)||this;return l.x=e??0,l.y=i??0,l.x2=n??1,l.y2=a??0,l.type="linear",l.global=s||!1,l}return t}(Ap);const Lp=_S;var SS=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this,a)||this;return s.x=e??.5,s.y=i??.5,s.r=n??.5,s.type="radial",s.global=o||!1,s}return t}(Ap);const wS=SS;var xr=[0,0],Tr=[0,0],ua=new ot,fa=new ot,bS=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new ot;for(var i=0;i<2;i++)this._axes[i]=new ot;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var i=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,l=o+t.height;if(i[0].set(a,o),i[1].set(s,o),i[2].set(s,l),i[3].set(a,l),e)for(var u=0;u<4;u++)i[u].transform(e);ot.sub(n[0],i[1],i[0]),ot.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var u=0;u<2;u++)this._origin[u]=n[u].dot(i[0])},r.prototype.intersect=function(t,e){var i=!0,n=!e;return ua.set(1/0,1/0),fa.set(0,0),!this._intersectCheckOneSide(this,t,ua,fa,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,ua,fa,n,-1)&&(i=!1,n)||n||ot.copy(e,i?ua:fa),i},r.prototype._intersectCheckOneSide=function(t,e,i,n,a,o){for(var s=!0,l=0;l<2;l++){var u=this._axes[l];if(this._getProjMinMaxOnAxis(l,t._corners,xr),this._getProjMinMaxOnAxis(l,e._corners,Tr),xr[1]<Tr[0]||xr[0]>Tr[1]){if(s=!1,a)return s;var f=Math.abs(Tr[0]-xr[1]),h=Math.abs(xr[0]-Tr[1]);Math.min(f,h)>n.len()&&(f<h?ot.scale(n,u,-f*o):ot.scale(n,u,h*o))}else if(i){var f=Math.abs(Tr[0]-xr[1]),h=Math.abs(xr[0]-Tr[1]);Math.min(f,h)<i.len()&&(f<h?ot.scale(i,u,f*o):ot.scale(i,u,-h*o))}}return s},r.prototype._getProjMinMaxOnAxis=function(t,e,i){for(var n=this._axes[t],a=this._origin,o=e[0].dot(n)+a[t],s=o,l=o,u=1;u<e.length;u++){var f=e[u].dot(n)+a[t];s=Math.min(f,s),l=Math.max(f,l)}i[0]=s,i[1]=l},r}();const no=bS;var xS=[],TS=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,i){i?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,i){i=i||!1;for(var n=0;n<e.length;n++)this.addDisplayable(e[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var i=this._cursor;i<this._displayables.length;i++)e&&e(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)e&&e(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.parent=this,i.update(),i.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new et(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(xS)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(e,i))return!0}return!1},t}(Wn);const CS=TS;var DS=yt();function MS(r,t,e,i,n){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),l=r==="update";if(s){var u=void 0,f=void 0,h=void 0;i?(u=Z(i.duration,200),f=Z(i.easing,"cubicOut"),h=0):(u=t.getShallow(l?"animationDurationUpdate":"animationDuration"),f=t.getShallow(l?"animationEasingUpdate":"animationEasing"),h=t.getShallow(l?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(u=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),$(h)&&(h=h(e,n)),$(u)&&(u=u(e));var c={duration:u||0,delay:h,easing:f};return c}else return null}function Bu(r,t,e,i,n,a,o){var s=!1,l;$(n)?(o=a,a=n,n=null):H(n)&&(a=n.cb,o=n.during,s=n.isFrom,l=n.removeOpt,n=n.dataIndex);var u=r==="leave";u||t.stopAnimation("leave");var f=MS(r,i,n,u?l||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(t,n):null);if(f&&f.duration>0){var h=f.duration,c=f.delay,v=f.easing,d={duration:h,delay:c||0,easing:v,done:a,force:!!a||!!o,setToFinal:!u,scope:r,during:o};s?t.animateFrom(e,d):t.animateTo(e,d)}else t.stopAnimation(),!s&&t.attr(e),o&&o(1),a&&a()}function nr(r,t,e,i,n,a){Bu("update",r,t,e,i,n,a)}function Un(r,t,e,i,n,a){Bu("enter",r,t,e,i,n,a)}function gn(r){if(!r.__zr)return!0;for(var t=0;t<r.animators.length;t++){var e=r.animators[t];if(e.scope==="leave")return!0}return!1}function ao(r,t,e,i,n,a){gn(r)||Bu("leave",r,t,e,i,n,a)}function Hh(r,t,e,i){r.removeTextContent(),r.removeTextGuideLine(),ao(r,{style:{opacity:0}},t,e,i)}function AS(r,t,e){function i(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(n){n.isGroup||Hh(n,t,e,i)}):Hh(r,t,e,i)}function LS(r){DS(r).oldStyle=r.style}var oo=Math.max,so=Math.min,Bl={};function IS(r){return st.extend(r)}var PS=j1;function RS(r,t){return PS(r,t)}function ve(r,t){Bl[r]=t}function ES(r){if(Bl.hasOwnProperty(r))return Bl[r]}function Nu(r,t,e,i){var n=J1(r,t);return e&&(i==="center"&&(e=Pp(e,n.getBoundingRect())),Rp(n,e)),n}function Ip(r,t,e){var i=new Yr({style:{image:r,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(n){if(e==="center"){var a={width:n.width,height:n.height};i.setStyle(Pp(t,a))}}});return i}function Pp(r,t){var e=t.width/t.height,i=r.height*e,n;i<=r.width?n=r.height:(i=r.width,n=i/e);var a=r.x+r.width/2,o=r.y+r.height/2;return{x:a-i/2,y:o-n/2,width:i,height:n}}var kS=tS;function Rp(r,t){if(r.applyTransform){var e=r.getBoundingRect(),i=e.calculateTransform(t);r.applyTransform(i)}}function An(r,t){return qd(r,r,{lineWidth:t}),r}function OS(r){return Kd(r.shape,r.shape,r.style),r}var BS=Pr;function NS(r,t){for(var e=yu([]);r&&r!==t;)Si(e,r.getLocalTransform(),e),r=r.parent;return e}function Fu(r,t,e){return t&&!Gt(t)&&(t=wu.getLocalTransform(t)),e&&(t=_u([],t)),ie([],r,t)}function FS(r,t,e){var i=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),n=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[r==="left"?-i:r==="right"?i:0,r==="top"?-n:r==="bottom"?n:0];return a=Fu(a,t,e),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function Gh(r){return!r.isGroup}function zS(r){return r.shape!=null}function Ep(r,t,e){if(!r||!t)return;function i(o){var s={};return o.traverse(function(l){Gh(l)&&l.anid&&(s[l.anid]=l)}),s}function n(o){var s={x:o.x,y:o.y,rotation:o.rotation};return zS(o)&&(s.shape=k({},o.shape)),s}var a=i(r);t.traverse(function(o){if(Gh(o)&&o.anid){var s=a[o.anid];if(s){var l=n(o);o.attr(n(s)),nr(o,l,e,rt(o).dataIndex)}}})}function HS(r,t){return V(r,function(e){var i=e[0];i=oo(i,t.x),i=so(i,t.x+t.width);var n=e[1];return n=oo(n,t.y),n=so(n,t.y+t.height),[i,n]})}function GS(r,t){var e=oo(r.x,t.x),i=so(r.x+r.width,t.x+t.width),n=oo(r.y,t.y),a=so(r.y+r.height,t.y+t.height);if(i>=e&&a>=n)return{x:e,y:n,width:i-e,height:a-n}}function zu(r,t,e){var i=k({rectHover:!0},t),n=i.style={strokeNoScale:!0};if(e=e||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(n.image=r.slice(8),it(n,e),new Yr(i)):Nu(r.replace("path://",""),i,e,"center")}function VS(r,t,e,i,n){for(var a=0,o=n[n.length-1];a<n.length;a++){var s=n[a];if(kp(r,t,e,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function kp(r,t,e,i,n,a,o,s){var l=e-r,u=i-t,f=o-n,h=s-a,c=Ms(f,h,l,u);if(WS(c))return!1;var v=r-n,d=t-a,y=Ms(v,d,l,u)/c;if(y<0||y>1)return!1;var p=Ms(v,d,f,h)/c;return!(p<0||p>1)}function Ms(r,t,e,i){return r*i-e*t}function WS(r){return r<=1e-6&&r>=-1e-6}function Do(r){var t=r.itemTooltipOption,e=r.componentModel,i=r.itemName,n=z(t)?{formatter:t}:t,a=e.mainType,o=e.componentIndex,s={componentType:a,name:i,$vars:["name"]};s[a+"Index"]=o;var l=r.formatterParamsExtra;l&&A(ht(l),function(f){zr(s,f)||(s[f]=l[f],s.$vars.push(f))});var u=rt(r.el);u.componentMainType=a,u.componentIndex=o,u.tooltipConfig={name:i,option:it({content:i,encodeHTMLContent:!0,formatterParams:s},n)}}function Vh(r,t){var e;r.isGroup&&(e=t(r)),e||r.traverse(t)}function Hu(r,t){if(r)if(N(r))for(var e=0;e<r.length;e++)Vh(r[e],t);else Vh(r,t)}ve("circle",Eu);ve("ellipse",dp);ve("sector",ku);ve("ring",mp);ve("polygon",wp);ve("polyline",xp);ve("rect",bt);ve("line",Wr);ve("bezierCurve",Dp);ve("arc",Ou);const US=Object.freeze(Object.defineProperty({__proto__:null,Arc:Ou,BezierCurve:Dp,BoundingRect:et,Circle:Eu,CompoundPath:yS,Ellipse:dp,Group:Et,Image:Yr,IncrementalDisplayable:CS,Line:Wr,LinearGradient:Lp,OrientedBoundingRect:no,Path:st,Point:ot,Polygon:wp,Polyline:xp,RadialGradient:wS,Rect:bt,Ring:mp,Sector:ku,Text:kt,applyTransform:Fu,clipPointsByRect:HS,clipRectByRect:GS,createIcon:zu,extendPath:RS,extendShape:IS,getShapeClass:ES,getTransform:NS,groupTransition:Ep,initProps:Un,isElementRemoved:gn,lineLineIntersect:kp,linePolygonIntersect:VS,makeImage:Ip,makePath:Nu,mergePath:kS,registerShape:ve,removeElement:ao,removeElementWithFadeOut:AS,resizePath:Rp,setTooltipConfig:Do,subPixelOptimize:BS,subPixelOptimizeLine:An,subPixelOptimizeRect:OS,transformDirection:FS,traverseElements:Hu,updateProps:nr},Symbol.toStringTag,{value:"Module"}));var Mo={};function $S(r,t){for(var e=0;e<be.length;e++){var i=be[e],n=t[i],a=r.ensureState(i);a.style=a.style||{},a.style.text=n}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:t.normal}),r.useStates(o,!0)}function Wh(r,t,e){var i=r.labelFetcher,n=r.labelDataIndex,a=r.labelDimIndex,o=t.normal,s;i&&(s=i.getFormattedLabel(n,"normal",null,a,o&&o.get("formatter"),e!=null?{interpolatedValue:e}:null)),s==null&&(s=$(r.defaultText)?r.defaultText(n,r,e):r.defaultText);for(var l={normal:s},u=0;u<be.length;u++){var f=be[u],h=t[f];l[f]=Z(i?i.getFormattedLabel(n,f,null,a,h&&h.get("formatter")):null,s)}return l}function Gu(r,t,e,i){e=e||Mo;for(var n=r instanceof kt,a=!1,o=0;o<Ch.length;o++){var s=t[Ch[o]];if(s&&s.getShallow("show")){a=!0;break}}var l=n?r:r.getTextContent();if(a){n||(l||(l=new kt,r.setTextContent(l)),r.stateProxy&&(l.stateProxy=r.stateProxy));var u=Wh(e,t),f=t.normal,h=!!f.getShallow("show"),c=ar(f,i&&i.normal,e,!1,!n);c.text=u.normal,n||r.setTextConfig(Uh(f,e,!1));for(var o=0;o<be.length;o++){var v=be[o],s=t[v];if(s){var d=l.ensureState(v),y=!!Z(s.getShallow("show"),h);if(y!==h&&(d.ignore=!y),d.style=ar(s,i&&i[v],e,!0,!n),d.style.text=u[v],!n){var p=r.ensureState(v);p.textConfig=Uh(s,e,!0)}}}l.silent=!!f.getShallow("silent"),l.style.x!=null&&(c.x=l.style.x),l.style.y!=null&&(c.y=l.style.y),l.ignore=!h,l.useStyle(c),l.dirty(),e.enableTextSetter&&(Wu(l).setLabelText=function(g){var m=Wh(e,t,g);$S(l,m)})}else l&&(l.ignore=!0);r.dirty()}function Vu(r,t){t=t||"label";for(var e={normal:r.getModel(t)},i=0;i<be.length;i++){var n=be[i];e[n]=r.getModel([n,t])}return e}function ar(r,t,e,i,n){var a={};return YS(a,r,e,i,n),t&&k(a,t),a}function Uh(r,t,e){t=t||{};var i={},n,a=r.getShallow("rotate"),o=Z(r.getShallow("distance"),e?null:5),s=r.getShallow("offset");return n=r.getShallow("position")||(e?null:"inside"),n==="outside"&&(n=t.defaultOutsidePosition||"top"),n!=null&&(i.position=n),s!=null&&(i.offset=s),a!=null&&(a*=Math.PI/180,i.rotation=a),o!=null&&(i.distance=o),i.outsideFill=r.get("color")==="inherit"?t.inheritColor||null:"auto",i}function YS(r,t,e,i,n){e=e||Mo;var a=t.ecModel,o=a&&a.option.textStyle,s=XS(t),l;if(s){l={};for(var u in s)if(s.hasOwnProperty(u)){var f=t.getModel(["rich",u]);Zh(l[u]={},f,o,e,i,n,!1,!0)}}l&&(r.rich=l);var h=t.get("overflow");h&&(r.overflow=h);var c=t.get("minMargin");c!=null&&(r.margin=c),Zh(r,t,o,e,i,n,!0,!1)}function XS(r){for(var t;r&&r!==r.ecModel;){var e=(r.option||Mo).rich;if(e){t=t||{};for(var i=ht(e),n=0;n<i.length;n++){var a=i[n];t[a]=1}}r=r.parentModel}return t}var $h=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],Yh=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],Xh=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function Zh(r,t,e,i,n,a,o,s){e=!n&&e||Mo;var l=i&&i.inheritColor,u=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=Z(t.getShallow("opacity"),e.opacity);(u==="inherit"||u==="auto")&&(l?u=l:u=null),(f==="inherit"||f==="auto")&&(l?f=l:f=null),a||(u=u||e.color,f=f||e.textBorderColor),u!=null&&(r.fill=u),f!=null&&(r.stroke=f);var c=Z(t.getShallow("textBorderWidth"),e.textBorderWidth);c!=null&&(r.lineWidth=c);var v=Z(t.getShallow("textBorderType"),e.textBorderType);v!=null&&(r.lineDash=v);var d=Z(t.getShallow("textBorderDashOffset"),e.textBorderDashOffset);d!=null&&(r.lineDashOffset=d),!n&&h==null&&!s&&(h=i&&i.defaultOpacity),h!=null&&(r.opacity=h),!n&&!a&&r.fill==null&&i.inheritColor&&(r.fill=i.inheritColor);for(var y=0;y<$h.length;y++){var p=$h[y],g=Z(t.getShallow(p),e[p]);g!=null&&(r[p]=g)}for(var y=0;y<Yh.length;y++){var p=Yh[y],g=t.getShallow(p);g!=null&&(r[p]=g)}if(r.verticalAlign==null){var m=t.getShallow("baseline");m!=null&&(r.verticalAlign=m)}if(!o||!i.disableBox){for(var y=0;y<Xh.length;y++){var p=Xh[y],g=t.getShallow(p);g!=null&&(r[p]=g)}var _=t.getShallow("borderType");_!=null&&(r.borderDash=_),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&l&&(r.backgroundColor=l),(r.borderColor==="auto"||r.borderColor==="inherit")&&l&&(r.borderColor=l)}}function ZS(r,t){var e=t&&t.getModel("textStyle");return _e([r.fontStyle||e&&e.getShallow("fontStyle")||"",r.fontWeight||e&&e.getShallow("fontWeight")||"",(r.fontSize||e&&e.getShallow("fontSize")||12)+"px",r.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}var Wu=yt();function EA(r,t,e,i){if(r){var n=Wu(r);n.prevValue=n.value,n.value=e;var a=t.normal;n.valueAnimation=a.get("valueAnimation"),n.valueAnimation&&(n.precision=a.get("precision"),n.defaultInterpolatedText=i,n.statesModels=t)}}var qS=["textStyle","color"],As=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],Ls=new kt,KS=function(){function r(){}return r.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(qS):null)},r.prototype.getFont=function(){return ZS({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},i=0;i<As.length;i++)e[As[i]]=this.getShallow(As[i]);return Ls.useStyle(e),Ls.update(),Ls.getBoundingRect()},r}();const QS=KS;var Op=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],JS=Mn(Op),jS=function(){function r(){}return r.prototype.getLineStyle=function(t){return JS(this,t)},r}(),Bp=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],tw=Mn(Bp),ew=function(){function r(){}return r.prototype.getItemStyle=function(t,e){return tw(this,t,e)},r}(),Xr=function(){function r(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}return r.prototype.init=function(t,e,i){},r.prototype.mergeOption=function(t,e){tt(this.option,t,!0)},r.prototype.get=function(t,e){return t==null?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},r.prototype.getShallow=function(t,e){var i=this.option,n=i==null?i:i[t];if(n==null&&!e){var a=this.parentModel;a&&(n=a.getShallow(t))}return n},r.prototype.getModel=function(t,e){var i=t!=null,n=i?this.parsePath(t):null,a=i?this._doGet(n):this.option;return e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(n)),new r(a,e,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var t=this.constructor;return new t(J(this.option))},r.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},r.prototype.resolveParentPath=function(t){return t},r.prototype.isAnimationEnabled=function(){if(!Y.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(t,e){var i=this.option;if(!t)return i;for(var n=0;n<t.length&&!(t[n]&&(i=i&&typeof i=="object"?i[t[n]]:null,i==null));n++);return i==null&&e&&(i=e._doGet(this.resolveParentPath(t),e.parentModel)),i},r}();Au(Xr);I_(Xr);xe(Xr,jS);xe(Xr,ew);xe(Xr,O_);xe(Xr,QS);const At=Xr;var rw=Math.round(Math.random()*10);function Ao(r){return[r||"",rw++].join("_")}function iw(r){var t={};r.registerSubTypeDefaulter=function(e,i){var n=Se(e);t[n.main]=i},r.determineSubType=function(e,i){var n=i.type;if(!n){var a=Se(e).main;r.hasSubTypes(e)&&t[a]&&(n=t[a](i))}return n}}function nw(r,t){r.topologicalTravel=function(a,o,s,l){if(!a.length)return;var u=e(o),f=u.graph,h=u.noEntryList,c={};for(A(a,function(m){c[m]=!0});h.length;){var v=h.pop(),d=f[v],y=!!c[v];y&&(s.call(l,v,d.originalDeps.slice()),delete c[v]),A(d.successor,y?g:p)}A(c,function(){var m="";throw new Error(m)});function p(m){f[m].entryCount--,f[m].entryCount===0&&h.push(m)}function g(m){c[m]=!0,p(m)}};function e(a){var o={},s=[];return A(a,function(l){var u=i(o,l),f=u.originalDeps=t(l),h=n(f,a);u.entryCount=h.length,u.entryCount===0&&s.push(l),A(h,function(c){at(u.predecessor,c)<0&&u.predecessor.push(c);var v=i(o,c);at(v.successor,c)<0&&v.successor.push(l)})}),{graph:o,noEntryList:s}}function i(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function n(a,o){var s=[];return A(a,function(l){at(o,l)>=0&&s.push(l)}),s}}function aw(r,t){return tt(tt({},r,!0),t,!0)}const ow={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},sw={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var lo="ZH",Uu="EN",bi=Uu,Ga={},$u={},Np=Y.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||bi).toUpperCase();return r.indexOf(lo)>-1?lo:bi}():bi;function Fp(r,t){r=r.toUpperCase(),$u[r]=new At(t),Ga[r]=t}function lw(r){if(z(r)){var t=Ga[r.toUpperCase()]||{};return r===lo||r===Uu?J(t):tt(J(t),J(Ga[bi]),!1)}else return tt(J(r),J(Ga[bi]),!1)}function uw(r){return $u[r]}function fw(){return $u[bi]}Fp(Uu,ow);Fp(lo,sw);var Yu=1e3,Xu=Yu*60,yn=Xu*60,re=yn*24,qh=re*365,sn={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},ha="{yyyy}-{MM}-{dd}",Kh={year:"{yyyy}",month:"{yyyy}-{MM}",day:ha,hour:ha+" "+sn.hour,minute:ha+" "+sn.minute,second:ha+" "+sn.second,millisecond:sn.none},Is=["year","month","day","hour","minute","second","millisecond"],zp=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Ge(r,t){return r+="","0000".substr(0,t-r.length)+r}function xi(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function hw(r){return r===xi(r)}function vw(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function Lo(r,t,e,i){var n=Oe(r),a=n[Zu(e)](),o=n[Ti(e)]()+1,s=Math.floor((o-1)/3)+1,l=n[Io(e)](),u=n["get"+(e?"UTC":"")+"Day"](),f=n[Ln(e)](),h=(f-1)%12+1,c=n[Po(e)](),v=n[Ro(e)](),d=n[Eo(e)](),y=f>=12?"pm":"am",p=y.toUpperCase(),g=i instanceof At?i:uw(i||Np)||fw(),m=g.getModel("time"),_=m.get("month"),S=m.get("monthAbbr"),b=m.get("dayOfWeek"),w=m.get("dayOfWeekAbbr");return(t||"").replace(/{a}/g,y+"").replace(/{A}/g,p+"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,Ge(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[o-1]).replace(/{MMM}/g,S[o-1]).replace(/{MM}/g,Ge(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Ge(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,b[u]).replace(/{ee}/g,w[u]).replace(/{e}/g,u+"").replace(/{HH}/g,Ge(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,Ge(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Ge(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Ge(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,Ge(d,3)).replace(/{S}/g,d+"")}function cw(r,t,e,i,n){var a=null;if(z(e))a=e;else if($(e))a=e(r.value,t,{level:r.level});else{var o=k({},sn);if(r.level>0)for(var s=0;s<Is.length;++s)o[Is[s]]="{primary|"+o[Is[s]]+"}";var l=e?e.inherit===!1?e:it(e,o):o,u=Hp(r.value,n);if(l[u])a=l[u];else if(l.inherit){for(var f=zp.indexOf(u),s=f-1;s>=0;--s)if(l[u]){a=l[u];break}a=a||o.none}if(N(a)){var h=r.level==null?0:r.level>=0?r.level:a.length+r.level;h=Math.min(h,a.length-1),a=a[h]}}return Lo(new Date(r.value),a,n,i)}function Hp(r,t){var e=Oe(r),i=e[Ti(t)]()+1,n=e[Io(t)](),a=e[Ln(t)](),o=e[Po(t)](),s=e[Ro(t)](),l=e[Eo(t)](),u=l===0,f=u&&s===0,h=f&&o===0,c=h&&a===0,v=c&&n===1,d=v&&i===1;return d?"year":v?"month":c?"day":h?"hour":f?"minute":u?"second":"millisecond"}function Qh(r,t,e){var i=vt(r)?Oe(r):r;switch(t=t||Hp(r,e),t){case"year":return i[Zu(e)]();case"half-year":return i[Ti(e)]()>=6?1:0;case"quarter":return Math.floor((i[Ti(e)]()+1)/4);case"month":return i[Ti(e)]();case"day":return i[Io(e)]();case"half-day":return i[Ln(e)]()/24;case"hour":return i[Ln(e)]();case"minute":return i[Po(e)]();case"second":return i[Ro(e)]();case"millisecond":return i[Eo(e)]()}}function Zu(r){return r?"getUTCFullYear":"getFullYear"}function Ti(r){return r?"getUTCMonth":"getMonth"}function Io(r){return r?"getUTCDate":"getDate"}function Ln(r){return r?"getUTCHours":"getHours"}function Po(r){return r?"getUTCMinutes":"getMinutes"}function Ro(r){return r?"getUTCSeconds":"getSeconds"}function Eo(r){return r?"getUTCMilliseconds":"getMilliseconds"}function dw(r){return r?"setUTCFullYear":"setFullYear"}function Gp(r){return r?"setUTCMonth":"setMonth"}function Vp(r){return r?"setUTCDate":"setDate"}function Wp(r){return r?"setUTCHours":"setHours"}function Up(r){return r?"setUTCMinutes":"setMinutes"}function $p(r){return r?"setUTCSeconds":"setSeconds"}function Yp(r){return r?"setUTCMilliseconds":"setMilliseconds"}function Xp(r){if(!u_(r))return z(r)?r:"-";var t=(r+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function Zp(r,t){return r=(r||"").toLowerCase().replace(/-(.)/g,function(e,i){return i.toUpperCase()}),t&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var ko=nd;function Nl(r,t,e){var i="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function n(f){return f&&_e(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=r instanceof Date;if(o||s){var l=o?Oe(r):r;if(isNaN(+l)){if(s)return"-"}else return Lo(l,i,e)}if(t==="ordinal")return sl(r)?n(r):vt(r)&&a(r)?r+"":"-";var u=eo(r);return a(u)?Xp(u):sl(r)?n(r):typeof r=="boolean"?r+"":"-"}var Jh=["a","b","c","d","e","f","g"],Ps=function(r,t){return"{"+r+(t??"")+"}"};function qp(r,t,e){N(t)||(t=[t]);var i=t.length;if(!i)return"";for(var n=t[0].$vars||[],a=0;a<n.length;a++){var o=Jh[a];r=r.replace(Ps(o),Ps(o,0))}for(var s=0;s<i;s++)for(var l=0;l<n.length;l++){var u=t[s][n[l]];r=r.replace(Ps(Jh[l],s),e?Nt(u):u)}return r}function pw(r,t){var e=z(r)?{color:r,extraCssText:t}:r||{},i=e.color,n=e.type;t=e.extraCssText;var a=e.renderMode||"html";if(!i)return"";if(a==="html")return n==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Nt(i)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Nt(i)+";"+(t||"")+'"></span>';var o=e.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:n==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}}function Ur(r,t){return t=t||"transparent",z(r)?r:H(r)&&r.colorStops&&(r.colorStops[0]||{}).color||t}function jh(r,t){if(t==="_blank"||t==="blank"){var e=window.open();e.opener=null,e.location.href=r}else window.open(r,t)}var Va=A,gw=["left","right","top","bottom","width","height"],va=[["width","left","right"],["height","top","bottom"]];function qu(r,t,e,i,n){var a=0,o=0;i==null&&(i=1/0),n==null&&(n=1/0);var s=0;t.eachChild(function(l,u){var f=l.getBoundingRect(),h=t.childAt(u+1),c=h&&h.getBoundingRect(),v,d;if(r==="horizontal"){var y=f.width+(c?-c.x+f.x:0);v=a+y,v>i||l.newline?(a=0,v=y,o+=s+e,s=f.height):s=Math.max(s,f.height)}else{var p=f.height+(c?-c.y+f.y:0);d=o+p,d>n||l.newline?(a+=s+e,o=0,d=p,s=f.width):s=Math.max(s,f.width)}l.newline||(l.x=a,l.y=o,l.markRedraw(),r==="horizontal"?a=v+e:o=d+e)})}var mn=qu;mt(qu,"vertical");mt(qu,"horizontal");function In(r,t,e){e=ko(e||0);var i=t.width,n=t.height,a=Pt(r.left,i),o=Pt(r.top,n),s=Pt(r.right,i),l=Pt(r.bottom,n),u=Pt(r.width,i),f=Pt(r.height,n),h=e[2]+e[0],c=e[1]+e[3],v=r.aspect;switch(isNaN(u)&&(u=i-s-c-a),isNaN(f)&&(f=n-l-h-o),v!=null&&(isNaN(u)&&isNaN(f)&&(v>i/n?u=i*.8:f=n*.8),isNaN(u)&&(u=v*f),isNaN(f)&&(f=u/v)),isNaN(a)&&(a=i-s-u-c),isNaN(o)&&(o=n-l-f-h),r.left||r.right){case"center":a=i/2-u/2-e[3];break;case"right":a=i-u-c;break}switch(r.top||r.bottom){case"middle":case"center":o=n/2-f/2-e[0];break;case"bottom":o=n-f-h;break}a=a||0,o=o||0,isNaN(u)&&(u=i-c-a-(s||0)),isNaN(f)&&(f=n-h-o-(l||0));var d=new et(a+e[3],o+e[0],u,f);return d.margin=e,d}function Pn(r){var t=r.layoutMode||r.constructor.layoutMode;return H(t)?t:t?{type:t}:null}function Ci(r,t,e){var i=e&&e.ignoreSize;!N(i)&&(i=[i,i]);var n=o(va[0],0),a=o(va[1],1);u(va[0],r,n),u(va[1],r,a);function o(f,h){var c={},v=0,d={},y=0,p=2;if(Va(f,function(_){d[_]=r[_]}),Va(f,function(_){s(t,_)&&(c[_]=d[_]=t[_]),l(c,_)&&v++,l(d,_)&&y++}),i[h])return l(t,f[1])?d[f[2]]=null:l(t,f[2])&&(d[f[1]]=null),d;if(y===p||!v)return d;if(v>=p)return c;for(var g=0;g<f.length;g++){var m=f[g];if(!s(c,m)&&s(r,m)){c[m]=r[m];break}}return c}function s(f,h){return f.hasOwnProperty(h)}function l(f,h){return f[h]!=null&&f[h]!=="auto"}function u(f,h,c){Va(f,function(v){h[v]=c[v]})}}function Oo(r){return yw({},r)}function yw(r,t){return t&&r&&Va(gw,function(e){t.hasOwnProperty(e)&&(r[e]=t[e])}),r}var mw=yt(),Pi=function(r){O(t,r);function t(e,i,n){var a=r.call(this,e,i,n)||this;return a.uid=Ao("ec_cpt_model"),a}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=Pn(this),a=n?Oo(e):{},o=i.getTheme();tt(e,o.get(this.mainType)),tt(e,this.getDefaultOption()),n&&Ci(e,a,n)},t.prototype.mergeOption=function(e,i){tt(this.option,e,!0);var n=Pn(this);n&&Ci(this.option,e,n)},t.prototype.optionUpdated=function(e,i){},t.prototype.getDefaultOption=function(){var e=this.constructor;if(!M_(e))return e.defaultOption;var i=mw(this);if(!i.defaultOption){for(var n=[],a=e;a;){var o=a.prototype.defaultOption;o&&n.push(o),a=a.superClass}for(var s={},l=n.length-1;l>=0;l--)s=tt(s,n[l],!0);i.defaultOption=s}return i.defaultOption},t.prototype.getReferringComponents=function(e,i){var n=e+"Index",a=e+"Id";return Vn(this.ecModel,e,{index:this.get(n,!0),id:this.get(a,!0)},i)},t.prototype.getBoxLayoutParams=function(){var e=this;return{left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(e){this.option.zlevel=e},t.protoInitialize=function(){var e=t.prototype;e.type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0}(),t}(At);Hd(Pi,At);So(Pi);iw(Pi);nw(Pi,_w);function _w(r){var t=[];return A(Pi.getClassesByMainType(r),function(e){t=t.concat(e.dependencies||e.prototype.dependencies||[])}),t=V(t,function(e){return Se(e).main}),r!=="dataset"&&at(t,"dataset")<=0&&t.unshift("dataset"),t}const ut=Pi;var Kp="";typeof navigator<"u"&&(Kp=navigator.platform||"");var ni="rgba(0, 0, 0, 0.2)";const Sw={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:ni,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:ni,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:ni,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:ni,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:ni,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:ni,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Kp.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var Qp=X(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),ne="original",Wt="arrayRows",ce="objectRows",Ce="keyedColumns",er="typedArray",Jp="unknown",Ee="column",Ri="row",St={Must:1,Might:2,Not:3},jp=yt();function ww(r){jp(r).datasetMap=X()}function bw(r,t,e){var i={},n=Ku(t);if(!n||!r)return i;var a=[],o=[],s=t.ecModel,l=jp(s).datasetMap,u=n.uid+"_"+e.seriesLayoutBy,f,h;r=r.slice(),A(r,function(y,p){var g=H(y)?y:r[p]={name:y};g.type==="ordinal"&&f==null&&(f=p,h=d(g)),i[g.name]=[]});var c=l.get(u)||l.set(u,{categoryWayDim:h,valueWayDim:0});A(r,function(y,p){var g=y.name,m=d(y);if(f==null){var _=c.valueWayDim;v(i[g],_,m),v(o,_,m),c.valueWayDim+=m}else if(f===p)v(i[g],0,m),v(a,0,m);else{var _=c.categoryWayDim;v(i[g],_,m),v(o,_,m),c.categoryWayDim+=m}});function v(y,p,g){for(var m=0;m<g;m++)y.push(p+m)}function d(y){var p=y.dimsDef;return p?p.length:1}return a.length&&(i.itemName=a),o.length&&(i.seriesName=o),i}function kA(r,t,e){var i={},n=Ku(r);if(!n)return i;var a=t.sourceFormat,o=t.dimensionsDefine,s;(a===ce||a===Ce)&&A(o,function(f,h){(H(f)?f.name:f)==="name"&&(s=h)});var l=function(){for(var f={},h={},c=[],v=0,d=Math.min(5,e);v<d;v++){var y=eg(t.data,a,t.seriesLayoutBy,o,t.startIndex,v);c.push(y);var p=y===St.Not;if(p&&f.v==null&&v!==s&&(f.v=v),(f.n==null||f.n===f.v||!p&&c[f.n]===St.Not)&&(f.n=v),g(f)&&c[f.n]!==St.Not)return f;p||(y===St.Might&&h.v==null&&v!==s&&(h.v=v),(h.n==null||h.n===h.v)&&(h.n=v))}function g(m){return m.v!=null&&m.n!=null}return g(f)?f:g(h)?h:null}();if(l){i.value=[l.v];var u=s??l.n;i.itemName=[u],i.seriesName=[u]}return i}function Ku(r){var t=r.get("data",!0);if(!t)return Vn(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},he).models[0]}function xw(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:Vn(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},he).models}function tg(r,t){return eg(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,t)}function eg(r,t,e,i,n,a){var o,s=5;if(Vt(r))return St.Not;var l,u;if(i){var f=i[a];H(f)?(l=f.name,u=f.type):z(f)&&(l=f)}if(u!=null)return u==="ordinal"?St.Must:St.Not;if(t===Wt){var h=r;if(e===Ri){for(var c=h[a],v=0;v<(c||[]).length&&v<s;v++)if((o=S(c[n+v]))!=null)return o}else for(var v=0;v<h.length&&v<s;v++){var d=h[n+v];if(d&&(o=S(d[a]))!=null)return o}}else if(t===ce){var y=r;if(!l)return St.Not;for(var v=0;v<y.length&&v<s;v++){var p=y[v];if(p&&(o=S(p[l]))!=null)return o}}else if(t===Ce){var g=r;if(!l)return St.Not;var c=g[l];if(!c||Vt(c))return St.Not;for(var v=0;v<c.length&&v<s;v++)if((o=S(c[v]))!=null)return o}else if(t===ne)for(var m=r,v=0;v<m.length&&v<s;v++){var p=m[v],_=Gn(p);if(!N(_))return St.Not;if((o=S(_[a]))!=null)return o}function S(b){var w=z(b);if(b!=null&&Number.isFinite(Number(b))&&b!=="")return w?St.Might:St.Not;if(w&&b!=="-")return St.Must}return St.Not}var Tw=X();function Cw(r,t,e){var i=Tw.get(t);if(!i)return e;var n=i(r);return n?e.concat(n):e}var tv=yt();yt();var Qu=function(){function r(){}return r.prototype.getColorFromPalette=function(t,e,i){var n=Rt(this.get("color",!0)),a=this.get("colorLayer",!0);return Mw(this,tv,n,a,t,e,i)},r.prototype.clearColorPalette=function(){Aw(this,tv)},r}();function Dw(r,t){for(var e=r.length,i=0;i<e;i++)if(r[i].length>t)return r[i];return r[e-1]}function Mw(r,t,e,i,n,a,o){a=a||r;var s=t(a),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(n))return u[n];var f=o==null||!i?e:Dw(i,o);if(f=f||e,!(!f||!f.length)){var h=f[l];return n&&(u[n]=h),s.paletteIdx=(l+1)%f.length,h}}function Aw(r,t){t(r).paletteIdx=0,t(r).paletteNameMap={}}var ca,Wi,ev,rv="\0_ec_inner",Lw=1,rg=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e,i,n,a,o,s){a=a||{},this.option=null,this._theme=new At(a),this._locale=new At(o),this._optionManager=s},t.prototype.setOption=function(e,i,n){var a=av(i);this._optionManager.setOption(e,n,a),this._resetOption(null,a)},t.prototype.resetOption=function(e,i){return this._resetOption(e,av(i))},t.prototype._resetOption=function(e,i){var n=!1,a=this._optionManager;if(!e||e==="recreate"){var o=a.mountOption(e==="recreate");!this.option||e==="recreate"?ev(this,o):(this.restoreData(),this._mergeOption(o,i)),n=!0}if((e==="timeline"||e==="media")&&this.restoreData(),!e||e==="recreate"||e==="timeline"){var s=a.getTimelineOption(this);s&&(n=!0,this._mergeOption(s,i))}if(!e||e==="recreate"||e==="media"){var l=a.getMediaOption(this);l.length&&A(l,function(u){n=!0,this._mergeOption(u,i)},this)}return n},t.prototype.mergeOption=function(e){this._mergeOption(e,null)},t.prototype._mergeOption=function(e,i){var n=this.option,a=this._componentsMap,o=this._componentsCount,s=[],l=X(),u=i&&i.replaceMergeMainTypeMap;ww(this),A(e,function(h,c){h!=null&&(ut.hasClass(c)?c&&(s.push(c),l.set(c,!0)):n[c]=n[c]==null?J(h):tt(n[c],h,!0))}),u&&u.each(function(h,c){ut.hasClass(c)&&!l.get(c)&&(s.push(c),l.set(c,!0))}),ut.topologicalTravel(s,ut.getAllClassMainTypes(),f,this);function f(h){var c=Cw(this,h,Rt(e[h])),v=a.get(h),d=v?u&&u.get(h)?"replaceMerge":"normalMerge":"replaceAll",y=v_(v,c,d);__(y,h,ut),n[h]=null,a.set(h,null),o.set(h,0);var p=[],g=[],m=0,_;A(y,function(S,b){var w=S.existing,x=S.newOption;if(!x)w&&(w.mergeOption({},this),w.optionUpdated({},!1));else{var C=h==="series",T=ut.getClass(h,S.keyInfo.subType,!C);if(!T)return;if(h==="tooltip"){if(_)return;_=!0}if(w&&w.constructor===T)w.name=S.keyInfo.name,w.mergeOption(x,this),w.optionUpdated(x,!1);else{var D=k({componentIndex:b},S.keyInfo);w=new T(x,this,this,D),k(w,D),S.brandNew&&(w.__requireNewView=!0),w.init(x,this,this),w.optionUpdated(null,!0)}}w?(p.push(w.option),g.push(w),m++):(p.push(void 0),g.push(void 0))},this),n[h]=p,a.set(h,g),o.set(h,m),h==="series"&&ca(this)}this._seriesIndices||ca(this)},t.prototype.getOption=function(){var e=J(this.option);return A(e,function(i,n){if(ut.hasClass(n)){for(var a=Rt(i),o=a.length,s=!1,l=o-1;l>=0;l--)a[l]&&!Dn(a[l])?s=!0:(a[l]=null,!s&&o--);a.length=o,e[n]=a}}),delete e[rv],e},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(e){this._payload=e},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(e,i){var n=this._componentsMap.get(e);if(n){var a=n[i||0];if(a)return a;if(i==null){for(var o=0;o<n.length;o++)if(n[o])return n[o]}}},t.prototype.queryComponents=function(e){var i=e.mainType;if(!i)return[];var n=e.index,a=e.id,o=e.name,s=this._componentsMap.get(i);if(!s||!s.length)return[];var l;return n!=null?(l=[],A(Rt(n),function(u){s[u]&&l.push(s[u])})):a!=null?l=iv("id",a,s):o!=null?l=iv("name",o,s):l=wt(s,function(u){return!!u}),nv(l,e)},t.prototype.findComponents=function(e){var i=e.query,n=e.mainType,a=s(i),o=a?this.queryComponents(a):wt(this._componentsMap.get(n),function(u){return!!u});return l(nv(o,e));function s(u){var f=n+"Index",h=n+"Id",c=n+"Name";return u&&(u[f]!=null||u[h]!=null||u[c]!=null)?{mainType:n,index:u[f],id:u[h],name:u[c]}:null}function l(u){return e.filter?wt(u,e.filter):u}},t.prototype.eachComponent=function(e,i,n){var a=this._componentsMap;if($(e)){var o=i,s=e;a.each(function(h,c){for(var v=0;h&&v<h.length;v++){var d=h[v];d&&s.call(o,c,d,d.componentIndex)}})}else for(var l=z(e)?a.get(e):H(e)?this.findComponents(e):null,u=0;l&&u<l.length;u++){var f=l[u];f&&i.call(n,f,f.componentIndex)}},t.prototype.getSeriesByName=function(e){var i=we(e,null);return wt(this._componentsMap.get("series"),function(n){return!!n&&i!=null&&n.name===i})},t.prototype.getSeriesByIndex=function(e){return this._componentsMap.get("series")[e]},t.prototype.getSeriesByType=function(e){return wt(this._componentsMap.get("series"),function(i){return!!i&&i.subType===e})},t.prototype.getSeries=function(){return wt(this._componentsMap.get("series"),function(e){return!!e})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(e,i){Wi(this),A(this._seriesIndices,function(n){var a=this._componentsMap.get("series")[n];e.call(i,a,n)},this)},t.prototype.eachRawSeries=function(e,i){A(this._componentsMap.get("series"),function(n){n&&e.call(i,n,n.componentIndex)})},t.prototype.eachSeriesByType=function(e,i,n){Wi(this),A(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===e&&i.call(n,o,a)},this)},t.prototype.eachRawSeriesByType=function(e,i,n){return A(this.getSeriesByType(e),i,n)},t.prototype.isSeriesFiltered=function(e){return Wi(this),this._seriesIndicesMap.get(e.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(e,i){Wi(this);var n=[];A(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];e.call(i,o,a)&&n.push(a)},this),this._seriesIndices=n,this._seriesIndicesMap=X(n)},t.prototype.restoreData=function(e){ca(this);var i=this._componentsMap,n=[];i.each(function(a,o){ut.hasClass(o)&&n.push(o)}),ut.topologicalTravel(n,ut.getAllClassMainTypes(),function(a){A(i.get(a),function(o){o&&(a!=="series"||!Iw(o,e))&&o.restoreData()})})},t.internalField=function(){ca=function(e){var i=e._seriesIndices=[];A(e._componentsMap.get("series"),function(n){n&&i.push(n.componentIndex)}),e._seriesIndicesMap=X(i)},Wi=function(e){},ev=function(e,i){e.option={},e.option[rv]=Lw,e._componentsMap=X({series:[]}),e._componentsCount=X();var n=i.aria;H(n)&&n.enabled==null&&(n.enabled=!0),Pw(i,e._theme.option),tt(i,Sw,!1),e._mergeOption(i,null)}}(),t}(At);function Iw(r,t){if(t){var e=t.seriesIndex,i=t.seriesId,n=t.seriesName;return e!=null&&r.componentIndex!==e||i!=null&&r.id!==i||n!=null&&r.name!==n}}function Pw(r,t){var e=r.color&&!r.colorLayer;A(t,function(i,n){n==="colorLayer"&&e||ut.hasClass(n)||(typeof i=="object"?r[n]=r[n]?tt(r[n],i,!1):J(i):r[n]==null&&(r[n]=i))})}function iv(r,t,e){if(N(t)){var i=X();return A(t,function(a){if(a!=null){var o=we(a,null);o!=null&&i.set(a,!0)}}),wt(e,function(a){return a&&i.get(a[r])})}else{var n=we(t,null);return wt(e,function(a){return a&&n!=null&&a[r]===n})}}function nv(r,t){return t.hasOwnProperty("subType")?wt(r,function(e){return e&&e.subType===t.subType}):r}function av(r){var t=X();return r&&A(Rt(r.replaceMerge),function(e){t.set(e,!0)}),{replaceMergeMainTypeMap:t}}xe(rg,Qu);const ig=rg;var Rw=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],Ew=function(){function r(t){A(Rw,function(e){this[e]=ft(t[e],t)},this)}return r}();const ng=Ew;var Rs={},kw=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(t,e){var i=[];A(Rs,function(n,a){var o=n.create(t,e);i=i.concat(o||[])}),this._coordinateSystems=i},r.prototype.update=function(t,e){A(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(t,e){Rs[t]=e},r.get=function(t){return Rs[t]},r}();const Ju=kw;var Ow=/^(min|max)?(.+)$/,Bw=function(){function r(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return r.prototype.setOption=function(t,e,i){t&&(A(Rt(t.series),function(o){o&&o.data&&Vt(o.data)&&ll(o.data)}),A(Rt(t.dataset),function(o){o&&o.source&&Vt(o.source)&&ll(o.source)})),t=J(t);var n=this._optionBackup,a=Nw(t,e,!n);this._newBaseOption=a.baseOption,n?(a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},r.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],J(t?e.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=J(i[n.getCurrentIndex()]))}return e},r.prototype.getMediaOption=function(t){var e=this._api.getWidth(),i=this._api.getHeight(),n=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!n.length&&!a)return s;for(var l=0,u=n.length;l<u;l++)Fw(n[l].query,e,i)&&o.push(l);return!o.length&&a&&(o=[-1]),o.length&&!Hw(o,this._currentMediaIndices)&&(s=V(o,function(f){return J(f===-1?a.option:n[f].option)})),this._currentMediaIndices=o,s},r}();function Nw(r,t,e){var i=[],n,a,o=r.baseOption,s=r.timeline,l=r.options,u=r.media,f=!!r.media,h=!!(l||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(r.options=r.media=null),a=r),f&&N(u)&&A(u,function(v){v&&v.option&&(v.query?i.push(v):n||(n=v))}),c(a),A(l,function(v){return c(v)}),A(i,function(v){return c(v.option)});function c(v){A(t,function(d){d(v,e)})}return{baseOption:a,timelineOptions:l||[],mediaDefault:n,mediaList:i}}function Fw(r,t,e){var i={width:t,height:e,aspectratio:t/e},n=!0;return A(r,function(a,o){var s=o.match(Ow);if(!(!s||!s[1]||!s[2])){var l=s[1],u=s[2].toLowerCase();zw(i[u],a,l)||(n=!1)}}),n}function zw(r,t,e){return e==="min"?r>=t:e==="max"?r<=t:r===t}function Hw(r,t){return r.join(",")===t.join(",")}const Gw=Bw;var oe=A,Rn=H,ov=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Es(r){var t=r&&r.itemStyle;if(t)for(var e=0,i=ov.length;e<i;e++){var n=ov[e],a=t.normal,o=t.emphasis;a&&a[n]&&(r[n]=r[n]||{},r[n].normal?tt(r[n].normal,a[n]):r[n].normal=a[n],a[n]=null),o&&o[n]&&(r[n]=r[n]||{},r[n].emphasis?tt(r[n].emphasis,o[n]):r[n].emphasis=o[n],o[n]=null)}}function Mt(r,t,e){if(r&&r[t]&&(r[t].normal||r[t].emphasis)){var i=r[t].normal,n=r[t].emphasis;i&&(e?(r[t].normal=r[t].emphasis=null,it(r[t],i)):r[t]=i),n&&(r.emphasis=r.emphasis||{},r.emphasis[t]=n,n.focus&&(r.emphasis.focus=n.focus),n.blurScope&&(r.emphasis.blurScope=n.blurScope))}}function ln(r){Mt(r,"itemStyle"),Mt(r,"lineStyle"),Mt(r,"areaStyle"),Mt(r,"label"),Mt(r,"labelLine"),Mt(r,"upperLabel"),Mt(r,"edgeLabel")}function dt(r,t){var e=Rn(r)&&r[t],i=Rn(e)&&e.textStyle;if(i)for(var n=0,a=oh.length;n<a;n++){var o=oh[n];i.hasOwnProperty(o)&&(e[o]=i[o])}}function Qt(r){r&&(ln(r),dt(r,"label"),r.emphasis&&dt(r.emphasis,"label"))}function Vw(r){if(Rn(r)){Es(r),ln(r),dt(r,"label"),dt(r,"upperLabel"),dt(r,"edgeLabel"),r.emphasis&&(dt(r.emphasis,"label"),dt(r.emphasis,"upperLabel"),dt(r.emphasis,"edgeLabel"));var t=r.markPoint;t&&(Es(t),Qt(t));var e=r.markLine;e&&(Es(e),Qt(e));var i=r.markArea;i&&Qt(i);var n=r.data;if(r.type==="graph"){n=n||r.nodes;var a=r.links||r.edges;if(a&&!Vt(a))for(var o=0;o<a.length;o++)Qt(a[o]);A(r.categories,function(u){ln(u)})}if(n&&!Vt(n))for(var o=0;o<n.length;o++)Qt(n[o]);if(t=r.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)Qt(s[o]);if(e=r.markLine,e&&e.data)for(var l=e.data,o=0;o<l.length;o++)N(l[o])?(Qt(l[o][0]),Qt(l[o][1])):Qt(l[o]);r.type==="gauge"?(dt(r,"axisLabel"),dt(r,"title"),dt(r,"detail")):r.type==="treemap"?(Mt(r.breadcrumb,"itemStyle"),A(r.levels,function(u){ln(u)})):r.type==="tree"&&ln(r.leaves)}}function Me(r){return N(r)?r:r?[r]:[]}function sv(r){return(N(r)?r[0]:r)||{}}function Ww(r,t){oe(Me(r.series),function(i){Rn(i)&&Vw(i)});var e=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&e.push("valueAxis","categoryAxis","logAxis","timeAxis"),oe(e,function(i){oe(Me(r[i]),function(n){n&&(dt(n,"axisLabel"),dt(n.axisPointer,"label"))})}),oe(Me(r.parallel),function(i){var n=i&&i.parallelAxisDefault;dt(n,"axisLabel"),dt(n&&n.axisPointer,"label")}),oe(Me(r.calendar),function(i){Mt(i,"itemStyle"),dt(i,"dayLabel"),dt(i,"monthLabel"),dt(i,"yearLabel")}),oe(Me(r.radar),function(i){dt(i,"name"),i.name&&i.axisName==null&&(i.axisName=i.name,delete i.name),i.nameGap!=null&&i.axisNameGap==null&&(i.axisNameGap=i.nameGap,delete i.nameGap)}),oe(Me(r.geo),function(i){Rn(i)&&(Qt(i),oe(Me(i.regions),function(n){Qt(n)}))}),oe(Me(r.timeline),function(i){Qt(i),Mt(i,"label"),Mt(i,"itemStyle"),Mt(i,"controlStyle",!0);var n=i.data;N(n)&&A(n,function(a){H(a)&&(Mt(a,"label"),Mt(a,"itemStyle"))})}),oe(Me(r.toolbox),function(i){Mt(i,"iconStyle"),oe(i.feature,function(n){Mt(n,"iconStyle")})}),dt(sv(r.axisPointer),"label"),dt(sv(r.tooltip).axisPointer,"label")}function Uw(r,t){for(var e=t.split(","),i=r,n=0;n<e.length&&(i=i&&i[e[n]],i!=null);n++);return i}function $w(r,t,e,i){for(var n=t.split(","),a=r,o,s=0;s<n.length-1;s++)o=n[s],a[o]==null&&(a[o]={}),a=a[o];(i||a[n[s]]==null)&&(a[n[s]]=e)}function lv(r){r&&A(Yw,function(t){t[0]in r&&!(t[1]in r)&&(r[t[1]]=r[t[0]])})}var Yw=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Xw=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],ks=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Ui(r){var t=r&&r.itemStyle;if(t)for(var e=0;e<ks.length;e++){var i=ks[e][1],n=ks[e][0];t[i]!=null&&(t[n]=t[i])}}function uv(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function fv(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function Zw(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function ag(r,t){if(r)for(var e=0;e<r.length;e++)t(r[e]),r[e]&&ag(r[e].children,t)}function og(r,t){Ww(r,t),r.series=Rt(r.series),A(r.series,function(e){if(H(e)){var i=e.type;if(i==="line")e.clipOverflow!=null&&(e.clip=e.clipOverflow);else if(i==="pie"||i==="gauge"){e.clockWise!=null&&(e.clockwise=e.clockWise),uv(e.label);var n=e.data;if(n&&!Vt(n))for(var a=0;a<n.length;a++)uv(n[a]);e.hoverOffset!=null&&(e.emphasis=e.emphasis||{},(e.emphasis.scaleSize=null)&&(e.emphasis.scaleSize=e.hoverOffset))}else if(i==="gauge"){var o=Uw(e,"pointer.color");o!=null&&$w(e,"itemStyle.color",o)}else if(i==="bar"){Ui(e),Ui(e.backgroundStyle),Ui(e.emphasis);var n=e.data;if(n&&!Vt(n))for(var a=0;a<n.length;a++)typeof n[a]=="object"&&(Ui(n[a]),Ui(n[a]&&n[a].emphasis))}else if(i==="sunburst"){var s=e.highlightPolicy;s&&(e.emphasis=e.emphasis||{},e.emphasis.focus||(e.emphasis.focus=s)),fv(e),ag(e.data,fv)}else i==="graph"||i==="sankey"?Zw(e):i==="map"&&(e.mapType&&!e.map&&(e.map=e.mapType),e.mapLocation&&it(e,e.mapLocation));e.hoverAnimation!=null&&(e.emphasis=e.emphasis||{},e.emphasis&&e.emphasis.scale==null&&(e.emphasis.scale=e.hoverAnimation)),lv(e)}}),r.dataRange&&(r.visualMap=r.dataRange),A(Xw,function(e){var i=r[e];i&&(N(i)||(i=[i]),A(i,function(n){lv(n)}))})}function qw(r){var t=X();r.eachSeries(function(e){var i=e.get("stack");if(i){var n=t.get(i)||t.set(i,[]),a=e.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:e};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;n.length&&a.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(o)}}),t.each(Kw)}function Kw(r){A(r,function(t,e){var i=[],n=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,l=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(u,f,h){var c=o.get(t.stackedDimension,h);if(isNaN(c))return n;var v,d;s?d=o.getRawIndex(h):v=o.get(t.stackedByDimension,h);for(var y=NaN,p=e-1;p>=0;p--){var g=r[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,v)),d>=0){var m=g.data.getByRawIndex(g.stackResultDimension,d);if(l==="all"||l==="positive"&&m>0||l==="negative"&&m<0||l==="samesign"&&c>=0&&m>0||l==="samesign"&&c<=0&&m<0){c=o_(c,m),y=m;break}}}return i[0]=c,i[1]=y,i})})}var Bo=function(){function r(t){this.data=t.data||(t.sourceFormat===Ce?{}:[]),this.sourceFormat=t.sourceFormat||Jp,this.seriesLayoutBy=t.seriesLayoutBy||Ee,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var i=0;i<e.length;i++){var n=e[i];n.type==null&&tg(this,i)===St.Must&&(n.type="ordinal")}}return r}();function ju(r){return r instanceof Bo}function Fl(r,t,e){e=e||sg(r);var i=t.seriesLayoutBy,n=Jw(r,e,i,t.sourceHeader,t.dimensions),a=new Bo({data:r,sourceFormat:e,seriesLayoutBy:i,dimensionsDefine:n.dimensionsDefine,startIndex:n.startIndex,dimensionsDetectedCount:n.dimensionsDetectedCount,metaRawOption:J(t)});return a}function tf(r){return new Bo({data:r,sourceFormat:Vt(r)?er:ne})}function Qw(r){return new Bo({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:J(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function sg(r){var t=Jp;if(Vt(r))t=er;else if(N(r)){r.length===0&&(t=Wt);for(var e=0,i=r.length;e<i;e++){var n=r[e];if(n!=null){if(N(n)||Vt(n)){t=Wt;break}else if(H(n)){t=ce;break}}}}else if(H(r)){for(var a in r)if(zr(r,a)&&Gt(r[a])){t=Ce;break}}return t}function Jw(r,t,e,i,n){var a,o;if(!r)return{dimensionsDefine:hv(n),startIndex:o,dimensionsDetectedCount:a};if(t===Wt){var s=r;i==="auto"||i==null?vv(function(u){u!=null&&u!=="-"&&(z(u)?o==null&&(o=1):o=0)},e,s,10):o=vt(i)?i:i?1:0,!n&&o===1&&(n=[],vv(function(u,f){n[f]=u!=null?u+"":""},e,s,1/0)),a=n?n.length:e===Ri?s.length:s[0]?s[0].length:null}else if(t===ce)n||(n=jw(r));else if(t===Ce)n||(n=[],A(r,function(u,f){n.push(f)}));else if(t===ne){var l=Gn(r[0]);a=N(l)&&l.length||1}return{startIndex:o,dimensionsDefine:hv(n),dimensionsDetectedCount:a}}function jw(r){for(var t=0,e;t<r.length&&!(e=r[t++]););if(e)return ht(e)}function hv(r){if(r){var t=X();return V(r,function(e,i){e=H(e)?e:{name:e};var n={name:e.name,displayName:e.displayName,type:e.type};if(n.name==null)return n;n.name+="",n.displayName==null&&(n.displayName=n.name);var a=t.get(n.name);return a?n.name+="-"+a.count++:t.set(n.name,{count:1}),n})}}function vv(r,t,e,i){if(t===Ri)for(var n=0;n<e.length&&n<i;n++)r(e[n]?e[n][0]:null,n);else for(var a=e[0]||[],n=0;n<a.length&&n<i;n++)r(a[n],n)}function lg(r){var t=r.sourceFormat;return t===ce||t===Ce}var Cr,Dr,Mr,cv,dv,ug=function(){function r(t,e){var i=ju(t)?t:tf(t);this._source=i;var n=this._data=i.data;i.sourceFormat===er&&(this._offset=0,this._dimSize=e,this._data=n),dv(this,n,i)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(t,e){},r.prototype.appendData=function(t){},r.prototype.clean=function(){},r.protoInitialize=function(){var t=r.prototype;t.pure=!1,t.persistent=!0}(),r.internalField=function(){var t;dv=function(o,s,l){var u=l.sourceFormat,f=l.seriesLayoutBy,h=l.startIndex,c=l.dimensionsDefine,v=cv[ef(u,f)];if(k(o,v),u===er)o.getItem=e,o.count=n,o.fillStorage=i;else{var d=fg(u,f);o.getItem=ft(d,null,s,h,c);var y=hg(u,f);o.count=ft(y,null,s,h,c)}};var e=function(o,s){o=o-this._offset,s=s||[];for(var l=this._data,u=this._dimSize,f=u*o,h=0;h<u;h++)s[h]=l[f+h];return s},i=function(o,s,l,u){for(var f=this._data,h=this._dimSize,c=0;c<h;c++){for(var v=u[c],d=v[0]==null?1/0:v[0],y=v[1]==null?-1/0:v[1],p=s-o,g=l[c],m=0;m<p;m++){var _=f[m*h+c];g[o+m]=_,_<d&&(d=_),_>y&&(y=_)}v[0]=d,v[1]=y}},n=function(){return this._data?this._data.length/this._dimSize:0};cv=(t={},t[Wt+"_"+Ee]={pure:!0,appendData:a},t[Wt+"_"+Ri]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[ce]={pure:!0,appendData:a},t[Ce]={pure:!0,appendData:function(o){var s=this._data;A(o,function(l,u){for(var f=s[u]||(s[u]=[]),h=0;h<(l||[]).length;h++)f.push(l[h])})}},t[ne]={appendData:a},t[er]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),pv=function(r,t,e,i){return r[i]},tb=(Cr={},Cr[Wt+"_"+Ee]=function(r,t,e,i){return r[i+t]},Cr[Wt+"_"+Ri]=function(r,t,e,i,n){i+=t;for(var a=n||[],o=r,s=0;s<o.length;s++){var l=o[s];a[s]=l?l[i]:null}return a},Cr[ce]=pv,Cr[Ce]=function(r,t,e,i,n){for(var a=n||[],o=0;o<e.length;o++){var s=e[o].name,l=r[s];a[o]=l?l[i]:null}return a},Cr[ne]=pv,Cr);function fg(r,t){var e=tb[ef(r,t)];return e}var gv=function(r,t,e){return r.length},eb=(Dr={},Dr[Wt+"_"+Ee]=function(r,t,e){return Math.max(0,r.length-t)},Dr[Wt+"_"+Ri]=function(r,t,e){var i=r[0];return i?Math.max(0,i.length-t):0},Dr[ce]=gv,Dr[Ce]=function(r,t,e){var i=e[0].name,n=r[i];return n?n.length:0},Dr[ne]=gv,Dr);function hg(r,t){var e=eb[ef(r,t)];return e}var Os=function(r,t,e){return r[t]},rb=(Mr={},Mr[Wt]=Os,Mr[ce]=function(r,t,e){return r[e]},Mr[Ce]=Os,Mr[ne]=function(r,t,e){var i=Gn(r);return i instanceof Array?i[t]:i},Mr[er]=Os,Mr);function vg(r){var t=rb[r];return t}function ef(r,t){return r===Wt?r+"_"+t:r}function Di(r,t,e){if(r){var i=r.getRawDataItem(t);if(i!=null){var n=r.getStore(),a=n.getSource().sourceFormat;if(e!=null){var o=r.getDimensionIndex(e),s=n.getDimensionProperty(o);return vg(a)(i,o,s)}else{var l=i;return a===ne&&(l=Gn(i)),l}}}}var ib=/\{@(.+?)\}/g,nb=function(){function r(){}return r.prototype.getDataParams=function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),a=i.getRawIndex(t),o=i.getName(t),s=i.getRawDataItem(t),l=i.getItemVisual(t,"style"),u=l&&l[i.getItemVisual(t,"drawType")||"fill"],f=l&&l.stroke,h=this.mainType,c=h==="series",v=i.userOutput&&i.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:a,data:s,dataType:e,value:n,color:u,borderColor:f,dimensionNames:v?v.fullDimensions:null,encode:v?v.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(t,e,i,n,a,o){e=e||"normal";var s=this.getData(i),l=this.getDataParams(t,i);if(o&&(l.value=o.interpolatedValue),n!=null&&N(l.value)&&(l.value=l.value[n]),!a){var u=s.getItemModel(t);a=u.get(e==="normal"?["label","formatter"]:[e,"label","formatter"])}if($(a))return l.status=e,l.dimensionIndex=n,a(l);if(z(a)){var f=qp(a,l);return f.replace(ib,function(h,c){var v=c.length,d=c;d.charAt(0)==="["&&d.charAt(v-1)==="]"&&(d=+d.slice(1,v-1));var y=Di(s,t,d);if(o&&N(o.interpolatedValue)){var p=s.getDimensionIndex(d);p>=0&&(y=o.interpolatedValue[p])}return y!=null?y+"":""})}},r.prototype.getRawValue=function(t,e){return Di(this.getData(e),t)},r.prototype.formatTooltip=function(t,e,i){},r}();function yv(r){var t,e;return H(r)?r.type&&(e=r):t=r,{text:t,frag:e}}function _n(r){return new ab(r)}var ab=function(){function r(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return r.prototype.perform=function(t){var e=this._upstream,i=t&&t.skip;if(this._dirty&&e){var n=this.context;n.data=n.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!i&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,l=f(t&&t.modBy),u=t&&t.modDataCount||0;(o!==l||s!==u)&&(a="reset");function f(m){return!(m>=1)&&(m=1),m}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(e?this._dueEnd=e._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var v=this._dueIndex,d=Math.min(c!=null?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(h||v<d)){var y=this._progress;if(N(y))for(var p=0;p<y.length;p++)this._doProgress(y[p],v,d,l,u);else this._doProgress(y,v,d,l,u)}this._dueIndex=d;var g=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=g}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(t,e,i,n,a){mv.reset(e,i,n,a),this._callingProgress=t,this._callingProgress({start:e,end:i,count:i-e,next:mv.next},this.context)},r.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var e,i;!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(i=e.forceFirstProgress,e=e.progress),N(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var n=this._downstream;return n&&n.dirty(),i},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},r}(),mv=function(){var r,t,e,i,n,a={reset:function(l,u,f,h){t=l,r=u,e=f,i=h,n=Math.ceil(i/e),a.next=e>1&&i>0?s:o}};return a;function o(){return t<r?t++:null}function s(){var l=t%n*e+Math.ceil(t/n),u=t>=r?null:l<i?l:t;return t++,u}}();function Wa(r,t){var e=t&&t.type;return e==="ordinal"?r:(e==="time"&&!vt(r)&&r!=null&&r!=="-"&&(r=+Oe(r)),r==null||r===""?NaN:Number(r))}X({number:function(r){return parseFloat(r)},time:function(r){return+Oe(r)},trim:function(r){return z(r)?_e(r):r}});var ob=function(){function r(t,e){var i=t==="desc";this._resultLT=i?1:-1,e==null&&(e=i?"min":"max"),this._incomparable=e==="min"?-1/0:1/0}return r.prototype.evaluate=function(t,e){var i=vt(t)?t:eo(t),n=vt(e)?e:eo(e),a=isNaN(i),o=isNaN(n);if(a&&(i=this._incomparable),o&&(n=this._incomparable),a&&o){var s=z(t),l=z(e);s&&(i=l?t:0),l&&(n=s?e:0)}return i<n?this._resultLT:i>n?-this._resultLT:0},r}(),sb=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(t){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(t){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(t,e){},r.prototype.retrieveValueFromItem=function(t,e){},r.prototype.convertValue=function(t,e){return Wa(t,e)},r}();function lb(r,t){var e=new sb,i=r.data,n=e.sourceFormat=r.sourceFormat,a=r.startIndex,o="";r.seriesLayoutBy!==Ee&&Ft(o);var s=[],l={},u=r.dimensionsDefine;if(u)A(u,function(y,p){var g=y.name,m={index:p,name:g,displayName:y.displayName};if(s.push(m),g!=null){var _="";zr(l,g)&&Ft(_),l[g]=m}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var h=fg(n,Ee);t.__isBuiltIn&&(e.getRawDataItem=function(y){return h(i,a,s,y)},e.getRawData=ft(ub,null,r)),e.cloneRawData=ft(fb,null,r);var c=hg(n,Ee);e.count=ft(c,null,i,a,s);var v=vg(n);e.retrieveValue=function(y,p){var g=h(i,a,s,y);return d(g,p)};var d=e.retrieveValueFromItem=function(y,p){if(y!=null){var g=s[p];if(g)return v(y,p,g.name)}};return e.getDimensionInfo=ft(hb,null,s,l),e.cloneAllDimensionInfo=ft(vb,null,s),e}function ub(r){var t=r.sourceFormat;if(!rf(t)){var e="";Ft(e)}return r.data}function fb(r){var t=r.sourceFormat,e=r.data;if(!rf(t)){var i="";Ft(i)}if(t===Wt){for(var n=[],a=0,o=e.length;a<o;a++)n.push(e[a].slice());return n}else if(t===ce){for(var n=[],a=0,o=e.length;a<o;a++)n.push(k({},e[a]));return n}}function hb(r,t,e){if(e!=null){if(vt(e)||!isNaN(e)&&!zr(t,e))return r[e];if(zr(t,e))return t[e]}}function vb(r){return J(r)}var cg=X();function cb(r){r=J(r);var t=r.type,e="";t||Ft(e);var i=t.split(":");i.length!==2&&Ft(e);var n=!1;i[0]==="echarts"&&(t=i[1],n=!0),r.__isBuiltIn=n,cg.set(t,r)}function db(r,t,e){var i=Rt(r),n=i.length,a="";n||Ft(a);for(var o=0,s=n;o<s;o++){var l=i[o];t=pb(l,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function pb(r,t,e,i){var n="";t.length||Ft(n),H(r)||Ft(n);var a=r.type,o=cg.get(a);o||Ft(n);var s=V(t,function(u){return lb(u,o)}),l=Rt(o.transform({upstream:s[0],upstreamList:s,config:J(r.config)}));return V(l,function(u,f){var h="";H(u)||Ft(h),u.data||Ft(h);var c=sg(u.data);rf(c)||Ft(h);var v,d=t[0];if(d&&f===0&&!u.dimensions){var y=d.startIndex;y&&(u.data=d.data.slice(0,y).concat(u.data)),v={seriesLayoutBy:Ee,sourceHeader:y,dimensions:d.metaRawOption.dimensions}}else v={seriesLayoutBy:Ee,sourceHeader:0,dimensions:u.dimensions};return Fl(u.data,v,null)})}function rf(r){return r===Wt||r===ce}var No="undefined",gb=typeof Uint32Array===No?Array:Uint32Array,yb=typeof Uint16Array===No?Array:Uint16Array,dg=typeof Int32Array===No?Array:Int32Array,_v=typeof Float64Array===No?Array:Float64Array,pg={float:_v,int:dg,ordinal:Array,number:Array,time:_v},Bs;function ai(r){return r>65535?gb:yb}function oi(){return[1/0,-1/0]}function mb(r){var t=r.constructor;return t===Array?r.slice():new t(r)}function Sv(r,t,e,i,n){var a=pg[e||"float"];if(n){var o=r[t],s=o&&o.length;if(s!==i){for(var l=new a(i),u=0;u<s;u++)l[u]=o[u];r[t]=l}}else r[t]=new a(i)}var zl=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=X()}return r.prototype.initData=function(t,e,i){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var n=t.getSource(),a=this.defaultDimValueGetter=Bs[n.sourceFormat];this._dimValueGetter=i||a,this._rawExtent=[],lg(n),this._dimensions=V(e,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(t,e){var i=this._calcDimNameToIdx,n=this._dimensions,a=i.get(t);if(a!=null){if(n[a].type===e)return a}else a=n.length;return n[a]={type:e},i.set(t,a),this._chunks[a]=new pg[e||"float"](this._rawCount),this._rawExtent[a]=oi(),a},r.prototype.collectOrdinalMeta=function(t,e){var i=this._chunks[t],n=this._dimensions[t],a=this._rawExtent,o=n.ordinalOffset||0,s=i.length;o===0&&(a[t]=oi());for(var l=a[t],u=o;u<s;u++){var f=i[u]=e.parseAndCollect(i[u]);isNaN(f)||(l[0]=Math.min(f,l[0]),l[1]=Math.max(f,l[1]))}n.ordinalMeta=e,n.ordinalOffset=s,n.type="ordinal"},r.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],i=e.ordinalMeta;return i},r.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},r.prototype.appendData=function(t){var e=this._provider,i=this.count();e.appendData(t);var n=e.count();return e.persistent||(n+=i),i<n&&this._initDataFromProvider(i,n,!0),[i,n]},r.prototype.appendValues=function(t,e){for(var i=this._chunks,n=this._dimensions,a=n.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e||0),u=0;u<a;u++){var f=n[u];Sv(i,u,f.type,l,!0)}for(var h=[],c=s;c<l;c++)for(var v=c-s,d=0;d<a;d++){var f=n[d],y=Bs.arrayRows.call(this,t[v]||h,f.property,v,d);i[d][c]=y;var p=o[d];y<p[0]&&(p[0]=y),y>p[1]&&(p[1]=y)}return this._rawCount=this._count=l,{start:s,end:l}},r.prototype._initDataFromProvider=function(t,e,i){for(var n=this._provider,a=this._chunks,o=this._dimensions,s=o.length,l=this._rawExtent,u=V(o,function(m){return m.property}),f=0;f<s;f++){var h=o[f];l[f]||(l[f]=oi()),Sv(a,f,h.type,e,i)}if(n.fillStorage)n.fillStorage(t,e,a,l);else for(var c=[],v=t;v<e;v++){c=n.getItem(v,c);for(var d=0;d<s;d++){var y=a[d],p=this._dimValueGetter(c,u[d],v,d);y[v]=p;var g=l[d];p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}!n.persistent&&n.clean&&n.clean(),this._rawCount=this._count=e,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var i=this._chunks[t];return i?i[this.getRawIndex(e)]:NaN},r.prototype.getValues=function(t,e){var i=[],n=[];if(e==null){e=t,t=[];for(var a=0;a<this._dimensions.length;a++)n.push(a)}else n=t;for(var a=0,o=n.length;a<o;a++)i.push(this.get(n[a],e));return i},r.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var i=this._chunks[t];return i?i[e]:NaN},r.prototype.getSum=function(t){var e=this._chunks[t],i=0;if(e)for(var n=0,a=this.count();n<a;n++){var o=this.get(t,n);isNaN(o)||(i+=o)}return i},r.prototype.getMedian=function(t){var e=[];this.each([t],function(a){isNaN(a)||e.push(a)});var i=e.sort(function(a,o){return a-o}),n=this.count();return n===0?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},r.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(i!=null&&i<this._count&&i===t)return t;for(var n=0,a=this._count-1;n<=a;){var o=(n+a)/2|0;if(e[o]<t)n=o+1;else if(e[o]>t)a=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(t,e,i){var n=this._chunks,a=n[t],o=[];if(!a)return o;i==null&&(i=1/0);for(var s=1/0,l=-1,u=0,f=0,h=this.count();f<h;f++){var c=this.getRawIndex(f),v=e-a[c],d=Math.abs(v);d<=i&&((d<s||d===s&&v>=0&&l<0)&&(s=d,l=v,u=0),v===l&&(o[u++]=f))}return o.length=u,o},r.prototype.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var a=0;a<n;a++)t[a]=e[a]}else t=new i(e.buffer,0,n)}else{var i=ai(this._rawCount);t=new i(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},r.prototype.filter=function(t,e){if(!this._count)return this;for(var i=this.clone(),n=i.count(),a=ai(i._rawCount),o=new a(n),s=[],l=t.length,u=0,f=t[0],h=i._chunks,c=0;c<n;c++){var v=void 0,d=i.getRawIndex(c);if(l===0)v=e(c);else if(l===1){var y=h[f][d];v=e(y,c)}else{for(var p=0;p<l;p++)s[p]=h[t[p]][d];s[p]=c,v=e.apply(null,s)}v&&(o[u++]=d)}return u<n&&(i._indices=o),i._count=u,i._extent=[],i._updateGetRawIdx(),i},r.prototype.selectRange=function(t){var e=this.clone(),i=e._count;if(!i)return this;var n=ht(t),a=n.length;if(!a)return this;var o=e.count(),s=ai(e._rawCount),l=new s(o),u=0,f=n[0],h=t[f][0],c=t[f][1],v=e._chunks,d=!1;if(!e._indices){var y=0;if(a===1){for(var p=v[n[0]],g=0;g<i;g++){var m=p[g];(m>=h&&m<=c||isNaN(m))&&(l[u++]=y),y++}d=!0}else if(a===2){for(var p=v[n[0]],_=v[n[1]],S=t[n[1]][0],b=t[n[1]][1],g=0;g<i;g++){var m=p[g],w=_[g];(m>=h&&m<=c||isNaN(m))&&(w>=S&&w<=b||isNaN(w))&&(l[u++]=y),y++}d=!0}}if(!d)if(a===1)for(var g=0;g<o;g++){var x=e.getRawIndex(g),m=v[n[0]][x];(m>=h&&m<=c||isNaN(m))&&(l[u++]=x)}else for(var g=0;g<o;g++){for(var C=!0,x=e.getRawIndex(g),T=0;T<a;T++){var D=n[T],m=v[D][x];(m<t[D][0]||m>t[D][1])&&(C=!1)}C&&(l[u++]=e.getRawIndex(g))}return u<o&&(e._indices=l),e._count=u,e._extent=[],e._updateGetRawIdx(),e},r.prototype.map=function(t,e){var i=this.clone(t);return this._updateDims(i,t,e),i},r.prototype.modify=function(t,e){this._updateDims(this,t,e)},r.prototype._updateDims=function(t,e,i){for(var n=t._chunks,a=[],o=e.length,s=t.count(),l=[],u=t._rawExtent,f=0;f<e.length;f++)u[e[f]]=oi();for(var h=0;h<s;h++){for(var c=t.getRawIndex(h),v=0;v<o;v++)l[v]=n[e[v]][c];l[o]=h;var d=i&&i.apply(null,l);if(d!=null){typeof d!="object"&&(a[0]=d,d=a);for(var f=0;f<d.length;f++){var y=e[f],p=d[f],g=u[y],m=n[y];m&&(m[c]=p),p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}}},r.prototype.lttbDownSample=function(t,e){var i=this.clone([t],!0),n=i._chunks,a=n[t],o=this.count(),s=0,l=Math.floor(1/e),u=this.getRawIndex(0),f,h,c,v=new(ai(this._rawCount))(Math.min((Math.ceil(o/l)+2)*2,o));v[s++]=u;for(var d=1;d<o-1;d+=l){for(var y=Math.min(d+l,o-1),p=Math.min(d+l*2,o),g=(p+y)/2,m=0,_=y;_<p;_++){var S=this.getRawIndex(_),b=a[S];isNaN(b)||(m+=b)}m/=p-y;var w=d,x=Math.min(d+l,o),C=d-1,T=a[u];f=-1,c=w;for(var D=-1,M=0,_=w;_<x;_++){var S=this.getRawIndex(_),b=a[S];if(isNaN(b)){M++,D<0&&(D=S);continue}h=Math.abs((C-g)*(b-T)-(C-_)*(m-T)),h>f&&(f=h,c=S)}M>0&&M<x-w&&(v[s++]=Math.min(D,c),c=Math.max(D,c)),v[s++]=c,u=c}return v[s++]=this.getRawIndex(o-1),i._count=s,i._indices=v,i.getRawIndex=this._getRawIdx,i},r.prototype.minmaxDownSample=function(t,e){for(var i=this.clone([t],!0),n=i._chunks,a=Math.floor(1/e),o=n[t],s=this.count(),l=new(ai(this._rawCount))(Math.ceil(s/a)*2),u=0,f=0;f<s;f+=a){var h=f,c=o[this.getRawIndex(h)],v=f,d=o[this.getRawIndex(v)],y=a;f+a>s&&(y=s-f);for(var p=0;p<y;p++){var g=this.getRawIndex(f+p),m=o[g];m<c&&(c=m,h=f+p),m>d&&(d=m,v=f+p)}var _=this.getRawIndex(h),S=this.getRawIndex(v);h<v?(l[u++]=_,l[u++]=S):(l[u++]=S,l[u++]=_)}return i._count=u,i._indices=l,i._updateGetRawIdx(),i},r.prototype.downSample=function(t,e,i,n){for(var a=this.clone([t],!0),o=a._chunks,s=[],l=Math.floor(1/e),u=o[t],f=this.count(),h=a._rawExtent[t]=oi(),c=new(ai(this._rawCount))(Math.ceil(f/l)),v=0,d=0;d<f;d+=l){l>f-d&&(l=f-d,s.length=l);for(var y=0;y<l;y++){var p=this.getRawIndex(d+y);s[y]=u[p]}var g=i(s),m=this.getRawIndex(Math.min(d+n(s,g)||0,f-1));u[m]=g,g<h[0]&&(h[0]=g),g>h[1]&&(h[1]=g),c[v++]=m}return a._count=v,a._indices=c,a._updateGetRawIdx(),a},r.prototype.each=function(t,e){if(this._count)for(var i=t.length,n=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(i){case 0:e(a);break;case 1:e(n[t[0]][s],a);break;case 2:e(n[t[0]][s],n[t[1]][s],a);break;default:for(var l=0,u=[];l<i;l++)u[l]=n[t[l]][s];u[l]=a,e.apply(null,u)}}},r.prototype.getDataExtent=function(t){var e=this._chunks[t],i=oi();if(!e)return i;var n=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=i;for(var s=o[0],l=o[1],u=0;u<n;u++){var f=this.getRawIndex(u),h=e[f];h<s&&(s=h),h>l&&(l=h)}return o=[s,l],this._extent[t]=o,o},r.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var i=[],n=this._chunks,a=0;a<n.length;a++)i.push(n[a][e]);return i},r.prototype.clone=function(t,e){var i=new r,n=this._chunks,a=t&&ir(t,function(s,l){return s[l]=!0,s},{});if(a)for(var o=0;o<n.length;o++)i._chunks[o]=a[o]?mb(n[o]):n[o];else i._chunks=n;return this._copyCommonProps(i),e||(i._indices=this._cloneIndices()),i._updateGetRawIdx(),i},r.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=J(this._extent),t._rawExtent=J(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var i=this._indices.length;e=new t(i);for(var n=0;n<i;n++)e[n]=this._indices[n]}else e=new t(this._indices);return e}return null},r.prototype._getRawIdxIdentity=function(t){return t},r.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function t(e,i,n,a){return Wa(e[a],this._dimensions[a])}Bs={arrayRows:t,objectRows:function(e,i,n,a){return Wa(e[i],this._dimensions[a])},keyedColumns:t,original:function(e,i,n,a){var o=e&&(e.value==null?e:e.value);return Wa(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(e,i,n,a){return e[a]}}}(),r}(),_b=function(){function r(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,e=this._getUpstreamSourceManagers(),i=!!e.length,n,a;if(da(t)){var o=t,s=void 0,l=void 0,u=void 0;if(i){var f=e[0];f.prepareSource(),u=f.getSource(),s=u.data,l=u.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),l=Vt(s)?er:ne,a=[];var h=this._getSourceMetaRawOption()||{},c=u&&u.metaRawOption||{},v=Z(h.seriesLayoutBy,c.seriesLayoutBy)||null,d=Z(h.sourceHeader,c.sourceHeader),y=Z(h.dimensions,c.dimensions),p=v!==c.seriesLayoutBy||!!d!=!!c.sourceHeader||y;n=p?[Fl(s,{seriesLayoutBy:v,sourceHeader:d,dimensions:y},l)]:[]}else{var g=t;if(i){var m=this._applyTransform(e);n=m.sourceList,a=m.upstreamSignList}else{var _=g.get("source",!0);n=[Fl(_,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(n,a)},r.prototype._applyTransform=function(t){var e=this._sourceHost,i=e.get("transform",!0),n=e.get("fromTransformResult",!0);if(n!=null){var a="";t.length!==1&&wv(a)}var o,s=[],l=[];return A(t,function(u){u.prepareSource();var f=u.getSource(n||0),h="";n!=null&&!f&&wv(h),s.push(f),l.push(u._getVersionSign())}),i?o=db(i,s,{datasetIndex:e.componentIndex}):n!=null&&(o=[Qw(s[0])]),{sourceList:o,upstreamSignList:l}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var i=t[e];if(i._isDirty()||this._upstreamSignList[e]!==i._getVersionSign())return!0}},r.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var i=this._getUpstreamSourceManagers();return i[0]&&i[0].getSource(t)}return e},r.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},r.prototype._innerGetDataStore=function(t,e,i){var n=0,a=this._storeList,o=a[n];o||(o=a[n]={});var s=o[i];if(!s){var l=this._getUpstreamSourceManagers()[0];da(this._sourceHost)&&l?s=l._innerGetDataStore(t,e,i):(s=new zl,s.initData(new ug(e,t.length),t)),o[i]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(da(t)){var e=Ku(t);return e?[e.getSourceManager()]:[]}else return V(xw(t),function(i){return i.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,e,i,n;if(da(t))e=t.get("seriesLayoutBy",!0),i=t.get("sourceHeader",!0),n=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;e=a.get("seriesLayoutBy",!0),i=a.get("sourceHeader",!0),n=a.get("dimensions",!0)}return{seriesLayoutBy:e,sourceHeader:i,dimensions:n}},r}();function da(r){return r.mainType==="series"}function wv(r){throw new Error(r)}var Sb="line-height:1";function gg(r){var t=r.lineHeight;return t==null?Sb:"line-height:"+Nt(t+"")+"px"}function yg(r,t){var e=r.color||"#6e7079",i=r.fontSize||12,n=r.fontWeight||"400",a=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+Nt(i+"")+"px;color:"+Nt(e)+";font-weight:"+Nt(n+""),valueStyle:"font-size:"+Nt(o+"")+"px;color:"+Nt(a)+";font-weight:"+Nt(s+"")}:{nameStyle:{fontSize:i,fill:e,fontWeight:n},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var wb=[0,10,20,30],bb=["",`
`,`

`,`


`];function En(r,t){return t.type=r,t}function Hl(r){return r.type==="section"}function mg(r){return Hl(r)?xb:Tb}function _g(r){if(Hl(r)){var t=0,e=r.blocks.length,i=e>1||e>0&&!r.noHeader;return A(r.blocks,function(n){var a=_g(n);a>=t&&(t=a+ +(i&&(!a||Hl(n)&&!n.noHeader)))}),t}return 0}function xb(r,t,e,i){var n=t.noHeader,a=Cb(_g(t)),o=[],s=t.blocks||[];ke(!s||N(s)),s=s||[];var l=r.orderMode;if(t.sortBlocks&&l){s=s.slice();var u={valueAsc:"asc",valueDesc:"desc"};if(zr(u,l)){var f=new ob(u[l],null);s.sort(function(y,p){return f.evaluate(y.sortParam,p.sortParam)})}else l==="seriesDesc"&&s.reverse()}A(s,function(y,p){var g=t.valueFormatter,m=mg(y)(g?k(k({},r),{valueFormatter:g}):r,y,p>0?a.html:0,i);m!=null&&o.push(m)});var h=r.renderMode==="richText"?o.join(a.richText):Gl(i,o.join(""),n?e:a.html);if(n)return h;var c=Nl(t.header,"ordinal",r.useUTC),v=yg(i,r.renderMode).nameStyle,d=gg(i);return r.renderMode==="richText"?Sg(r,c,v)+a.richText+h:Gl(i,'<div style="'+v+";"+d+';">'+Nt(c)+"</div>"+h,e)}function Tb(r,t,e,i){var n=r.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,l=t.name,u=r.useUTC,f=t.valueFormatter||r.valueFormatter||function(S){return S=N(S)?S:[S],V(S,function(b,w){return Nl(b,N(v)?v[w]:v,u)})};if(!(a&&o)){var h=s?"":r.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",n),c=a?"":Nl(l,"ordinal",u),v=t.valueType,d=o?[]:f(t.value,t.dataIndex),y=!s||!a,p=!s&&a,g=yg(i,n),m=g.nameStyle,_=g.valueStyle;return n==="richText"?(s?"":h)+(a?"":Sg(r,c,m))+(o?"":Ab(r,d,y,p,_)):Gl(i,(s?"":h)+(a?"":Db(c,!s,m))+(o?"":Mb(d,y,p,_)),e)}}function bv(r,t,e,i,n,a){if(r){var o=mg(r),s={useUTC:n,renderMode:e,orderMode:i,markupStyleCreator:t,valueFormatter:r.valueFormatter};return o(s,r,0,a)}}function Cb(r){return{html:wb[r],richText:bb[r]}}function Gl(r,t,e){var i='<div style="clear:both"></div>',n="margin: "+e+"px 0 0",a=gg(r);return'<div style="'+n+";"+a+';">'+t+i+"</div>"}function Db(r,t,e){var i=t?"margin-left:2px":"";return'<span style="'+e+";"+i+'">'+Nt(r)+"</span>"}function Mb(r,t,e,i){var n=e?"10px":"20px",a=t?"float:right;margin-left:"+n:"";return r=N(r)?r:[r],'<span style="'+a+";"+i+'">'+V(r,function(o){return Nt(o)}).join("&nbsp;&nbsp;")+"</span>"}function Sg(r,t,e){return r.markupStyleCreator.wrapRichTextStyle(t,e)}function Ab(r,t,e,i,n){var a=[n],o=i?10:20;return e&&a.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(N(t)?t.join("  "):t,a)}function Lb(r,t){var e=r.getData().getItemVisual(t,"style"),i=e[r.visualDrawType];return Ur(i)}function wg(r,t){var e=r.get("padding");return e??(t==="richText"?[8,10]:10)}var Ns=function(){function r(){this.richTextStyles={},this._nextStyleNameId=kd()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(t,e,i){var n=i==="richText"?this._generateStyleName():null,a=pw({color:e,type:t,renderMode:i,markerId:n});return z(a)?a:(this.richTextStyles[n]=a.style,a.content)},r.prototype.wrapRichTextStyle=function(t,e){var i={};N(e)?A(e,function(a){return k(i,a)}):k(i,e);var n=this._generateStyleName();return this.richTextStyles[n]=i,"{"+n+"|"+t+"}"},r}();function Ib(r){var t=r.series,e=r.dataIndex,i=r.multipleSeries,n=t.getData(),a=n.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(e),l=N(s),u=Lb(t,e),f,h,c,v;if(o>1||l&&!o){var d=Pb(s,t,e,a,u);f=d.inlineValues,h=d.inlineValueTypes,c=d.blocks,v=d.inlineValues[0]}else if(o){var y=n.getDimensionInfo(a[0]);v=f=Di(n,e,a[0]),h=y.type}else v=f=l?s[0]:s;var p=Du(t),g=p&&t.name||"",m=n.getName(e),_=i?g:m;return En("section",{header:g,noHeader:i||!p,sortParam:v,blocks:[En("nameValue",{markerType:"item",markerColor:u,name:_,noName:!_e(_),value:f,valueType:h,dataIndex:e})].concat(c||[])})}function Pb(r,t,e,i,n){var a=t.getData(),o=ir(r,function(h,c,v){var d=a.getDimensionInfo(v);return h=h||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],l=[],u=[];i.length?A(i,function(h){f(Di(a,e,h),h)}):A(r,f);function f(h,c){var v=a.getDimensionInfo(c);!v||v.otherDims.tooltip===!1||(o?u.push(En("nameValue",{markerType:"subItem",markerColor:n,name:v.displayName,value:h,valueType:v.type})):(s.push(h),l.push(v.type)))}return{inlineValues:s,inlineValueTypes:l,blocks:u}}var Ve=yt();function pa(r,t){return r.getName(t)||r.getId(t)}var Rb="__universalTransitionEnabled",Fo=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return t.prototype.init=function(e,i,n){this.seriesIndex=this.componentIndex,this.dataTask=_n({count:kb,reset:Ob}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(e,n);var a=Ve(this).sourceManager=new _b(this);a.prepareSource();var o=this.getInitialData(e,n);Tv(o,this),this.dataTask.context.data=o,Ve(this).dataBeforeProcessed=o,xv(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=Pn(this),a=n?Oo(e):{},o=this.subType;ut.hasClass(o)&&(o+="Series"),tt(e,i.getTheme().get(this.subType)),tt(e,this.getDefaultOption()),ah(e,"label",["show"]),this.fillDataTextStyle(e.data),n&&Ci(e,a,n)},t.prototype.mergeOption=function(e,i){e=tt(this.option,e,!0),this.fillDataTextStyle(e.data);var n=Pn(this);n&&Ci(this.option,e,n);var a=Ve(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(e,i);Tv(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,Ve(this).dataBeforeProcessed=o,xv(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(e){if(e&&!Vt(e))for(var i=["show"],n=0;n<e.length;n++)e[n]&&e[n].label&&ah(e[n],"label",i)},t.prototype.getInitialData=function(e,i){},t.prototype.appendData=function(e){var i=this.getRawData();i.appendData(e.data)},t.prototype.getData=function(e){var i=Vl(this);if(i){var n=i.context.data;return e==null||!n.getLinkedData?n:n.getLinkedData(e)}else return Ve(this).data},t.prototype.getAllData=function(){var e=this.getData();return e&&e.getLinkedDataAll?e.getLinkedDataAll():[{data:e}]},t.prototype.setData=function(e){var i=Vl(this);if(i){var n=i.context;n.outputData=e,i!==this.dataTask&&(n.data=e)}Ve(this).data=e},t.prototype.getEncode=function(){var e=this.get("encode",!0);if(e)return X(e)},t.prototype.getSourceManager=function(){return Ve(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return Ve(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var e=this.get("colorBy");return e||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var e=this.coordinateSystem;return e&&e.getBaseAxis&&e.getBaseAxis()},t.prototype.formatTooltip=function(e,i,n){return Ib({series:this,dataIndex:e,multipleSeries:i})},t.prototype.isAnimationEnabled=function(){var e=this.ecModel;if(Y.node&&!(e&&e.ssr))return!1;var i=this.getShallow("animation");return i&&this.getData().count()>this.getShallow("animationThreshold")&&(i=!1),!!i},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(e,i,n){var a=this.ecModel,o=Qu.prototype.getColorFromPalette.call(this,e,i,n);return o||(o=a.getColorFromPalette(e,i,n)),o},t.prototype.coordDimToDataDim=function(e){return this.getRawData().mapDimensionsAll(e)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(e,i){this._innerSelect(this.getData(i),e)},t.prototype.unselect=function(e,i){var n=this.option.selectedMap;if(n){var a=this.option.selectedMode,o=this.getData(i);if(a==="series"||n==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<e.length;s++){var l=e[s],u=pa(o,l);n[u]=!1,this._selectedDataIndicesMap[u]=-1}}},t.prototype.toggleSelect=function(e,i){for(var n=[],a=0;a<e.length;a++)n[0]=e[a],this.isSelected(e[a],i)?this.unselect(n,i):this.select(n,i)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var e=this._selectedDataIndicesMap,i=ht(e),n=[],a=0;a<i.length;a++){var o=e[i[a]];o>=0&&n.push(o)}return n},t.prototype.isSelected=function(e,i){var n=this.option.selectedMap;if(!n)return!1;var a=this.getData(i);return(n==="all"||n[pa(a,e)])&&!a.getItemModel(e).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[Rb])return!0;var e=this.option.universalTransition;return e?e===!0?!0:e&&e.enabled:!1},t.prototype._innerSelect=function(e,i){var n,a,o=this.option,s=o.selectedMode,l=i.length;if(!(!s||!l)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){H(o.selectedMap)||(o.selectedMap={});for(var u=o.selectedMap,f=0;f<l;f++){var h=i[f],c=pa(e,h);u[c]=!0,this._selectedDataIndicesMap[c]=e.getRawIndex(h)}}else if(s==="single"||s===!0){var v=i[l-1],c=pa(e,v);o.selectedMap=(n={},n[c]=!0,n),this._selectedDataIndicesMap=(a={},a[c]=e.getRawIndex(v),a)}}},t.prototype._initSelectedMapFromData=function(e){if(!this.option.selectedMap){var i=[];e.hasItemOption&&e.each(function(n){var a=e.getRawDataItem(n);a&&a.selected&&i.push(n)}),i.length>0&&this._innerSelect(e,i)}},t.registerClass=function(e){return ut.registerClass(e)},t.protoInitialize=function(){var e=t.prototype;e.type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"}(),t}(ut);xe(Fo,nb);xe(Fo,Qu);Hd(Fo,ut);function xv(r){var t=r.name;Du(r)||(r.name=Eb(r)||t)}function Eb(r){var t=r.getRawData(),e=t.mapDimensionsAll("seriesName"),i=[];return A(e,function(n){var a=t.getDimensionInfo(n);a.displayName&&i.push(a.displayName)}),i.join(" ")}function kb(r){return r.model.getRawData().count()}function Ob(r){var t=r.model;return t.setData(t.getRawData().cloneShallow()),Bb}function Bb(r,t){t.outputData&&r.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function Tv(r,t){A(Lm(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(e){r.wrapMethod(e,mt(Nb,t))})}function Nb(r,t){var e=Vl(r);return e&&e.setOutputEnd((t||this).count()),t}function Vl(r){var t=(r.ecModel||{}).scheduler,e=t&&t.getPipeline(r.uid);if(e){var i=e.currentTask;if(i){var n=i.agentStubMap;n&&(i=n.get(r.uid))}return i}}const kn=Fo;var nf=function(){function r(){this.group=new Et,this.uid=Ao("viewComponent")}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){},r.prototype.updateLayout=function(t,e,i,n){},r.prototype.updateVisual=function(t,e,i,n){},r.prototype.toggleBlurSeries=function(t,e,i){},r.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},r}();Au(nf);So(nf);const Be=nf;function af(){var r=yt();return function(t){var e=r(t),i=t.pipelineContext,n=!!e.large,a=!!e.progressiveRender,o=e.large=!!(i&&i.large),s=e.progressiveRender=!!(i&&i.progressiveRender);return(n!==o||a!==s)&&"reset"}}var bg=yt(),Fb=af(),of=function(){function r(){this.group=new Et,this.uid=Ao("viewChart"),this.renderTask=_n({plan:zb,reset:Hb}),this.renderTask.context={view:this}}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.highlight=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&Dv(a,n,"emphasis")},r.prototype.downplay=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&Dv(a,n,"normal")},r.prototype.remove=function(t,e){this.group.removeAll()},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateLayout=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.eachRendered=function(t){Hu(this.group,t)},r.markUpdateMethod=function(t,e){bg(t).updateMethod=e},r.protoInitialize=function(){var t=r.prototype;t.type="chart"}(),r}();function Cv(r,t,e){r&&El(r)&&(t==="emphasis"?ro:io)(r,e)}function Dv(r,t,e){var i=Gr(r,t),n=t&&t.highlightKey!=null?U1(t.highlightKey):null;i!=null?A(Rt(i),function(a){Cv(r.getItemGraphicEl(a),e,n)}):r.eachItemGraphicEl(function(a){Cv(a,e,n)})}Au(of);So(of);function zb(r){return Fb(r.model)}function Hb(r){var t=r.model,e=r.ecModel,i=r.api,n=r.payload,a=t.pipelineContext.progressiveRender,o=r.view,s=n&&bg(n).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return l!=="render"&&o[l](t,e,i,n),Gb[l]}var Gb={incrementalPrepareRender:{progress:function(r,t){t.view.incrementalRender(r,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(r,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}};const rr=of;var uo="\0__throttleOriginMethod",Mv="\0__throttleRate",Av="\0__throttleType";function sf(r,t,e){var i,n=0,a=0,o=null,s,l,u,f;t=t||0;function h(){a=new Date().getTime(),o=null,r.apply(l,u||[])}var c=function(){for(var v=[],d=0;d<arguments.length;d++)v[d]=arguments[d];i=new Date().getTime(),l=this,u=v;var y=f||t,p=f||e;f=null,s=i-(p?n:a)-y,clearTimeout(o),p?o=setTimeout(h,y):s>=0?h():o=setTimeout(h,-s),n=i};return c.clear=function(){o&&(clearTimeout(o),o=null)},c.debounceNextCall=function(v){f=v},c}function xg(r,t,e,i){var n=r[t];if(n){var a=n[uo]||n,o=n[Av],s=n[Mv];if(s!==e||o!==i){if(e==null||!i)return r[t]=a;n=r[t]=sf(a,e,i==="debounce"),n[uo]=a,n[Av]=i,n[Mv]=e}return n}}function Wl(r,t){var e=r[t];e&&e[uo]&&(e.clear&&e.clear(),r[t]=e[uo])}var Lv=yt(),Iv={itemStyle:Mn(Bp,!0),lineStyle:Mn(Op,!0)},Vb={lineStyle:"stroke",itemStyle:"fill"};function Tg(r,t){var e=r.visualStyleMapper||Iv[t];return e||(console.warn("Unknown style type '"+t+"'."),Iv.itemStyle)}function Cg(r,t){var e=r.visualDrawType||Vb[t];return e||(console.warn("Unknown style type '"+t+"'."),"fill")}var Wb={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=r.getModel(i),a=Tg(r,i),o=a(n),s=n.getShallow("decal");s&&(e.setVisual("decal",s),s.dirty=!0);var l=Cg(r,i),u=o[l],f=$(u)?u:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[l]||f||h){var c=r.getColorFromPalette(r.name,null,t.getSeriesCount());o[l]||(o[l]=c,e.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||$(o.fill)?c:o.fill,o.stroke=o.stroke==="auto"||$(o.stroke)?c:o.stroke}if(e.setVisual("style",o),e.setVisual("drawType",l),!t.isSeriesFiltered(r)&&f)return e.setVisual("colorFromPalette",!1),{dataEach:function(v,d){var y=r.getDataParams(d),p=k({},o);p[l]=f(y),v.setItemVisual(d,"style",p)}}}},$i=new At,Ub={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!(r.ignoreStyleOnData||t.isSeriesFiltered(r))){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=Tg(r,i),a=e.getVisual("drawType");return{dataEach:e.hasItemOption?function(o,s){var l=o.getRawDataItem(s);if(l&&l[i]){$i.option=l[i];var u=n($i),f=o.ensureUniqueItemVisual(s,"style");k(f,u),$i.option.decal&&(o.setItemVisual(s,"decal",$i.option.decal),$i.option.decal.dirty=!0),a in u&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},$b={performRawSeries:!0,overallReset:function(r){var t=X();r.eachSeries(function(e){var i=e.getColorBy();if(!e.isColorBySeries()){var n=e.type+"-"+i,a=t.get(n);a||(a={},t.set(n,a)),Lv(e).scope=a}}),r.eachSeries(function(e){if(!(e.isColorBySeries()||r.isSeriesFiltered(e))){var i=e.getRawData(),n={},a=e.getData(),o=Lv(e).scope,s=e.visualStyleAccessPath||"itemStyle",l=Cg(e,s);a.each(function(u){var f=a.getRawIndex(u);n[f]=u}),i.each(function(u){var f=n[u],h=a.getItemVisual(f,"colorFromPalette");if(h){var c=a.ensureUniqueItemVisual(f,"style"),v=i.getName(u)||u+"",d=i.count();c[l]=e.getColorFromPalette(v,o,d)}})}})}},ga=Math.PI;function Yb(r,t){t=t||{},it(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var e=new Et,i=new bt({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});e.add(i);var n=new kt({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new bt({style:{fill:"none"},textContent:n,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});e.add(a);var o;return t.showSpinner&&(o=new Ou({shape:{startAngle:-ga/2,endAngle:-ga/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:ga*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:ga*3/2}).delay(300).start("circularInOut"),e.add(o)),e.resize=function(){var s=n.getBoundingRect().width,l=t.showSpinner?t.spinnerRadius:0,u=(r.getWidth()-l*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:l),f=r.getHeight()/2;t.showSpinner&&o.setShape({cx:u,cy:f}),a.setShape({x:u-l,y:f-l,width:l*2,height:l*2}),i.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},e.resize(),e}var Xb=function(){function r(t,e,i,n){this._stageTaskMap=X(),this.ecInstance=t,this.api=e,i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice(),this._allHandlers=i.concat(n)}return r.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(i){var n=i.overallTask;n&&n.dirty()})},r.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,a=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,o=a?i.step:null,s=n&&n.modDataCount,l=s!=null?Math.ceil(s/o):null;return{step:o,modBy:l,modDataCount:s}}},r.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},r.prototype.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),a=n.count(),o=i.progressiveEnabled&&e.incrementalPrepareRender&&a>=i.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),l=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=i.context={progressiveRender:o,modDataCount:l,large:s}},r.prototype.restorePipelines=function(t){var e=this,i=e._pipelineMap=X();t.eachSeries(function(n){var a=n.getProgressive(),o=n.uid;i.set(o,{id:o,head:null,tail:null,threshold:n.getProgressiveThreshold(),progressiveEnabled:a&&!(n.preventIncremental&&n.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),e._pipe(n,n.dataTask)})},r.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),i=this.api;A(this._allHandlers,function(n){var a=t.get(n.uid)||t.set(n.uid,{}),o="";ke(!(n.reset&&n.overallReset),o),n.reset&&this._createSeriesStageTask(n,a,e,i),n.overallReset&&this._createOverallStageTask(n,a,e,i)},this)},r.prototype.prepareView=function(t,e,i,n){var a=t.renderTask,o=a.context;o.model=e,o.ecModel=i,o.api=n,a.__block=!t.incrementalPrepareRender,this._pipe(e,a)},r.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},r.prototype.performVisualTasks=function(t,e,i){this._performStageTasks(this._visualHandlers,t,e,i)},r.prototype._performStageTasks=function(t,e,i,n){n=n||{};var a=!1,o=this;A(t,function(l,u){if(!(n.visualType&&n.visualType!==l.visualType)){var f=o._stageTaskMap.get(l.uid),h=f.seriesTaskMap,c=f.overallTask;if(c){var v,d=c.agentStubMap;d.each(function(p){s(n,p)&&(p.dirty(),v=!0)}),v&&c.dirty(),o.updatePayload(c,i);var y=o.getPerformArgs(c,n.block);d.each(function(p){p.perform(y)}),c.perform(y)&&(a=!0)}else h&&h.each(function(p,g){s(n,p)&&p.dirty();var m=o.getPerformArgs(p,n.block);m.skip=!l.performRawSeries&&e.isSeriesFiltered(p.context.model),o.updatePayload(p,i),p.perform(m)&&(a=!0)})}});function s(l,u){return l.setDirty&&(!l.dirtyMap||l.dirtyMap.get(u.__pipeline.id))}this.unfinished=a||this.unfinished},r.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(i){e=i.dataTask.perform()||e}),this.unfinished=e||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},r.prototype.updatePayload=function(t,e){e!=="remain"&&(t.context.payload=e)},r.prototype._createSeriesStageTask=function(t,e,i,n){var a=this,o=e.seriesTaskMap,s=e.seriesTaskMap=X(),l=t.seriesType,u=t.getTargetSeries;t.createOnAllSeries?i.eachRawSeries(f):l?i.eachRawSeriesByType(l,f):u&&u(i,n).each(f);function f(h){var c=h.uid,v=s.set(c,o&&o.get(c)||_n({plan:Jb,reset:jb,count:ex}));v.context={model:h,ecModel:i,api:n,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,v)}},r.prototype._createOverallStageTask=function(t,e,i,n){var a=this,o=e.overallTask=e.overallTask||_n({reset:Zb});o.context={ecModel:i,api:n,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,l=o.agentStubMap=X(),u=t.seriesType,f=t.getTargetSeries,h=!0,c=!1,v="";ke(!t.createOnAllSeries,v),u?i.eachRawSeriesByType(u,d):f?f(i,n).each(d):(h=!1,A(i.getSeries(),d));function d(y){var p=y.uid,g=l.set(p,s&&s.get(p)||(c=!0,_n({reset:qb,onDirty:Qb})));g.context={model:y,overallProgress:h},g.agent=o,g.__block=h,a._pipe(y,g)}c&&o.dirty()},r.prototype._pipe=function(t,e){var i=t.uid,n=this._pipelineMap.get(i);!n.head&&(n.head=e),n.tail&&n.tail.pipe(e),n.tail=e,e.__idxInPipeline=n.count++,e.__pipeline=n},r.wrapStageHandler=function(t,e){return $(t)&&(t={overallReset:t,seriesType:rx(t)}),t.uid=Ao("stageHandler"),e&&(t.visualType=e),t},r}();function Zb(r){r.overallReset(r.ecModel,r.api,r.payload)}function qb(r){return r.overallProgress&&Kb}function Kb(){this.agent.dirty(),this.getDownstream().dirty()}function Qb(){this.agent&&this.agent.dirty()}function Jb(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function jb(r){r.useClearVisual&&r.data.clearAllVisual();var t=r.resetDefines=Rt(r.reset(r.model,r.ecModel,r.api,r.payload));return t.length>1?V(t,function(e,i){return Dg(i)}):tx}var tx=Dg(0);function Dg(r){return function(t,e){var i=e.data,n=e.resetDefines[r];if(n&&n.dataEach)for(var a=t.start;a<t.end;a++)n.dataEach(i,a);else n&&n.progress&&n.progress(t,i)}}function ex(r){return r.data.count()}function rx(r){fo=null;try{r(On,Mg)}catch{}return fo}var On={},Mg={},fo;Ag(On,ig);Ag(Mg,ng);On.eachSeriesByType=On.eachRawSeriesByType=function(r){fo=r};On.eachComponent=function(r){r.mainType==="series"&&r.subType&&(fo=r.subType)};function Ag(r,t){for(var e in t.prototype)r[e]=Ht}const Lg=Xb;var Pv=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const ix={color:Pv,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Pv]};var Ct="#B9B8CE",Rv="#100C2A",ya=function(){return{axisLine:{lineStyle:{color:Ct}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},Ev=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Ig={darkMode:!0,color:Ev,backgroundColor:Rv,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Ct},pageTextStyle:{color:Ct}},textStyle:{color:Ct},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Ct}},dataZoom:{borderColor:"#71708A",textStyle:{color:Ct},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Ct}},timeline:{lineStyle:{color:Ct},label:{color:Ct},controlStyle:{color:Ct,borderColor:Ct}},calendar:{itemStyle:{color:Rv},dayLabel:{color:Ct},monthLabel:{color:Ct},yearLabel:{color:Ct}},timeAxis:ya(),logAxis:ya(),valueAxis:ya(),categoryAxis:ya(),line:{symbol:"circle"},graph:{color:Ev},gauge:{title:{color:Ct},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Ct},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Ig.categoryAxis.splitLine.show=!1;const nx=Ig;var ax=function(){function r(){}return r.prototype.normalizeQuery=function(t){var e={},i={},n={};if(z(t)){var a=Se(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};A(t,function(l,u){for(var f=!1,h=0;h<o.length;h++){var c=o[h],v=u.lastIndexOf(c);if(v>0&&v===u.length-c.length){var d=u.slice(0,v);d!=="data"&&(e.mainType=d,e[c.toLowerCase()]=l,f=!0)}}s.hasOwnProperty(u)&&(i[u]=l,f=!0),f||(n[u]=l)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},r.prototype.filter=function(t,e){var i=this.eventInfo;if(!i)return!0;var n=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return f(l,o,"mainType")&&f(l,o,"subType")&&f(l,o,"index","componentIndex")&&f(l,o,"name")&&f(l,o,"id")&&f(u,a,"name")&&f(u,a,"dataIndex")&&f(u,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,a));function f(h,c,v,d){return h[v]==null||c[d||v]===h[v]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),Ul=["symbol","symbolSize","symbolRotate","symbolOffset"],kv=Ul.concat(["symbolKeepAspect"]),ox={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData();if(r.legendIcon&&e.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var i={},n={},a=!1,o=0;o<Ul.length;o++){var s=Ul[o],l=r.get(s);$(l)?(a=!0,n[s]=l):i[s]=l}if(i.symbol=i.symbol||r.defaultSymbol,e.setVisual(k({legendIcon:r.legendIcon||i.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},i)),t.isSeriesFiltered(r))return;var u=ht(n);function f(h,c){for(var v=r.getRawValue(c),d=r.getDataParams(c),y=0;y<u.length;y++){var p=u[y];h.setItemVisual(c,p,n[p](v,d))}}return{dataEach:a?f:null}}},sx={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!r.hasSymbolVisual||t.isSeriesFiltered(r))return;var e=r.getData();function i(n,a){for(var o=n.getItemModel(a),s=0;s<kv.length;s++){var l=kv[s],u=o.getShallow(l,!0);u!=null&&n.setItemVisual(a,l,u)}}return{dataEach:e.hasItemOption?i:null}}};function lx(r,t,e){switch(e){case"color":var i=r.getItemVisual(t,"style");return i[r.getVisual("drawType")];case"opacity":return r.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(t,e)}}function ux(r,t){switch(t){case"color":var e=r.getVisual("style");return e[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(t)}}function OA(r,t){function e(i,n){var a=[];return i.eachComponent({mainType:"series",subType:r,query:n},function(o){a.push(o.seriesIndex)}),a}A([[r+"ToggleSelect","toggleSelect"],[r+"Select","select"],[r+"UnSelect","unselect"]],function(i){t(i[0],function(n,a,o){n=k({},n),o.dispatchAction(k(n,{type:i[1],seriesIndex:e(a,n)}))})})}function si(r,t,e,i,n){var a=r+t;e.isSilent(a)||i.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,l=o.option.selectedMap,u=n.selected,f=0;f<u.length;f++)if(u[f].seriesIndex===s){var h=o.getData(),c=Gr(h,n.fromActionPayload);e.trigger(a,{type:a,seriesId:o.id,name:N(c)?h.getName(c[0]):h.getName(c),selected:z(l)?l:k({},l)})}})}function fx(r,t,e){r.on("selectchanged",function(i){var n=e.getModel();i.isFromClick?(si("map","selectchanged",t,n,i),si("pie","selectchanged",t,n,i)):i.fromAction==="select"?(si("map","selected",t,n,i),si("pie","selected",t,n,i)):i.fromAction==="unselect"&&(si("map","unselected",t,n,i),si("pie","unselected",t,n,i))})}function un(r,t,e){for(var i;r&&!(t(r)&&(i=r,e));)r=r.__hostTarget||r.parent;return i}var hx=Math.round(Math.random()*9),vx=typeof Object.defineProperty=="function",cx=function(){function r(){this._id="__ec_inner_"+hx++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var i=this._guard(t);return vx?Object.defineProperty(i,this._id,{value:e,enumerable:!1,configurable:!0}):i[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}();const dx=cx;var px=st.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i+a),r.lineTo(e-n,i+a),r.closePath()}}),gx=st.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i),r.lineTo(e,i+a),r.lineTo(e-n,i),r.closePath()}}),yx=st.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.x,i=t.y,n=t.width/5*3,a=Math.max(n,t.height),o=n/2,s=o*o/(a-o),l=i-a+o+s,u=Math.asin(s/o),f=Math.cos(u)*o,h=Math.sin(u),c=Math.cos(u),v=o*.6,d=o*.7;r.moveTo(e-f,l+s),r.arc(e,l,o,Math.PI-u,Math.PI*2+u),r.bezierCurveTo(e+f-h*v,l+s+c*v,e,i-d,e,i),r.bezierCurveTo(e,i-d,e-f+h*v,l+s+c*v,e-f,l+s),r.closePath()}}),mx=st.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.height,i=t.width,n=t.x,a=t.y,o=i/3*2;r.moveTo(n,a),r.lineTo(n+o,a+e),r.lineTo(n,a+e/4*3),r.lineTo(n-o,a+e),r.lineTo(n,a),r.closePath()}}),_x={line:Wr,rect:bt,roundRect:bt,square:bt,circle:Eu,diamond:gx,pin:yx,arrow:mx,triangle:px},Sx={line:function(r,t,e,i,n){n.x1=r,n.y1=t+i/2,n.x2=r+e,n.y2=t+i/2},rect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i},roundRect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i,n.r=Math.min(e,i)/4},square:function(r,t,e,i,n){var a=Math.min(e,i);n.x=r,n.y=t,n.width=a,n.height=a},circle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.r=Math.min(e,i)/2},diamond:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i},pin:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},arrow:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},triangle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i}},$l={};A(_x,function(r,t){$l[t]=new r});var bx=st.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,t,e){var i=Dd(r,t,e),n=this.shape;return n&&n.symbolType==="pin"&&t.position==="inside"&&(i.y=e.y+e.height*.4),i},buildPath:function(r,t,e){var i=t.symbolType;if(i!=="none"){var n=$l[i];n||(i="rect",n=$l[i]),Sx[i](t.x,t.y,t.width,t.height,n.shape),n.buildPath(r,n.shape,e)}}});function xx(r,t){if(this.type!=="image"){var e=this.style;this.__isEmptyBrush?(e.stroke=r,e.fill=t||"#fff",e.lineWidth=2):this.shape.symbolType==="line"?e.stroke=r:e.fill=r,this.markRedraw()}}function Mi(r,t,e,i,n,a,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var l;return r.indexOf("image://")===0?l=Ip(r.slice(8),new et(t,e,i,n),o?"center":"cover"):r.indexOf("path://")===0?l=Nu(r.slice(7),{},new et(t,e,i,n),o?"center":"cover"):l=new bx({shape:{symbolType:r,x:t,y:e,width:i,height:n}}),l.__isEmptyBrush=s,l.setColor=xx,a&&l.setColor(a),l}function Tx(r){return N(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function Pg(r,t){if(r!=null)return N(r)||(r=[r,r]),[Pt(r[0],t[0])||0,Pt(Z(r[1],r[0]),t[1])||0]}function Rr(r){return isFinite(r)}function Cx(r,t,e){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*e.width+e.x,n=n*e.width+e.x,a=a*e.height+e.y,o=o*e.height+e.y),i=Rr(i)?i:0,n=Rr(n)?n:1,a=Rr(a)?a:0,o=Rr(o)?o:0;var s=r.createLinearGradient(i,a,n,o);return s}function Dx(r,t,e){var i=e.width,n=e.height,a=Math.min(i,n),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,l=t.r==null?.5:t.r;t.global||(o=o*i+e.x,s=s*n+e.y,l=l*a),o=Rr(o)?o:.5,s=Rr(s)?s:.5,l=l>=0&&Rr(l)?l:.5;var u=r.createRadialGradient(o,s,0,o,s,l);return u}function Yl(r,t,e){for(var i=t.type==="radial"?Dx(r,t,e):Cx(r,t,e),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function Mx(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function ma(r){return parseInt(r,10)}function _a(r,t,e){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(e[i]!=null&&e[i]!=="auto")return parseFloat(e[i]);var s=document.defaultView.getComputedStyle(r);return(r[n]||ma(s[i])||ma(r.style[i]))-(ma(s[a])||0)-(ma(s[o])||0)|0}function Ax(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:vt(r)?[r]:N(r)?r:null}function Rg(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&Ax(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(e){var n=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;n&&n!==1&&(e=V(e,function(a){return a/n}),i/=n)}return[e,i]}var Lx=new Vr(!0);function ho(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function Ov(r){return typeof r=="string"&&r!=="none"}function vo(r){var t=r.fill;return t!=null&&t!=="none"}function Bv(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function Nv(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function Xl(r,t,e){var i=Gd(t.image,t.__image,e);if(wo(i)){var n=r.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*Im),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function Ix(r,t,e,i){var n,a=ho(e),o=vo(e),s=e.strokePercent,l=s<1,u=!t.path;(!t.silent||l)&&u&&t.createPathProxy();var f=t.path||Lx,h=t.__dirty;if(!i){var c=e.fill,v=e.stroke,d=o&&!!c.colorStops,y=a&&!!v.colorStops,p=o&&!!c.image,g=a&&!!v.image,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0;(d||y)&&(w=t.getBoundingRect()),d&&(m=h?Yl(r,c,w):t.__canvasFillGradient,t.__canvasFillGradient=m),y&&(_=h?Yl(r,v,w):t.__canvasStrokeGradient,t.__canvasStrokeGradient=_),p&&(S=h||!t.__canvasFillPattern?Xl(r,c,t):t.__canvasFillPattern,t.__canvasFillPattern=S),g&&(b=h||!t.__canvasStrokePattern?Xl(r,v,t):t.__canvasStrokePattern,t.__canvasStrokePattern=S),d?r.fillStyle=m:p&&(S?r.fillStyle=S:o=!1),y?r.strokeStyle=_:g&&(b?r.strokeStyle=b:a=!1)}var x=t.getGlobalScale();f.setScale(x[0],x[1],t.segmentIgnoreThreshold);var C,T;r.setLineDash&&e.lineDash&&(n=Rg(t),C=n[0],T=n[1]);var D=!0;(u||h&vi)&&(f.setDPR(r.dpr),l?f.setContext(null):(f.setContext(r),D=!1),f.reset(),t.buildPath(f,t.shape,i),f.toStatic(),t.pathUpdated()),D&&f.rebuildPath(r,l?s:1),C&&(r.setLineDash(C),r.lineDashOffset=T),i||(e.strokeFirst?(a&&Nv(r,e),o&&Bv(r,e)):(o&&Bv(r,e),a&&Nv(r,e))),C&&r.setLineDash([])}function Px(r,t,e){var i=t.__image=Gd(e.image,t.__image,t,t.onload);if(!(!i||!wo(i))){var n=e.x||0,a=e.y||0,o=t.getWidth(),s=t.getHeight(),l=i.width/i.height;if(o==null&&s!=null?o=s*l:s==null&&o!=null?s=o/l:o==null&&s==null&&(o=i.width,s=i.height),e.sWidth&&e.sHeight){var u=e.sx||0,f=e.sy||0;r.drawImage(i,u,f,e.sWidth,e.sHeight,n,a,o,s)}else if(e.sx&&e.sy){var u=e.sx,f=e.sy,h=o-u,c=s-f;r.drawImage(i,u,f,h,c,n,a,o,s)}else r.drawImage(i,n,a,o,s)}}function Rx(r,t,e){var i,n=e.text;if(n!=null&&(n+=""),n){r.font=e.font||Fr,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,o=void 0;r.setLineDash&&e.lineDash&&(i=Rg(t),a=i[0],o=i[1]),a&&(r.setLineDash(a),r.lineDashOffset=o),e.strokeFirst?(ho(e)&&r.strokeText(n,e.x,e.y),vo(e)&&r.fillText(n,e.x,e.y)):(vo(e)&&r.fillText(n,e.x,e.y),ho(e)&&r.strokeText(n,e.x,e.y)),a&&r.setLineDash([])}}var Fv=["shadowBlur","shadowOffsetX","shadowOffsetY"],zv=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Eg(r,t,e,i,n){var a=!1;if(!i&&(e=e||{},t===e))return!1;if(i||t.opacity!==e.opacity){zt(r,n),a=!0;var o=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(o)?kr.opacity:o}(i||t.blend!==e.blend)&&(a||(zt(r,n),a=!0),r.globalCompositeOperation=t.blend||kr.blend);for(var s=0;s<Fv.length;s++){var l=Fv[s];(i||t[l]!==e[l])&&(a||(zt(r,n),a=!0),r[l]=r.dpr*(t[l]||0))}return(i||t.shadowColor!==e.shadowColor)&&(a||(zt(r,n),a=!0),r.shadowColor=t.shadowColor||kr.shadowColor),a}function Hv(r,t,e,i,n){var a=Bn(t,n.inHover),o=i?null:e&&Bn(e,n.inHover)||{};if(a===o)return!1;var s=Eg(r,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(zt(r,n),s=!0),Ov(a.fill)&&(r.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(zt(r,n),s=!0),Ov(a.stroke)&&(r.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(zt(r,n),s=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var l=a.lineWidth,u=l/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==u&&(s||(zt(r,n),s=!0),r.lineWidth=u)}for(var f=0;f<zv.length;f++){var h=zv[f],c=h[0];(i||a[c]!==o[c])&&(s||(zt(r,n),s=!0),r[c]=a[c]||h[1])}return s}function Ex(r,t,e,i,n){return Eg(r,Bn(t,n.inHover),e&&Bn(e,n.inHover),i,n)}function kg(r,t){var e=t.transform,i=r.dpr||1;e?r.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):r.setTransform(i,0,0,i,0,0)}function kx(r,t,e){for(var i=!1,n=0;n<r.length;n++){var a=r[n];i=i||a.isZeroArea(),kg(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=i}function Ox(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var Gv=1,Vv=2,Wv=3,Uv=4;function Bx(r){var t=vo(r),e=ho(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function zt(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function Bn(r,t){return t&&r.__hoverStyle||r.style}function Og(r,t){Er(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Er(r,t,e,i){var n=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=~$t,t.__isRendered=!1;return}var a=t.__clipPaths,o=e.prevElClipPaths,s=!1,l=!1;if((!o||Mx(a,o))&&(o&&o.length&&(zt(r,e),r.restore(),l=s=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(zt(r,e),r.save(),kx(a,r,e),s=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var u=e.prevEl;u||(l=s=!0);var f=t instanceof st&&t.autoBatch&&Bx(t.style);s||Ox(n,u.transform)?(zt(r,e),kg(r,t)):f||zt(r,e);var h=Bn(t,e.inHover);t instanceof st?(e.lastDrawType!==Gv&&(l=!0,e.lastDrawType=Gv),Hv(r,t,u,l,e),(!f||!e.batchFill&&!e.batchStroke)&&r.beginPath(),Ix(r,t,h,f),f&&(e.batchFill=h.fill||"",e.batchStroke=h.stroke||"")):t instanceof Al?(e.lastDrawType!==Wv&&(l=!0,e.lastDrawType=Wv),Hv(r,t,u,l,e),Rx(r,t,h)):t instanceof Yr?(e.lastDrawType!==Vv&&(l=!0,e.lastDrawType=Vv),Ex(r,t,u,l,e),Px(r,t,h)):t.getTemporalDisplayables&&(e.lastDrawType!==Uv&&(l=!0,e.lastDrawType=Uv),Nx(r,t,e)),f&&i&&zt(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function Nx(r,t,e){var i=t.getDisplayables(),n=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var l=i[o];l.beforeBrush&&l.beforeBrush(),l.innerBeforeBrush(),Er(r,l,a,o===s-1),l.innerAfterBrush(),l.afterBrush&&l.afterBrush(),a.prevEl=l}for(var u=0,f=n.length;u<f;u++){var l=n[u];l.beforeBrush&&l.beforeBrush(),l.innerBeforeBrush(),Er(r,l,a,u===f-1),l.innerAfterBrush(),l.afterBrush&&l.afterBrush(),a.prevEl=l}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var Fs=new dx,$v=new Hn(100),Yv=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Zl(r,t){if(r==="none")return null;var e=t.getDevicePixelRatio(),i=t.getZr(),n=i.painter.type==="svg";r.dirty&&Fs.delete(r);var a=Fs.get(r);if(a)return a;var o=it(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return l(s),s.rotation=o.rotation,s.scaleX=s.scaleY=n?1:1/e,Fs.set(r,s),r.dirty=!1,s;function l(u){for(var f=[e],h=!0,c=0;c<Yv.length;++c){var v=o[Yv[c]];if(v!=null&&!N(v)&&!z(v)&&!vt(v)&&typeof v!="boolean"){h=!1;break}f.push(v)}var d;if(h){d=f.join(",")+(n?"-svg":"");var y=$v.get(d);y&&(n?u.svgElement=y:u.image=y)}var p=Ng(o.dashArrayX),g=Fx(o.dashArrayY),m=Bg(o.symbol),_=zx(p),S=Fg(g),b=!n&&Li.createCanvas(),w=n&&{tag:"g",attrs:{},key:"dcl",children:[]},x=T(),C;b&&(b.width=x.width*e,b.height=x.height*e,C=b.getContext("2d")),D(),h&&$v.put(d,b||w),u.image=b,u.svgElement=w,u.svgWidth=x.width,u.svgHeight=x.height;function T(){for(var M=1,L=0,I=_.length;L<I;++L)M=ih(M,_[L]);for(var P=1,L=0,I=m.length;L<I;++L)P=ih(P,m[L].length);M*=P;var R=S*_.length*m.length;return{width:Math.max(1,Math.min(M,o.maxTileWidth)),height:Math.max(1,Math.min(R,o.maxTileHeight))}}function D(){C&&(C.clearRect(0,0,b.width,b.height),o.backgroundColor&&(C.fillStyle=o.backgroundColor,C.fillRect(0,0,b.width,b.height)));for(var M=0,L=0;L<g.length;++L)M+=g[L];if(M<=0)return;for(var I=-S,P=0,R=0,E=0;I<x.height;){if(P%2===0){for(var G=R/2%m.length,B=0,F=0,W=0;B<x.width*2;){for(var q=0,L=0;L<p[E].length;++L)q+=p[E][L];if(q<=0)break;if(F%2===0){var K=(1-o.symbolSize)*.5,nt=B+p[E][F]*K,lt=I+g[P]*K,ct=p[E][F]*o.symbolSize,ae=g[P]*o.symbolSize,or=W/2%m[G].length;qr(nt,lt,ct,ae,m[G][or])}B+=p[E][F],++W,++F,F===p[E].length&&(F=0)}++E,E===p.length&&(E=0)}I+=g[P],++R,++P,P===g.length&&(P=0)}function qr(Ut,_t,U,Q,sr){var Lt=n?1:e,bf=Mi(sr,Ut*Lt,_t*Lt,U*Lt,Q*Lt,o.color,o.symbolKeepAspect);if(n){var xf=i.painter.renderOneToVNode(bf);xf&&w.children.push(xf)}else Og(C,bf)}}}}function Bg(r){if(!r||r.length===0)return[["rect"]];if(z(r))return[[r]];for(var t=!0,e=0;e<r.length;++e)if(!z(r[e])){t=!1;break}if(t)return Bg([r]);for(var i=[],e=0;e<r.length;++e)z(r[e])?i.push([r[e]]):i.push(r[e]);return i}function Ng(r){if(!r||r.length===0)return[[0,0]];if(vt(r)){var t=Math.ceil(r);return[[t,t]]}for(var e=!0,i=0;i<r.length;++i)if(!vt(r[i])){e=!1;break}if(e)return Ng([r]);for(var n=[],i=0;i<r.length;++i)if(vt(r[i])){var t=Math.ceil(r[i]);n.push([t,t])}else{var t=V(r[i],function(s){return Math.ceil(s)});t.length%2===1?n.push(t.concat(t)):n.push(t)}return n}function Fx(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(vt(r)){var t=Math.ceil(r);return[t,t]}var e=V(r,function(i){return Math.ceil(i)});return r.length%2?e.concat(e):e}function zx(r){return V(r,function(t){return Fg(t)})}function Fg(r){for(var t=0,e=0;e<r.length;++e)t+=r[e];return r.length%2===1?t*2:t}function Hx(r,t){r.eachRawSeries(function(e){if(!r.isSeriesFiltered(e)){var i=e.getData();i.hasItemVisual()&&i.each(function(o){var s=i.getItemVisual(o,"decal");if(s){var l=i.ensureUniqueItemVisual(o,"style");l.decal=Zl(s,t)}});var n=i.getVisual("decal");if(n){var a=i.getVisual("style");a.decal=Zl(n,t)}}})}var Gx=new Te;const fe=Gx;var zg={};function Vx(r,t){zg[r]=t}function Wx(r){return zg[r]}var Ux=1,$x=800,Yx=900,Xx=1e3,Zx=2e3,qx=5e3,Hg=1e3,Kx=1100,lf=2e3,Gg=3e3,Qx=4e3,zo=4500,Jx=4600,jx=5e3,tT=6e3,Vg=7e3,eT={PROCESSOR:{FILTER:Xx,SERIES_FILTER:$x,STATISTIC:qx},VISUAL:{LAYOUT:Hg,PROGRESSIVE_LAYOUT:Kx,GLOBAL:lf,CHART:Gg,POST_CHART_LAYOUT:Jx,COMPONENT:Qx,BRUSH:jx,CHART_ITEM:zo,ARIA:tT,DECAL:Vg}},Tt="__flagInMainProcess",Bt="__pendingUpdate",zs="__needsUpdateStatus",Xv=/^[a-zA-Z0-9_]+$/,Hs="__connectUpdateStatus",Zv=0,rT=1,iT=2;function Wg(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.isDisposed()){this.id;return}return $g(this,r,t)}}function Ug(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return $g(this,r,t)}}function $g(r,t,e){return e[0]=e[0]&&e[0].toLowerCase(),Te.prototype[t].apply(r,e)}var Yg=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Te),Xg=Yg.prototype;Xg.on=Ug("on");Xg.off=Ug("off");var li,Gs,Sa,We,Vs,Ws,Us,Yi,Xi,qv,Kv,$s,Qv,wa,Jv,Zg,Xt,jv,qg=function(r){O(t,r);function t(e,i,n){var a=r.call(this,new ax)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],n=n||{},z(i)&&(i=Kg[i]),a._dom=e;var o="canvas",s="auto",l=!1;n.ssr;var u=a._zr=th(e,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:Z(n.useDirtyRect,l),useCoarsePointer:Z(n.useCoarsePointer,s),pointerSize:n.pointerSize});a._ssr=n.ssr,a._throttledZrFlush=sf(ft(u.flush,u),17),i=J(i),i&&og(i,!0),a._theme=i,a._locale=lw(n.locale||Np),a._coordSysMgr=new Ju;var f=a._api=Jv(a);function h(c,v){return c.__prio-v.__prio}return Ra(po,h),Ra(ql,h),a._scheduler=new Lg(a,f,ql,po),a._messageCenter=new Yg,a._initEvents(),a.resize=ft(a.resize,a),u.animation.on("frame",a._onframe,a),qv(u,a),Kv(u,a),ll(a),a}return t.prototype._onframe=function(){if(!this._disposed){jv(this);var e=this._scheduler;if(this[Bt]){var i=this[Bt].silent;this[Tt]=!0;try{li(this),We.update.call(this,null,this[Bt].updateParams)}catch(l){throw this[Tt]=!1,this[Bt]=null,l}this._zr.flush(),this[Tt]=!1,this[Bt]=null,Yi.call(this,i),Xi.call(this,i)}else if(e.unfinished){var n=Ux,a=this._model,o=this._api;e.unfinished=!1;do{var s=+new Date;e.performSeriesTasks(a),e.performDataProcessorTasks(a),Ws(this,a),e.performVisualTasks(a),wa(this,this._model,o,"remain",{}),n-=+new Date-s}while(n>0&&e.unfinished);e.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(e,i,n){if(!this[Tt]){if(this._disposed){this.id;return}var a,o,s;if(H(i)&&(n=i.lazyUpdate,a=i.silent,o=i.replaceMerge,s=i.transition,i=i.notMerge),this[Tt]=!0,!this._model||i){var l=new Gw(this._api),u=this._theme,f=this._model=new ig;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,u,this._locale,l)}this._model.setOption(e,{replaceMerge:o},Kl);var h={seriesTransition:s,optionChanged:!0};if(n)this[Bt]={silent:a,updateParams:h},this[Tt]=!1,this.getZr().wakeUp();else{try{li(this),We.update.call(this,null,h)}catch(c){throw this[Bt]=null,this[Tt]=!1,c}this._ssr||this._zr.flush(),this[Bt]=null,this[Tt]=!1,Yi.call(this,a),Xi.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||Y.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(e){return this.renderToCanvas(e)},t.prototype.renderToCanvas=function(e){e=e||{};var i=this._zr.painter;return i.getRenderedCanvas({backgroundColor:e.backgroundColor||this._model.get("backgroundColor"),pixelRatio:e.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(e){e=e||{};var i=this._zr.painter;return i.renderToString({useViewBox:e.useViewBox})},t.prototype.getSvgDataURL=function(){if(Y.svgSupported){var e=this._zr,i=e.storage.getDisplayList();return A(i,function(n){n.stopAnimation(null,!0)}),e.painter.toDataURL()}},t.prototype.getDataURL=function(e){if(this._disposed){this.id;return}e=e||{};var i=e.excludeComponents,n=this._model,a=[],o=this;A(i,function(l){n.eachComponent({mainType:l},function(u){var f=o._componentsMap[u.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(e).toDataURL("image/"+(e&&e.type||"png"));return A(a,function(l){l.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(e){if(this._disposed){this.id;return}var i=e.type==="svg",n=this.group,a=Math.min,o=Math.max,s=1/0;if(tc[n]){var l=s,u=s,f=-s,h=-s,c=[],v=e&&e.pixelRatio||this.getDevicePixelRatio();A(wn,function(_,S){if(_.group===n){var b=i?_.getZr().painter.getSvgDom().innerHTML:_.renderToCanvas(J(e)),w=_.getDom().getBoundingClientRect();l=a(w.left,l),u=a(w.top,u),f=o(w.right,f),h=o(w.bottom,h),c.push({dom:b,left:w.left,top:w.top})}}),l*=v,u*=v,f*=v,h*=v;var d=f-l,y=h-u,p=Li.createCanvas(),g=th(p,{renderer:i?"svg":"canvas"});if(g.resize({width:d,height:y}),i){var m="";return A(c,function(_){var S=_.left-l,b=_.top-u;m+='<g transform="translate('+S+","+b+')">'+_.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=m,e.connectedBackgroundColor&&g.painter.setBackgroundColor(e.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()}else return e.connectedBackgroundColor&&g.add(new bt({shape:{x:0,y:0,width:d,height:y},style:{fill:e.connectedBackgroundColor}})),A(c,function(_){var S=new Yr({style:{x:_.left*v-l,y:_.top*v-u,image:_.dom}});g.add(S)}),g.refreshImmediately(),p.toDataURL("image/"+(e&&e.type||"png"))}else return this.getDataURL(e)},t.prototype.convertToPixel=function(e,i){return Vs(this,"convertToPixel",e,i)},t.prototype.convertFromPixel=function(e,i){return Vs(this,"convertFromPixel",e,i)},t.prototype.containPixel=function(e,i){if(this._disposed){this.id;return}var n=this._model,a,o=hs(n,e);return A(o,function(s,l){l.indexOf("Models")>=0&&A(s,function(u){var f=u.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(i);else if(l==="seriesModels"){var h=this._chartsMap[u.__viewId];h&&h.containPoint&&(a=a||h.containPoint(i,u))}},this)},this),!!a},t.prototype.getVisual=function(e,i){var n=this._model,a=hs(n,e,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),l=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return l!=null?lx(s,l,i):ux(s,i)},t.prototype.getViewOfComponentModel=function(e){return this._componentsMap[e.__viewId]},t.prototype.getViewOfSeriesModel=function(e){return this._chartsMap[e.__viewId]},t.prototype._initEvents=function(){var e=this;A(nT,function(i){var n=function(a){var o=e.getModel(),s=a.target,l,u=i==="globalout";if(u?l={}:s&&un(s,function(d){var y=rt(d);if(y&&y.dataIndex!=null){var p=y.dataModel||o.getSeriesByIndex(y.seriesIndex);return l=p&&p.getDataParams(y.dataIndex,y.dataType,s)||{},!0}else if(y.eventData)return l=k({},y.eventData),!0},!0),l){var f=l.componentType,h=l.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=l.seriesIndex);var c=f&&h!=null&&o.getComponent(f,h),v=c&&e[c.mainType==="series"?"_chartsMap":"_componentsMap"][c.__viewId];l.event=a,l.type=i,e._$eventProcessor.eventInfo={targetEl:s,packedEvent:l,model:c,view:v},e.trigger(i,l)}};n.zrEventfulCallAtLast=!0,e._zr.on(i,n,e)}),A(Sn,function(i,n){e._messageCenter.on(n,function(a){this.trigger(n,a)},e)}),A(["selectchanged"],function(i){e._messageCenter.on(i,function(n){this.trigger(i,n)},e)}),fx(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var e=this.getDom();e&&Fd(this.getDom(),ff,"");var i=this,n=i._api,a=i._model;A(i._componentsViews,function(o){o.dispose(a,n)}),A(i._chartsViews,function(o){o.dispose(a,n)}),i._zr.dispose(),i._dom=i._model=i._chartsMap=i._componentsMap=i._chartsViews=i._componentsViews=i._scheduler=i._api=i._zr=i._throttledZrFlush=i._theme=i._coordSysMgr=i._messageCenter=null,delete wn[i.id]},t.prototype.resize=function(e){if(!this[Tt]){if(this._disposed){this.id;return}this._zr.resize(e);var i=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!i){var n=i.resetOption("media"),a=e&&e.silent;this[Bt]&&(a==null&&(a=this[Bt].silent),n=!0,this[Bt]=null),this[Tt]=!0;try{n&&li(this),We.update.call(this,{type:"resize",animation:k({duration:0},e&&e.animation)})}catch(o){throw this[Tt]=!1,o}this[Tt]=!1,Yi.call(this,a),Xi.call(this,a)}}},t.prototype.showLoading=function(e,i){if(this._disposed){this.id;return}if(H(e)&&(i=e,e=""),e=e||"default",this.hideLoading(),!!Ql[e]){var n=Ql[e](this._api,i),a=this._zr;this._loadingFX=n,a.add(n)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(e){var i=k({},e);return i.type=Sn[e.type],i},t.prototype.dispatchAction=function(e,i){if(this._disposed){this.id;return}if(H(i)||(i={silent:!!i}),!!co[e.type]&&this._model){if(this[Tt]){this._pendingActions.push(e);return}var n=i.silent;Us.call(this,e,n);var a=i.flush;a?this._zr.flush():a!==!1&&Y.browser.weChat&&this._throttledZrFlush(),Yi.call(this,n),Xi.call(this,n)}},t.prototype.updateLabelLayout=function(){fe.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(e){if(this._disposed){this.id;return}var i=e.seriesIndex,n=this.getModel(),a=n.getSeriesByIndex(i);a.appendData(e),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){li=function(h){var c=h._scheduler;c.restorePipelines(h._model),c.prepareStageTasks(),Gs(h,!0),Gs(h,!1),c.plan()},Gs=function(h,c){for(var v=h._model,d=h._scheduler,y=c?h._componentsViews:h._chartsViews,p=c?h._componentsMap:h._chartsMap,g=h._zr,m=h._api,_=0;_<y.length;_++)y[_].__alive=!1;c?v.eachComponent(function(w,x){w!=="series"&&S(x)}):v.eachSeries(S);function S(w){var x=w.__requireNewView;w.__requireNewView=!1;var C="_ec_"+w.id+"_"+w.type,T=!x&&p[C];if(!T){var D=Se(w.type),M=c?Be.getClass(D.main,D.sub):rr.getClass(D.sub);T=new M,T.init(v,m),p[C]=T,y.push(T),g.add(T.group)}w.__viewId=T.__id=C,T.__alive=!0,T.__model=w,T.group.__ecComponentInfo={mainType:w.mainType,index:w.componentIndex},!c&&d.prepareView(T,w,v,m)}for(var _=0;_<y.length;){var b=y[_];b.__alive?_++:(!c&&b.renderTask.dispose(),g.remove(b.group),b.dispose(v,m),y.splice(_,1),p[b.__id]===b&&delete p[b.__id],b.__id=b.group.__ecComponentInfo=null)}},Sa=function(h,c,v,d,y){var p=h._model;if(p.setUpdatePayload(v),!d){A([].concat(h._componentsViews).concat(h._chartsViews),b);return}var g={};g[d+"Id"]=v[d+"Id"],g[d+"Index"]=v[d+"Index"],g[d+"Name"]=v[d+"Name"];var m={mainType:d,query:g};y&&(m.subType=y);var _=v.excludeSeriesId,S;_!=null&&(S=X(),A(Rt(_),function(w){var x=we(w,null);x!=null&&S.set(x,!0)})),p&&p.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;if(!x)if(Eh(v))if(w instanceof kn)v.type===Or&&!v.notBlur&&!w.get(["emphasis","disabled"])&&B1(w,v,h._api);else{var C=Ru(w.mainType,w.componentIndex,v.name,h._api),T=C.focusSelf,D=C.dispatchers;v.type===Or&&T&&!v.notBlur&&Il(w.mainType,w.componentIndex,h._api),D&&A(D,function(M){v.type===Or?ro(M):io(M)})}else kl(v)&&w instanceof kn&&(z1(w,v,h._api),Ih(w),Xt(h))},h),p&&p.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;x||b(h[d==="series"?"_chartsMap":"_componentsMap"][w.__viewId])},h);function b(w){w&&w.__alive&&w[c]&&w[c](w.__model,p,h._api,v)}},We={prepareAndUpdate:function(h){li(this),We.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,c){var v=this._model,d=this._api,y=this._zr,p=this._coordSysMgr,g=this._scheduler;if(v){v.setUpdatePayload(h),g.restoreData(v,h),g.performSeriesTasks(v),p.create(v,d),g.performDataProcessorTasks(v,h),Ws(this,v),p.update(v,d),e(v),g.performVisualTasks(v,h),$s(this,v,d,h,c);var m=v.get("backgroundColor")||"transparent",_=v.get("darkMode");y.setBackgroundColor(m),_!=null&&_!=="auto"&&y.setDarkMode(_),fe.trigger("afterupdate",v,d)}},updateTransform:function(h){var c=this,v=this._model,d=this._api;if(v){v.setUpdatePayload(h);var y=[];v.eachComponent(function(g,m){if(g!=="series"){var _=c.getViewOfComponentModel(m);if(_&&_.__alive)if(_.updateTransform){var S=_.updateTransform(m,v,d,h);S&&S.update&&y.push(_)}else y.push(_)}});var p=X();v.eachSeries(function(g){var m=c._chartsMap[g.__viewId];if(m.updateTransform){var _=m.updateTransform(g,v,d,h);_&&_.update&&p.set(g.uid,1)}else p.set(g.uid,1)}),e(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0,dirtyMap:p}),wa(this,v,d,h,{},p),fe.trigger("afterupdate",v,d)}},updateView:function(h){var c=this._model;c&&(c.setUpdatePayload(h),rr.markUpdateMethod(h,"updateView"),e(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0}),$s(this,c,this._api,h,{}),fe.trigger("afterupdate",c,this._api))},updateVisual:function(h){var c=this,v=this._model;v&&(v.setUpdatePayload(h),v.eachSeries(function(d){d.getData().clearAllVisual()}),rr.markUpdateMethod(h,"updateVisual"),e(v),this._scheduler.performVisualTasks(v,h,{visualType:"visual",setDirty:!0}),v.eachComponent(function(d,y){if(d!=="series"){var p=c.getViewOfComponentModel(y);p&&p.__alive&&p.updateVisual(y,v,c._api,h)}}),v.eachSeries(function(d){var y=c._chartsMap[d.__viewId];y.updateVisual(d,v,c._api,h)}),fe.trigger("afterupdate",v,this._api))},updateLayout:function(h){We.update.call(this,h)}},Vs=function(h,c,v,d){if(h._disposed){h.id;return}for(var y=h._model,p=h._coordSysMgr.getCoordinateSystems(),g,m=hs(y,v),_=0;_<p.length;_++){var S=p[_];if(S[c]&&(g=S[c](y,m,d))!=null)return g}},Ws=function(h,c){var v=h._chartsMap,d=h._scheduler;c.eachSeries(function(y){d.updateStreamModes(y,v[y.__viewId])})},Us=function(h,c){var v=this,d=this.getModel(),y=h.type,p=h.escapeConnect,g=co[y],m=g.actionInfo,_=(m.update||"update").split(":"),S=_.pop(),b=_[0]!=null&&Se(_[0]);this[Tt]=!0;var w=[h],x=!1;h.batch&&(x=!0,w=V(h.batch,function(P){return P=it(k({},P),h),P.batch=null,P}));var C=[],T,D=kl(h),M=Eh(h);if(M&&sp(this._api),A(w,function(P){if(T=g.action(P,v._model,v._api),T=T||k({},P),T.type=m.event||T.type,C.push(T),M){var R=Mu(h),E=R.queryOptionMap,G=R.mainTypeSpecified,B=G?E.keys()[0]:"series";Sa(v,S,P,B),Xt(v)}else D?(Sa(v,S,P,"series"),Xt(v)):b&&Sa(v,S,P,b.main,b.sub)}),S!=="none"&&!M&&!D&&!b)try{this[Bt]?(li(this),We.update.call(this,h),this[Bt]=null):We[S].call(this,h)}catch(P){throw this[Tt]=!1,P}if(x?T={type:m.event||y,escapeConnect:p,batch:C}:T=C[0],this[Tt]=!1,!c){var L=this._messageCenter;if(L.trigger(T.type,T),D){var I={type:"selectchanged",escapeConnect:p,selected:H1(d),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};L.trigger(I.type,I)}}},Yi=function(h){for(var c=this._pendingActions;c.length;){var v=c.shift();Us.call(this,v,h)}},Xi=function(h){!h&&this.trigger("updated")},qv=function(h,c){h.on("rendered",function(v){c.trigger("rendered",v),h.animation.isFinished()&&!c[Bt]&&!c._scheduler.unfinished&&!c._pendingActions.length&&c.trigger("finished")})},Kv=function(h,c){h.on("mouseover",function(v){var d=v.target,y=un(d,El);y&&(N1(y,v,c._api),Xt(c))}).on("mouseout",function(v){var d=v.target,y=un(d,El);y&&(F1(y,v,c._api),Xt(c))}).on("click",function(v){var d=v.target,y=un(d,function(m){return rt(m).dataIndex!=null},!0);if(y){var p=y.selected?"unselect":"select",g=rt(y);c._api.dispatchAction({type:p,dataType:g.dataType,dataIndexInside:g.dataIndex,seriesIndex:g.seriesIndex,isFromClick:!0})}})};function e(h){h.clearColorPalette(),h.eachSeries(function(c){c.clearColorPalette()})}function i(h){var c=[],v=[],d=!1;if(h.eachComponent(function(m,_){var S=_.get("zlevel")||0,b=_.get("z")||0,w=_.getZLevelKey();d=d||!!w,(m==="series"?v:c).push({zlevel:S,z:b,idx:_.componentIndex,type:m,key:w})}),d){var y=c.concat(v),p,g;Ra(y,function(m,_){return m.zlevel===_.zlevel?m.z-_.z:m.zlevel-_.zlevel}),A(y,function(m){var _=h.getComponent(m.type,m.idx),S=m.zlevel,b=m.key;p!=null&&(S=Math.max(p,S)),b?(S===p&&b!==g&&S++,g=b):g&&(S===p&&S++,g=""),p=S,_.setZLevel(S)})}}$s=function(h,c,v,d,y){i(c),Qv(h,c,v,d,y),A(h._chartsViews,function(p){p.__alive=!1}),wa(h,c,v,d,y),A(h._chartsViews,function(p){p.__alive||p.remove(c,v)})},Qv=function(h,c,v,d,y,p){A(p||h._componentsViews,function(g){var m=g.__model;u(m,g),g.render(m,c,v,d),s(m,g),f(m,g)})},wa=function(h,c,v,d,y,p){var g=h._scheduler;y=k(y||{},{updatedSeries:c.getSeries()}),fe.trigger("series:beforeupdate",c,v,y);var m=!1;c.eachSeries(function(_){var S=h._chartsMap[_.__viewId];S.__alive=!0;var b=S.renderTask;g.updatePayload(b,d),u(_,S),p&&p.get(_.uid)&&b.dirty(),b.perform(g.getPerformArgs(b))&&(m=!0),S.group.silent=!!_.get("silent"),o(_,S),Ih(_)}),g.unfinished=m||g.unfinished,fe.trigger("series:layoutlabels",c,v,y),fe.trigger("series:transition",c,v,y),c.eachSeries(function(_){var S=h._chartsMap[_.__viewId];s(_,S),f(_,S)}),a(h,c),fe.trigger("series:afterupdate",c,v,y)},Xt=function(h){h[zs]=!0,h.getZr().wakeUp()},jv=function(h){h[zs]&&(h.getZr().storage.traverse(function(c){gn(c)||n(c)}),h[zs]=!1)};function n(h){for(var c=[],v=h.currentStates,d=0;d<v.length;d++){var y=v[d];y==="emphasis"||y==="blur"||y==="select"||c.push(y)}h.selected&&h.states.select&&c.push("select"),h.hoverState===To&&h.states.emphasis?c.push("emphasis"):h.hoverState===xo&&h.states.blur&&c.push("blur"),h.useStates(c)}function a(h,c){var v=h._zr,d=v.storage,y=0;d.traverse(function(p){p.isGroup||y++}),y>c.get("hoverLayerThreshold")&&!Y.node&&!Y.worker&&c.eachSeries(function(p){if(!p.preventUsingHoverLayer){var g=h._chartsMap[p.__viewId];g.__alive&&g.eachRendered(function(m){m.states.emphasis&&(m.states.emphasis.hoverLayer=!0)})}})}function o(h,c){var v=h.get("blendMode")||null;c.eachRendered(function(d){d.isGroup||(d.style.blend=v)})}function s(h,c){if(!h.preventAutoZ){var v=h.get("z")||0,d=h.get("zlevel")||0;c.eachRendered(function(y){return l(y,v,d,-1/0),!0})}}function l(h,c,v,d){var y=h.getTextContent(),p=h.getTextGuideLine(),g=h.isGroup;if(g)for(var m=h.childrenRef(),_=0;_<m.length;_++)d=Math.max(l(m[_],c,v,d),d);else h.z=c,h.zlevel=v,d=Math.max(h.z2,d);if(y&&(y.z=c,y.zlevel=v,isFinite(d)&&(y.z2=d+2)),p){var S=h.textGuideLineConfig;p.z=c,p.zlevel=v,isFinite(d)&&(p.z2=d+(S&&S.showAbove?1:-1))}return d}function u(h,c){c.eachRendered(function(v){if(!gn(v)){var d=v.getTextContent(),y=v.getTextGuideLine();v.stateTransition&&(v.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),y&&y.stateTransition&&(y.stateTransition=null),v.hasState()?(v.prevStates=v.currentStates,v.clearStates()):v.prevStates&&(v.prevStates=null)}})}function f(h,c){var v=h.getModel("stateAnimation"),d=h.isAnimationEnabled(),y=v.get("duration"),p=y>0?{duration:y,delay:v.get("delay"),easing:v.get("easing")}:null;c.eachRendered(function(g){if(g.states&&g.states.emphasis){if(gn(g))return;if(g instanceof st&&$1(g),g.__dirty){var m=g.prevStates;m&&g.useStates(m)}if(d){g.stateTransition=p;var _=g.getTextContent(),S=g.getTextGuideLine();_&&(_.stateTransition=p),S&&(S.stateTransition=p)}g.__dirty&&n(g)}})}Jv=function(h){return new(function(c){O(v,c);function v(){return c!==null&&c.apply(this,arguments)||this}return v.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},v.prototype.getComponentByElement=function(d){for(;d;){var y=d.__ecComponentInfo;if(y!=null)return h._model.getComponent(y.mainType,y.index);d=d.parent}},v.prototype.enterEmphasis=function(d,y){ro(d,y),Xt(h)},v.prototype.leaveEmphasis=function(d,y){io(d,y),Xt(h)},v.prototype.enterBlur=function(d){O1(d),Xt(h)},v.prototype.leaveBlur=function(d){ip(d),Xt(h)},v.prototype.enterSelect=function(d){np(d),Xt(h)},v.prototype.leaveSelect=function(d){ap(d),Xt(h)},v.prototype.getModel=function(){return h.getModel()},v.prototype.getViewOfComponentModel=function(d){return h.getViewOfComponentModel(d)},v.prototype.getViewOfSeriesModel=function(d){return h.getViewOfSeriesModel(d)},v}(ng))(h)},Zg=function(h){function c(v,d){for(var y=0;y<v.length;y++){var p=v[y];p[Hs]=d}}A(Sn,function(v,d){h._messageCenter.on(d,function(y){if(tc[h.group]&&h[Hs]!==Zv){if(y&&y.escapeConnect)return;var p=h.makeActionFromEvent(y),g=[];A(wn,function(m){m!==h&&m.group===h.group&&g.push(m)}),c(g,Zv),A(g,function(m){m[Hs]!==rT&&m.dispatchAction(p)}),c(g,iT)}})})}}(),t}(Te),uf=qg.prototype;uf.on=Wg("on");uf.off=Wg("off");uf.one=function(r,t,e){var i=this;function n(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),i.off(r,n)}this.on.call(this,r,n,e)};var nT=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var co={},Sn={},ql=[],Kl=[],po=[],Kg={},Ql={},wn={},tc={},aT=+new Date-0,ff="_echarts_instance_";function oT(r,t,e){var i=!(e&&e.ssr);if(i){var n=sT(r);if(n)return n}var a=new qg(r,t,e);return a.id="ec_"+aT++,wn[a.id]=a,i&&Fd(r,ff,a.id),Zg(a),fe.trigger("afterinit",a),a}function sT(r){return wn[b_(r,ff)]}function Qg(r,t){Kg[r]=t}function Jg(r){at(Kl,r)<0&&Kl.push(r)}function jg(r,t){vf(ql,r,t,Zx)}function lT(r){hf("afterinit",r)}function uT(r){hf("afterupdate",r)}function hf(r,t){fe.on(r,t)}function Ei(r,t,e){$(t)&&(e=t,t="");var i=H(r)?r.type:[r,r={event:t}][0];r.event=(r.event||i).toLowerCase(),t=r.event,!Sn[t]&&(ke(Xv.test(i)&&Xv.test(t)),co[i]||(co[i]={action:e,actionInfo:r}),Sn[t]=i)}function fT(r,t){Ju.register(r,t)}function hT(r,t){vf(po,r,t,Hg,"layout")}function Zr(r,t){vf(po,r,t,Gg,"visual")}var ec=[];function vf(r,t,e,i,n){if(($(t)||H(t))&&(e=t,t=i),!(at(ec,e)>=0)){ec.push(e);var a=Lg.wrapStageHandler(e,n);a.__prio=t,a.__raw=e,r.push(a)}}function ty(r,t){Ql[r]=t}function vT(r,t,e){var i=Wx("registerMap");i&&i(r,t,e)}var cT=cb;Zr(lf,Wb);Zr(zo,Ub);Zr(zo,$b);Zr(lf,ox);Zr(zo,sx);Zr(Vg,Hx);Jg(og);jg(Yx,qw);ty("default",Yb);Ei({type:Or,event:Or,update:Or},Ht);Ei({type:za,event:za,update:za},Ht);Ei({type:cn,event:cn,update:cn},Ht);Ei({type:Ha,event:Ha,update:Ha},Ht);Ei({type:dn,event:dn,update:dn},Ht);Qg("light",ix);Qg("dark",nx);function Zi(r){return r==null?0:r.length||1}function rc(r){return r}var dT=function(){function r(t,e,i,n,a,o){this._old=t,this._new=e,this._oldKeyGetter=i||rc,this._newKeyGetter=n||rc,this.context=a,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(t){return this._add=t,this},r.prototype.update=function(t){return this._update=t,this},r.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},r.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},r.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},r.prototype.remove=function(t){return this._remove=t,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var t=this._old,e=this._new,i={},n=new Array(t.length),a=new Array(e.length);this._initIndexMap(t,null,n,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var o=0;o<t.length;o++){var s=n[o],l=i[s],u=Zi(l);if(u>1){var f=l.shift();l.length===1&&(i[s]=l[0]),this._update&&this._update(f,o)}else u===1?(i[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(a,i)},r.prototype._executeMultiple=function(){var t=this._old,e=this._new,i={},n={},a=[],o=[];this._initIndexMap(t,i,a,"_oldKeyGetter"),this._initIndexMap(e,n,o,"_newKeyGetter");for(var s=0;s<a.length;s++){var l=a[s],u=i[l],f=n[l],h=Zi(u),c=Zi(f);if(h>1&&c===1)this._updateManyToOne&&this._updateManyToOne(f,u),n[l]=null;else if(h===1&&c>1)this._updateOneToMany&&this._updateOneToMany(f,u),n[l]=null;else if(h===1&&c===1)this._update&&this._update(f,u),n[l]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(f,u),n[l]=null;else if(h>1)for(var v=0;v<h;v++)this._remove&&this._remove(u[v]);else this._remove&&this._remove(u)}this._performRestAdd(o,n)},r.prototype._performRestAdd=function(t,e){for(var i=0;i<t.length;i++){var n=t[i],a=e[n],o=Zi(a);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(a[s]);else o===1&&this._add&&this._add(a);e[n]=null}},r.prototype._initIndexMap=function(t,e,i,n){for(var a=this._diffModeMultiple,o=0;o<t.length;o++){var s="_ec_"+this[n](t[o],o);if(a||(i[o]=s),!!e){var l=e[s],u=Zi(l);u===0?(e[s]=o,a&&i.push(s)):u===1?e[s]=[l,o]:l.push(o)}}},r}();const pT=dT;var gT=function(){function r(t,e){this._encode=t,this._schema=e}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function yT(r,t){var e={},i=e.encode={},n=X(),a=[],o=[],s={};A(r.dimensions,function(c){var v=r.getDimensionInfo(c),d=v.coordDim;if(d){var y=v.coordDimIndex;Ys(i,d)[y]=c,v.isExtraCoord||(n.set(d,1),_T(v.type)&&(a[0]=c),Ys(s,d)[y]=r.getDimensionIndex(v.name)),v.defaultTooltip&&o.push(c)}Qp.each(function(p,g){var m=Ys(i,g),_=v.otherDims[g];_!=null&&_!==!1&&(m[_]=v.name)})});var l=[],u={};n.each(function(c,v){var d=i[v];u[v]=d[0],l=l.concat(d)}),e.dataDimsOnCoord=l,e.dataDimIndicesOnCoord=V(l,function(c){return r.getDimensionInfo(c).storeDimIndex}),e.encodeFirstDimNotExtra=u;var f=i.label;f&&f.length&&(a=f.slice());var h=i.tooltip;return h&&h.length?o=h.slice():o.length||(o=a.slice()),i.defaultedLabel=a,i.defaultedTooltip=o,e.userOutput=new gT(s,t),e}function Ys(r,t){return r.hasOwnProperty(t)||(r[t]=[]),r[t]}function mT(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function _T(r){return!(r==="ordinal"||r==="time")}var ST=function(){function r(t){this.otherDims={},t!=null&&k(this,t)}return r}();const Ua=ST;var wT=yt(),bT={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},ey=function(){function r(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=ny(this.source)))},r.prototype.getSourceDimensionIndex=function(t){return Z(this._dimNameMap.get(t),-1)},r.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},r.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=lg(this.source),i=!ay(t),n="",a=[],o=0,s=0;o<t;o++){var l=void 0,u=void 0,f=void 0,h=this.dimensions[s];if(h&&h.storeDimIndex===o)l=e?h.name:null,u=h.type,f=h.ordinalMeta,s++;else{var c=this.getSourceDimension(o);c&&(l=e?c.name:null,u=c.type)}a.push({property:l,type:u,ordinalMeta:f}),e&&l!=null&&(!h||!h.isCalculationCoord)&&(n+=i?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),n+="$",n+=bT[u]||"f",f&&(n+=f.uid),n+="$"}var v=this.source,d=[v.seriesLayoutBy,v.startIndex,n].join("$$");return{dimensions:a,hash:d}},r.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,i=0;e<this._fullDimCount;e++){var n=void 0,a=this.dimensions[i];if(a&&a.storeDimIndex===e)a.isCalculationCoord||(n=a.name),i++;else{var o=this.getSourceDimension(e);o&&(n=o.name)}t.push(n)}return t},r.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function ry(r){return r instanceof ey}function iy(r){for(var t=X(),e=0;e<(r||[]).length;e++){var i=r[e],n=H(i)?i.name:i;n!=null&&t.get(n)==null&&t.set(n,e)}return t}function ny(r){var t=wT(r);return t.dimNameMap||(t.dimNameMap=iy(r.dimensionsDefine))}function ay(r){return r>30}var qi=H,Ue=V,xT=typeof Int32Array>"u"?Array:Int32Array,TT="e\0\0",ic=-1,CT=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],DT=["_approximateExtent"],nc,ba,Ki,Qi,Xs,Ji,Zs,MT=function(){function r(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i,n=!1;ry(t)?(i=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(n=!0,i=t),i=i||["x","y"];for(var a={},o=[],s={},l=!1,u={},f=0;f<i.length;f++){var h=i[f],c=z(h)?new Ua({name:h}):h instanceof Ua?h:new Ua(h),v=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=v,c.coordDimIndex=0);var d=c.otherDims=c.otherDims||{};o.push(v),a[v]=c,u[v]!=null&&(l=!0),c.createInvertedIndices&&(s[v]=[]),d.itemName===0&&(this._nameDimIdx=f),d.itemId===0&&(this._idDimIdx=f),n&&(c.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=a,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted){var y=this._dimIdxToName=X();A(o,function(p){y.set(a[p].storeDimIndex,p)})}}return r.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(e==null)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var i=this._dimIdxToName.get(e);if(i!=null)return i;var n=this._schema.getSourceDimension(e);if(n)return n.name},r.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(e!=null)return e;if(t==null)return-1;var i=this._getDimInfo(t);return i?i.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},r.prototype._recognizeDimIndex=function(t){if(vt(t)||t!=null&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},r.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},r.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},r.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(i){return e.hasOwnProperty(i)?e[i]:void 0}:function(i){return e[i]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(t,e){var i=this._dimSummary;if(e==null)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return n?n[e]:null},r.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,i=e.encode[t];return(i||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(t,e,i){var n=this,a;if(t instanceof zl&&(a=t),!a){var o=this.dimensions,s=ju(t)||Gt(t)?new ug(t,o.length):t;a=new zl;var l=Ue(o,function(u){return{type:n._dimInfos[u].type,property:u}});a.initData(s,l,i)}this._store=a,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,a.count()),this._dimSummary=yT(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},r.prototype.appendValues=function(t,e){var i=this._store.appendValues(t,e&&e.length),n=i.start,a=i.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var s=n;s<a;s++){var l=s-n;this._nameList[s]=e[l],o&&Zs(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,i=0;i<e.length;i++){var n=this._dimInfos[e[i]];n.ordinalMeta&&t.collectOrdinalMeta(n.storeDimIndex,n.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return this._idDimIdx==null&&t.getSource().sourceFormat!==er&&!t.fillStorage},r.prototype._doInit=function(t,e){if(!(t>=e)){var i=this._store,n=i.getProvider();this._updateOrdinalMeta();var a=this._nameList,o=this._idList,s=n.getSource().sourceFormat,l=s===ne;if(l&&!n.pure)for(var u=[],f=t;f<e;f++){var h=n.getItem(f,u);if(!this.hasItemOption&&h_(h)&&(this.hasItemOption=!0),h){var c=h.name;a[f]==null&&c!=null&&(a[f]=we(c,null));var v=h.id;o[f]==null&&v!=null&&(o[f]=we(v,null))}}if(this._shouldMakeIdFromName())for(var f=t;f<e;f++)Zs(this,f);nc(this)}},r.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},r.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},r.prototype.setCalculationInfo=function(t,e){qi(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},r.prototype.getName=function(t){var e=this.getRawIndex(t),i=this._nameList[e];return i==null&&this._nameDimIdx!=null&&(i=Ki(this,this._nameDimIdx,e)),i==null&&(i=""),i},r.prototype._getCategory=function(t,e){var i=this._store.get(t,e),n=this._store.getOrdinalMeta(t);return n?n.categories[i]:i},r.prototype.getId=function(t){return ba(this,this.getRawIndex(t))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.get(n.storeDimIndex,e)},r.prototype.getByRawIndex=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.getByRawIndex(n.storeDimIndex,e)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},r.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},r.prototype.getValues=function(t,e){var i=this,n=this._store;return N(t)?n.getValues(Ue(t,function(a){return i._getStoreDimIndex(a)}),e):n.getValues(t)},r.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,i=0,n=e.length;i<n;i++)if(isNaN(this._store.get(e[i],t)))return!1;return!0},r.prototype.indexOfName=function(t){for(var e=0,i=this._store.count();e<i;e++)if(this.getName(e)===t)return e;return-1},r.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},r.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},r.prototype.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i&&i[e];return n==null||isNaN(n)?ic:n},r.prototype.indicesOfNearest=function(t,e,i){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,i)},r.prototype.each=function(t,e,i){$(t)&&(i=e,e=t,t=[]);var n=i||this,a=Ue(Qi(t),this._getStoreDimIndex,this);this._store.each(a,n?ft(e,n):e)},r.prototype.filterSelf=function(t,e,i){$(t)&&(i=e,e=t,t=[]);var n=i||this,a=Ue(Qi(t),this._getStoreDimIndex,this);return this._store=this._store.filter(a,n?ft(e,n):e),this},r.prototype.selectRange=function(t){var e=this,i={},n=ht(t);return A(n,function(a){var o=e._getStoreDimIndex(a);i[o]=t[a]}),this._store=this._store.selectRange(i),this},r.prototype.mapArray=function(t,e,i){$(t)&&(i=e,e=t,t=[]),i=i||this;var n=[];return this.each(t,function(){n.push(e&&e.apply(this,arguments))},i),n},r.prototype.map=function(t,e,i,n){var a=i||n||this,o=Ue(Qi(t),this._getStoreDimIndex,this),s=Ji(this);return s._store=this._store.map(o,a?ft(e,a):e),s},r.prototype.modify=function(t,e,i,n){var a=i||n||this,o=Ue(Qi(t),this._getStoreDimIndex,this);this._store.modify(o,a?ft(e,a):e)},r.prototype.downSample=function(t,e,i,n){var a=Ji(this);return a._store=this._store.downSample(this._getStoreDimIndex(t),e,i,n),a},r.prototype.minmaxDownSample=function(t,e){var i=Ji(this);return i._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),i},r.prototype.lttbDownSample=function(t,e){var i=Ji(this);return i._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),i},r.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},r.prototype.getItemModel=function(t){var e=this.hostModel,i=this.getRawDataItem(t);return new At(i,e,e&&e.ecModel)},r.prototype.diff=function(t){var e=this;return new pT(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(i){return ba(t,i)},function(i){return ba(e,i)})},r.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},r.prototype.setVisual=function(t,e){this._visual=this._visual||{},qi(t)?k(this._visual,t):this._visual[t]=e},r.prototype.getItemVisual=function(t,e){var i=this._itemVisuals[t],n=i&&i[e];return n??this.getVisual(e)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(t,e){var i=this._itemVisuals,n=i[t];n||(n=i[t]={});var a=n[e];return a==null&&(a=this.getVisual(e),N(a)?a=a.slice():qi(a)&&(a=k({},a)),n[e]=a),a},r.prototype.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};this._itemVisuals[t]=n,qi(e)?k(n,e):n[e]=i},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(t,e){qi(t)?k(this._layout,t):this._layout[t]=e},r.prototype.getLayout=function(t){return this._layout[t]},r.prototype.getItemLayout=function(t){return this._itemLayouts[t]},r.prototype.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?k(this._itemLayouts[t]||{},e):e},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(t,e){var i=this.hostModel&&this.hostModel.seriesIndex;C1(i,this.dataType,t,e),this._graphicEls[t]=e},r.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},r.prototype.eachItemGraphicEl=function(t,e){A(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},r.prototype.cloneShallow=function(t){return t||(t=new r(this._schema?this._schema:Ue(this.dimensions,this._getDimInfo,this),this.hostModel)),Xs(t,this),t._store=this._store,t},r.prototype.wrapMethod=function(t,e){var i=this[t];$(i)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var n=i.apply(this,arguments);return e.apply(this,[n].concat(pu(arguments)))})},r.internalField=function(){nc=function(t){var e=t._invertedIndicesMap;A(e,function(i,n){var a=t._dimInfos[n],o=a.ordinalMeta,s=t._store;if(o){i=e[n]=new xT(o.categories.length);for(var l=0;l<i.length;l++)i[l]=ic;for(var l=0;l<s.count();l++)i[s.get(a.storeDimIndex,l)]=l}})},Ki=function(t,e,i){return we(t._getCategory(e,i),null)},ba=function(t,e){var i=t._idList[e];return i==null&&t._idDimIdx!=null&&(i=Ki(t,t._idDimIdx,e)),i==null&&(i=TT+e),i},Qi=function(t){return N(t)||(t=t!=null?[t]:[]),t},Ji=function(t){var e=new r(t._schema?t._schema:Ue(t.dimensions,t._getDimInfo,t),t.hostModel);return Xs(e,t),e},Xs=function(t,e){A(CT.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,A(DT,function(i){t[i]=J(e[i])}),t._calculationInfo=k({},e._calculationInfo)},Zs=function(t,e){var i=t._nameList,n=t._idList,a=t._nameDimIdx,o=t._idDimIdx,s=i[e],l=n[e];if(s==null&&a!=null&&(i[e]=s=Ki(t,a,e)),l==null&&o!=null&&(n[e]=l=Ki(t,o,e)),l==null&&s!=null){var u=t._nameRepeatCount,f=u[s]=(u[s]||0)+1;l=s,f>1&&(l+="__ec__"+f),n[e]=l}}}(),r}();const AT=MT;function LT(r,t){ju(r)||(r=tf(r)),t=t||{};var e=t.coordDimensions||[],i=t.dimensionsDefine||r.dimensionsDefine||[],n=X(),a=[],o=PT(r,e,i,t.dimensionsCount),s=t.canOmitUnusedDimensions&&ay(o),l=i===r.dimensionsDefine,u=l?ny(r):iy(i),f=t.encodeDefine;!f&&t.encodeDefaulter&&(f=t.encodeDefaulter(r,o));for(var h=X(f),c=new dg(o),v=0;v<c.length;v++)c[v]=-1;function d(T){var D=c[T];if(D<0){var M=i[T],L=H(M)?M:{name:M},I=new Ua,P=L.name;P!=null&&u.get(P)!=null&&(I.name=I.displayName=P),L.type!=null&&(I.type=L.type),L.displayName!=null&&(I.displayName=L.displayName);var R=a.length;return c[T]=R,I.storeDimIndex=T,a.push(I),I}return a[D]}if(!s)for(var v=0;v<o;v++)d(v);h.each(function(T,D){var M=Rt(T).slice();if(M.length===1&&!z(M[0])&&M[0]<0){h.set(D,!1);return}var L=h.set(D,[]);A(M,function(I,P){var R=z(I)?u.get(I):I;R!=null&&R<o&&(L[P]=R,p(d(R),D,P))})});var y=0;A(e,function(T){var D,M,L,I;if(z(T))D=T,I={};else{I=T,D=I.name;var P=I.ordinalMeta;I.ordinalMeta=null,I=k({},I),I.ordinalMeta=P,M=I.dimsDef,L=I.otherDims,I.name=I.coordDim=I.coordDimIndex=I.dimsDef=I.otherDims=null}var R=h.get(D);if(R!==!1){if(R=Rt(R),!R.length)for(var E=0;E<(M&&M.length||1);E++){for(;y<o&&d(y).coordDim!=null;)y++;y<o&&R.push(y++)}A(R,function(G,B){var F=d(G);if(l&&I.type!=null&&(F.type=I.type),p(it(F,I),D,B),F.name==null&&M){var W=M[B];!H(W)&&(W={name:W}),F.name=F.displayName=W.name,F.defaultTooltip=W.defaultTooltip}L&&it(F.otherDims,L)})}});function p(T,D,M){Qp.get(D)!=null?T.otherDims[D]=M:(T.coordDim=D,T.coordDimIndex=M,n.set(D,!0))}var g=t.generateCoord,m=t.generateCoordCount,_=m!=null;m=g?m||1:0;var S=g||"value";function b(T){T.name==null&&(T.name=T.coordDim)}if(s)A(a,function(T){b(T)}),a.sort(function(T,D){return T.storeDimIndex-D.storeDimIndex});else for(var w=0;w<o;w++){var x=d(w),C=x.coordDim;C==null&&(x.coordDim=RT(S,n,_),x.coordDimIndex=0,(!g||m<=0)&&(x.isExtraCoord=!0),m--),b(x),x.type==null&&(tg(r,w)===St.Must||x.isExtraCoord&&(x.otherDims.itemName!=null||x.otherDims.seriesName!=null))&&(x.type="ordinal")}return IT(a),new ey({source:r,dimensions:a,fullDimensionCount:o,dimensionOmitted:s})}function IT(r){for(var t=X(),e=0;e<r.length;e++){var i=r[e],n=i.name,a=t.get(n)||0;a>0&&(i.name=n+(a-1)),a++,t.set(n,a)}}function PT(r,t,e,i){var n=Math.max(r.dimensionsDetectedCount||1,t.length,e.length,i||0);return A(t,function(a){var o;H(a)&&(o=a.dimsDef)&&(n=Math.max(n,o.length))}),n}function RT(r,t,e){if(e||t.hasKey(r)){for(var i=0;t.hasKey(r+i);)i++;r+=i}return t.set(r,!0),r}var ET=function(){function r(t){this.coordSysDims=[],this.axisMap=X(),this.categoryAxisMap=X(),this.coordSysName=t}return r}();function kT(r){var t=r.get("coordinateSystem"),e=new ET(t),i=OT[t];if(i)return i(r,e,e.axisMap,e.categoryAxisMap),e}var OT={cartesian2d:function(r,t,e,i){var n=r.getReferringComponents("xAxis",he).models[0],a=r.getReferringComponents("yAxis",he).models[0];t.coordSysDims=["x","y"],e.set("x",n),e.set("y",a),ui(n)&&(i.set("x",n),t.firstCategoryDimIndex=0),ui(a)&&(i.set("y",a),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},singleAxis:function(r,t,e,i){var n=r.getReferringComponents("singleAxis",he).models[0];t.coordSysDims=["single"],e.set("single",n),ui(n)&&(i.set("single",n),t.firstCategoryDimIndex=0)},polar:function(r,t,e,i){var n=r.getReferringComponents("polar",he).models[0],a=n.findAxisModel("radiusAxis"),o=n.findAxisModel("angleAxis");t.coordSysDims=["radius","angle"],e.set("radius",a),e.set("angle",o),ui(a)&&(i.set("radius",a),t.firstCategoryDimIndex=0),ui(o)&&(i.set("angle",o),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},geo:function(r,t,e,i){t.coordSysDims=["lng","lat"]},parallel:function(r,t,e,i){var n=r.ecModel,a=n.getComponent("parallel",r.get("parallelIndex")),o=t.coordSysDims=a.dimensions.slice();A(a.parallelAxisIndex,function(s,l){var u=n.getComponent("parallelAxis",s),f=o[l];e.set(f,u),ui(u)&&(i.set(f,u),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=l))})}};function ui(r){return r.get("type")==="category"}function BT(r,t,e){e=e||{};var i=e.byIndex,n=e.stackedCoordDimension,a,o,s;NT(t)?a=t:(o=t.schema,a=o.dimensions,s=t.store);var l=!!(r&&r.get("stack")),u,f,h,c;if(A(a,function(m,_){z(m)&&(a[_]=m={name:m}),l&&!m.isExtraCoord&&(!i&&!u&&m.ordinalMeta&&(u=m),!f&&m.type!=="ordinal"&&m.type!=="time"&&(!n||n===m.coordDim)&&(f=m))}),f&&!i&&!u&&(i=!0),f){h="__\0ecstackresult_"+r.id,c="__\0ecstackedover_"+r.id,u&&(u.createInvertedIndices=!0);var v=f.coordDim,d=f.type,y=0;A(a,function(m){m.coordDim===v&&y++});var p={name:h,coordDim:v,coordDimIndex:y,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length},g={name:c,coordDim:c,coordDimIndex:y+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length+1};o?(s&&(p.storeDimIndex=s.ensureCalculationDimension(c,d),g.storeDimIndex=s.ensureCalculationDimension(h,d)),o.appendCalculationDimension(p),o.appendCalculationDimension(g)):(a.push(p),a.push(g))}return{stackedDimension:f&&f.name,stackedByDimension:u&&u.name,isStackedByIndex:i,stackedOverDimension:c,stackResultDimension:h}}function NT(r){return!ry(r.schema)}function Ai(r,t){return!!t&&t===r.getCalculationInfo("stackedDimension")}function FT(r,t){return Ai(r,t)?r.getCalculationInfo("stackResultDimension"):t}function zT(r,t){var e=r.get("coordinateSystem"),i=Ju.get(e),n;return t&&t.coordSysDims&&(n=V(t.coordSysDims,function(a){var o={name:a},s=t.axisMap.get(a);if(s){var l=s.get("type");o.type=mT(l)}return o})),n||(n=i&&(i.getDimensionsInfo?i.getDimensionsInfo():i.dimensions.slice())||["x","y"]),n}function HT(r,t,e){var i,n;return e&&A(r,function(a,o){var s=a.coordDim,l=e.categoryAxisMap.get(s);l&&(i==null&&(i=o),a.ordinalMeta=l.getOrdinalMeta(),t&&(a.createInvertedIndices=!0)),a.otherDims.itemName!=null&&(n=!0)}),!n&&i!=null&&(r[i].otherDims.itemName=0),i}function GT(r,t,e){e=e||{};var i=t.getSourceManager(),n,a=!1;r?(a=!0,n=tf(r)):(n=i.getSource(),a=n.sourceFormat===ne);var o=kT(t),s=zT(t,o),l=e.useEncodeDefaulter,u=$(l)?l:l?mt(bw,s,t):null,f={coordDimensions:s,generateCoord:e.generateCoord,encodeDefine:t.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!a},h=LT(n,f),c=HT(h.dimensions,e.createInvertedIndices,o),v=a?null:i.getSharedDataStore(h),d=BT(t,{schema:h,store:v}),y=new AT(h,t);y.setCalculationInfo(d);var p=c!=null&&VT(n)?function(g,m,_,S){return S===c?_:this.defaultDimValueGetter(g,m,_,S)}:null;return y.hasItemOption=!1,y.initData(a?n:v,null,p),y}function VT(r){if(r.sourceFormat===ne){var t=WT(r.data||[]);return!N(Gn(t))}}function WT(r){for(var t=0;t<r.length&&r[t]==null;)t++;return r[t]}var oy=function(){function r(t){this._setting=t||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},r.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r}();So(oy);const Fe=oy;var UT=0,$T=function(){function r(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++UT}return r.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&V(i,YT);return new r({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})},r.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},r.prototype.parseAndCollect=function(t){var e,i=this._needCollect;if(!z(t)&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=this._getOrCreateMap();return e=n.get(t),e==null&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=NaN),e},r.prototype._getOrCreateMap=function(){return this._map||(this._map=X(this.categories))},r}();function YT(r){return H(r)&&r.value!=null?r.value:r+""}const Jl=$T;function jl(r){return r.type==="interval"||r.type==="log"}function XT(r,t,e,i){var n={},a=r[1]-r[0],o=n.interval=Ed(a/t,!0);e!=null&&o<e&&(o=n.interval=e),i!=null&&o>i&&(o=n.interval=i);var s=n.intervalPrecision=sy(o),l=n.niceTickExtent=[pt(Math.ceil(r[0]/o)*o,s),pt(Math.floor(r[1]/o)*o,s)];return ZT(l,r),n}function qs(r){var t=Math.pow(10,Cu(r)),e=r/t;return e?e===2?e=3:e===3?e=5:e*=2:e=1,pt(e*t)}function sy(r){return Le(r)+2}function ac(r,t,e){r[t]=Math.max(Math.min(r[t],e[1]),e[0])}function ZT(r,t){!isFinite(r[0])&&(r[0]=t[0]),!isFinite(r[1])&&(r[1]=t[1]),ac(r,0,t),ac(r,1,t),r[0]>r[1]&&(r[0]=r[1])}function Ho(r,t){return r>=t[0]&&r<=t[1]}function Go(r,t){return t[1]===t[0]?.5:(r-t[0])/(t[1]-t[0])}function Vo(r,t){return r*(t[1]-t[0])+t[0]}var ly=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;i.type="ordinal";var n=i.getSetting("ordinalMeta");return n||(n=new Jl({})),N(n)&&(n=new Jl({categories:V(n,function(a){return H(a)?a.value:a})})),i._ordinalMeta=n,i._extent=i.getSetting("extent")||[0,n.categories.length-1],i}return t.prototype.parse=function(e){return e==null?NaN:z(e)?this._ordinalMeta.getOrdinal(e):Math.round(e)},t.prototype.contain=function(e){return e=this.parse(e),Ho(e,this._extent)&&this._ordinalMeta.categories[e]!=null},t.prototype.normalize=function(e){return e=this._getTickNumber(this.parse(e)),Go(e,this._extent)},t.prototype.scale=function(e){return e=Math.round(Vo(e,this._extent)),this.getRawOrdinalNumber(e)},t.prototype.getTicks=function(){for(var e=[],i=this._extent,n=i[0];n<=i[1];)e.push({value:n}),n++;return e},t.prototype.getMinorTicks=function(e){},t.prototype.setSortInfo=function(e){if(e==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var i=e.ordinalNumbers,n=this._ordinalNumbersByTick=[],a=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,l=Math.min(s,i.length);o<l;++o){var u=i[o];n[o]=u,a[u]=o}for(var f=0;o<s;++o){for(;a[f]!=null;)f++;n.push(f),a[f]=o}},t.prototype._getTickNumber=function(e){var i=this._ticksByOrdinalNumber;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getRawOrdinalNumber=function(e){var i=this._ordinalNumbersByTick;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getLabel=function(e){if(!this.isBlank()){var i=this.getRawOrdinalNumber(e.value),n=this._ordinalMeta.categories[i];return n==null?"":n+""}},t.prototype.count=function(){return this._extent[1]-this._extent[0]+1},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.isInExtentRange=function(e){return e=this._getTickNumber(e),this._extent[0]<=e&&this._extent[1]>=e},t.prototype.getOrdinalMeta=function(){return this._ordinalMeta},t.prototype.calcNiceTicks=function(){},t.prototype.calcNiceExtent=function(){},t.type="ordinal",t}(Fe);Fe.registerClass(ly);const uy=ly;var Ar=pt,fy=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return Ho(e,this._extent)},t.prototype.normalize=function(e){return Go(e,this._extent)},t.prototype.scale=function(e){return Vo(e,this._extent)},t.prototype.setExtent=function(e,i){var n=this._extent;isNaN(e)||(n[0]=parseFloat(e)),isNaN(i)||(n[1]=parseFloat(i))},t.prototype.unionExtent=function(e){var i=this._extent;e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1]),this.setExtent(i[0],i[1])},t.prototype.getInterval=function(){return this._interval},t.prototype.setInterval=function(e){this._interval=e,this._niceExtent=this._extent.slice(),this._intervalPrecision=sy(e)},t.prototype.getTicks=function(e){var i=this._interval,n=this._extent,a=this._niceExtent,o=this._intervalPrecision,s=[];if(!i)return s;var l=1e4;n[0]<a[0]&&(e?s.push({value:Ar(a[0]-i,o)}):s.push({value:n[0]}));for(var u=a[0];u<=a[1]&&(s.push({value:u}),u=Ar(u+i,o),u!==s[s.length-1].value);)if(s.length>l)return[];var f=s.length?s[s.length-1].value:a[1];return n[1]>f&&(e?s.push({value:Ar(f+i,o)}):s.push({value:n[1]})),s},t.prototype.getMinorTicks=function(e){for(var i=this.getTicks(!0),n=[],a=this.getExtent(),o=1;o<i.length;o++){for(var s=i[o],l=i[o-1],u=0,f=[],h=s.value-l.value,c=h/e;u<e-1;){var v=Ar(l.value+(u+1)*c);v>a[0]&&v<a[1]&&f.push(v),u++}n.push(f)}return n},t.prototype.getLabel=function(e,i){if(e==null)return"";var n=i&&i.precision;n==null?n=Le(e.value)||0:n==="auto"&&(n=this._intervalPrecision);var a=Ar(e.value,n,!0);return Xp(a)},t.prototype.calcNiceTicks=function(e,i,n){e=e||5;var a=this._extent,o=a[1]-a[0];if(isFinite(o)){o<0&&(o=-o,a.reverse());var s=XT(a,e,i,n);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1])if(i[0]!==0){var n=Math.abs(i[0]);e.fixMax||(i[1]+=n/2),i[0]-=n/2}else i[1]=1;var a=i[1]-i[0];isFinite(a)||(i[0]=0,i[1]=1),this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval);var o=this._interval;e.fixMin||(i[0]=Ar(Math.floor(i[0]/o)*o)),e.fixMax||(i[1]=Ar(Math.ceil(i[1]/o)*o))},t.prototype.setNiceExtent=function(e,i){this._niceExtent=[e,i]},t.type="interval",t}(Fe);Fe.registerClass(fy);const $n=fy;var hy=typeof Float32Array<"u",qT=hy?Float32Array:Array;function Ie(r){return N(r)?hy?new Float32Array(r):r:new qT(r)}var KT="__ec_stack_";function cf(r){return r.get("stack")||KT+r.seriesIndex}function df(r){return r.dim+r.index}function vy(r,t){var e=[];return t.eachSeriesByType(r,function(i){dy(i)&&e.push(i)}),e}function QT(r){var t={};A(r,function(l){var u=l.coordinateSystem,f=u.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var h=l.getData(),c=f.dim+"_"+f.index,v=h.getDimensionIndex(h.mapDimension(f.dim)),d=h.getStore(),y=0,p=d.count();y<p;++y){var g=d.get(v,y);t[c]?t[c].push(g):t[c]=[g]}});var e={};for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n){n.sort(function(l,u){return l-u});for(var a=null,o=1;o<n.length;++o){var s=n[o]-n[o-1];s>0&&(a=a===null?s:Math.min(a,s))}e[i]=a}}return e}function cy(r){var t=QT(r),e=[];return A(r,function(i){var n=i.coordinateSystem,a=n.getBaseAxis(),o=a.getExtent(),s;if(a.type==="category")s=a.getBandWidth();else if(a.type==="value"||a.type==="time"){var l=a.dim+"_"+a.index,u=t[l],f=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),c=Math.abs(h[1]-h[0]);s=u?f/c*u:f}else{var v=i.getData();s=Math.abs(o[1]-o[0])/v.count()}var d=Pt(i.get("barWidth"),s),y=Pt(i.get("barMaxWidth"),s),p=Pt(i.get("barMinWidth")||(py(i)?.5:1),s),g=i.get("barGap"),m=i.get("barCategoryGap");e.push({bandWidth:s,barWidth:d,barMaxWidth:y,barMinWidth:p,barGap:g,barCategoryGap:m,axisKey:df(a),stackId:cf(i)})}),JT(e)}function JT(r){var t={};A(r,function(i,n){var a=i.axisKey,o=i.bandWidth,s=t[a]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},l=s.stacks;t[a]=s;var u=i.stackId;l[u]||s.autoWidthCount++,l[u]=l[u]||{width:0,maxWidth:0};var f=i.barWidth;f&&!l[u].width&&(l[u].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var h=i.barMaxWidth;h&&(l[u].maxWidth=h);var c=i.barMinWidth;c&&(l[u].minWidth=c);var v=i.barGap;v!=null&&(s.gap=v);var d=i.barCategoryGap;d!=null&&(s.categoryGap=d)});var e={};return A(t,function(i,n){e[n]={};var a=i.stacks,o=i.bandWidth,s=i.categoryGap;if(s==null){var l=ht(a).length;s=Math.max(35-l*4,15)+"%"}var u=Pt(s,o),f=Pt(i.gap,1),h=i.remainedWidth,c=i.autoWidthCount,v=(h-u)/(c+(c-1)*f);v=Math.max(v,0),A(a,function(g){var m=g.maxWidth,_=g.minWidth;if(g.width){var S=g.width;m&&(S=Math.min(S,m)),_&&(S=Math.max(S,_)),g.width=S,h-=S+f*S,c--}else{var S=v;m&&m<S&&(S=Math.min(m,h)),_&&_>S&&(S=_),S!==v&&(g.width=S,h-=S+f*S,c--)}}),v=(h-u)/(c+(c-1)*f),v=Math.max(v,0);var d=0,y;A(a,function(g,m){g.width||(g.width=v),y=g,d+=g.width*(1+f)}),y&&(d-=y.width*f);var p=-d/2;A(a,function(g,m){e[n][m]=e[n][m]||{bandWidth:o,offset:p,width:g.width},p+=g.width*(1+f)})}),e}function jT(r,t,e){if(r&&t){var i=r[df(t)];return i!=null&&e!=null?i[cf(e)]:i}}function BA(r,t){var e=vy(r,t),i=cy(e);A(e,function(n){var a=n.getData(),o=n.coordinateSystem,s=o.getBaseAxis(),l=cf(n),u=i[df(s)][l],f=u.offset,h=u.width;a.setLayout({bandWidth:u.bandWidth,offset:f,size:h})})}function NA(r){return{seriesType:r,plan:af(),reset:function(t){if(dy(t)){var e=t.getData(),i=t.coordinateSystem,n=i.getBaseAxis(),a=i.getOtherAxis(n),o=e.getDimensionIndex(e.mapDimension(a.dim)),s=e.getDimensionIndex(e.mapDimension(n.dim)),l=t.get("showBackground",!0),u=e.mapDimension(a.dim),f=e.getCalculationInfo("stackResultDimension"),h=Ai(e,u)&&!!e.getCalculationInfo("stackedOnSeries"),c=a.isHorizontal(),v=tC(n,a),d=py(t),y=t.get("barMinHeight")||0,p=f&&e.getDimensionIndex(f),g=e.getLayout("size"),m=e.getLayout("offset");return{progress:function(_,S){for(var b=_.count,w=d&&Ie(b*3),x=d&&l&&Ie(b*3),C=d&&Ie(b),T=i.master.getRect(),D=c?T.width:T.height,M,L=S.getStore(),I=0;(M=_.next())!=null;){var P=L.get(h?p:o,M),R=L.get(s,M),E=v,G=void 0;h&&(G=+P-L.get(o,M));var B=void 0,F=void 0,W=void 0,q=void 0;if(c){var K=i.dataToPoint([P,R]);if(h){var nt=i.dataToPoint([G,R]);E=nt[0]}B=E,F=K[1]+m,W=K[0]-E,q=g,Math.abs(W)<y&&(W=(W<0?-1:1)*y)}else{var K=i.dataToPoint([R,P]);if(h){var nt=i.dataToPoint([R,G]);E=nt[1]}B=K[0]+m,F=E,W=g,q=K[1]-E,Math.abs(q)<y&&(q=(q<=0?-1:1)*y)}d?(w[I]=B,w[I+1]=F,w[I+2]=c?W:q,x&&(x[I]=c?T.x:B,x[I+1]=c?F:T.y,x[I+2]=D),C[M]=M):S.setItemLayout(M,{x:B,y:F,width:W,height:q}),I+=3}d&&S.setLayout({largePoints:w,largeDataIndices:C,largeBackgroundPoints:x,valueAxisHorizontal:c})}}}}}}function dy(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function py(r){return r.pipelineContext&&r.pipelineContext.large}function tC(r,t){var e=t.model.get("startValue");return e||(e=0),t.toGlobalCoord(t.dataToCoord(t.type==="log"?e>0?e:1:e))}var eC=function(r,t,e,i){for(;e<i;){var n=e+i>>>1;r[n][1]<t?e=n+1:i=n}return e},gy=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="time",i}return t.prototype.getLabel=function(e){var i=this.getSetting("useUTC");return Lo(e.value,Kh[vw(xi(this._minLevelUnit))]||Kh.second,i,this.getSetting("locale"))},t.prototype.getFormattedLabel=function(e,i,n){var a=this.getSetting("useUTC"),o=this.getSetting("locale");return cw(e,i,n,o,a)},t.prototype.getTicks=function(){var e=this._interval,i=this._extent,n=[];if(!e)return n;n.push({value:i[0],level:0});var a=this.getSetting("useUTC"),o=lC(this._minLevelUnit,this._approxInterval,a,i);return n=n.concat(o),n.push({value:i[1],level:0}),n},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1]&&(i[0]-=re,i[1]+=re),i[1]===-1/0&&i[0]===1/0){var n=new Date;i[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),i[0]=i[1]-re}this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval)},t.prototype.calcNiceTicks=function(e,i,n){e=e||10;var a=this._extent,o=a[1]-a[0];this._approxInterval=o/e,i!=null&&this._approxInterval<i&&(this._approxInterval=i),n!=null&&this._approxInterval>n&&(this._approxInterval=n);var s=xa.length,l=Math.min(eC(xa,this._approxInterval,0,s),s-1);this._interval=xa[l][1],this._minLevelUnit=xa[Math.max(l-1,0)][0]},t.prototype.parse=function(e){return vt(e)?e:+Oe(e)},t.prototype.contain=function(e){return Ho(this.parse(e),this._extent)},t.prototype.normalize=function(e){return Go(this.parse(e),this._extent)},t.prototype.scale=function(e){return Vo(e,this._extent)},t.type="time",t}($n),xa=[["second",Yu],["minute",Xu],["hour",yn],["quarter-day",yn*6],["half-day",yn*12],["day",re*1.2],["half-week",re*3.5],["week",re*7],["month",re*31],["quarter",re*95],["half-year",qh/2],["year",qh]];function rC(r,t,e,i){var n=Oe(t),a=Oe(e),o=function(d){return Qh(n,d,i)===Qh(a,d,i)},s=function(){return o("year")},l=function(){return s()&&o("month")},u=function(){return l()&&o("day")},f=function(){return u()&&o("hour")},h=function(){return f()&&o("minute")},c=function(){return h()&&o("second")},v=function(){return c()&&o("millisecond")};switch(r){case"year":return s();case"month":return l();case"day":return u();case"hour":return f();case"minute":return h();case"second":return c();case"millisecond":return v()}}function iC(r,t){return r/=re,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function nC(r){var t=30*re;return r/=t,r>6?6:r>3?3:r>2?2:1}function aC(r){return r/=yn,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function oc(r,t){return r/=t?Xu:Yu,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function oC(r){return Ed(r,!0)}function sC(r,t,e){var i=new Date(r);switch(xi(t)){case"year":case"month":i[Gp(e)](0);case"day":i[Vp(e)](1);case"hour":i[Wp(e)](0);case"minute":i[Up(e)](0);case"second":i[$p(e)](0),i[Yp(e)](0)}return i.getTime()}function lC(r,t,e,i){var n=1e4,a=zp,o=0;function s(D,M,L,I,P,R,E){for(var G=new Date(M),B=M,F=G[I]();B<L&&B<=i[1];)E.push({value:B}),F+=D,G[P](F),B=G.getTime();E.push({value:B,notAdd:!0})}function l(D,M,L){var I=[],P=!M.length;if(!rC(xi(D),i[0],i[1],e)){P&&(M=[{value:sC(new Date(i[0]),D,e)},{value:i[1]}]);for(var R=0;R<M.length-1;R++){var E=M[R].value,G=M[R+1].value;if(E!==G){var B=void 0,F=void 0,W=void 0,q=!1;switch(D){case"year":B=Math.max(1,Math.round(t/re/365)),F=Zu(e),W=dw(e);break;case"half-year":case"quarter":case"month":B=nC(t),F=Ti(e),W=Gp(e);break;case"week":case"half-week":case"day":B=iC(t),F=Io(e),W=Vp(e),q=!0;break;case"half-day":case"quarter-day":case"hour":B=aC(t),F=Ln(e),W=Wp(e);break;case"minute":B=oc(t,!0),F=Po(e),W=Up(e);break;case"second":B=oc(t,!1),F=Ro(e),W=$p(e);break;case"millisecond":B=oC(t),F=Eo(e),W=Yp(e);break}s(B,E,G,F,W,q,I),D==="year"&&L.length>1&&R===0&&L.unshift({value:L[0].value-B})}}for(var R=0;R<I.length;R++)L.push(I[R]);return I}}for(var u=[],f=[],h=0,c=0,v=0;v<a.length&&o++<n;++v){var d=xi(a[v]);if(hw(a[v])){l(a[v],u[u.length-1]||[],f);var y=a[v+1]?xi(a[v+1]):null;if(d!==y){if(f.length){c=h,f.sort(function(D,M){return D.value-M.value});for(var p=[],g=0;g<f.length;++g){var m=f[g].value;(g===0||f[g-1].value!==m)&&(p.push(f[g]),m>=i[0]&&m<=i[1]&&h++)}var _=(i[1]-i[0])/t;if(h>_*1.5&&c>_/1.5||(u.push(p),h>_||r===a[v]))break}f=[]}}}for(var S=wt(V(u,function(D){return wt(D,function(M){return M.value>=i[0]&&M.value<=i[1]&&!M.notAdd})}),function(D){return D.length>0}),b=[],w=S.length-1,v=0;v<S.length;++v)for(var x=S[v],C=0;C<x.length;++C)b.push({value:x[C].value,level:w-v});b.sort(function(D,M){return D.value-M.value});for(var T=[],v=0;v<b.length;++v)(v===0||b[v].value!==b[v-1].value)&&T.push(b[v]);return T}Fe.registerClass(gy);const uC=gy;var sc=Fe.prototype,bn=$n.prototype,fC=pt,hC=Math.floor,vC=Math.ceil,Ta=Math.pow,se=Math.log,pf=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new $n,e._interval=0,e}return t.prototype.getTicks=function(e){var i=this._originalScale,n=this._extent,a=i.getExtent(),o=bn.getTicks.call(this,e);return V(o,function(s){var l=s.value,u=pt(Ta(this.base,l));return u=l===n[0]&&this._fixMin?Ca(u,a[0]):u,u=l===n[1]&&this._fixMax?Ca(u,a[1]):u,{value:u}},this)},t.prototype.setExtent=function(e,i){var n=se(this.base);e=se(Math.max(0,e))/n,i=se(Math.max(0,i))/n,bn.setExtent.call(this,e,i)},t.prototype.getExtent=function(){var e=this.base,i=sc.getExtent.call(this);i[0]=Ta(e,i[0]),i[1]=Ta(e,i[1]);var n=this._originalScale,a=n.getExtent();return this._fixMin&&(i[0]=Ca(i[0],a[0])),this._fixMax&&(i[1]=Ca(i[1],a[1])),i},t.prototype.unionExtent=function(e){this._originalScale.unionExtent(e);var i=this.base;e[0]=se(e[0])/se(i),e[1]=se(e[1])/se(i),sc.unionExtent.call(this,e)},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.calcNiceTicks=function(e){e=e||10;var i=this._extent,n=i[1]-i[0];if(!(n===1/0||n<=0)){var a=l_(n),o=e/n*a;for(o<=.5&&(a*=10);!isNaN(a)&&Math.abs(a)<1&&Math.abs(a)>0;)a*=10;var s=[pt(vC(i[0]/a)*a),pt(hC(i[1]/a)*a)];this._interval=a,this._niceExtent=s}},t.prototype.calcNiceExtent=function(e){bn.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return e=se(e)/se(this.base),Ho(e,this._extent)},t.prototype.normalize=function(e){return e=se(e)/se(this.base),Go(e,this._extent)},t.prototype.scale=function(e){return e=Vo(e,this._extent),Ta(this.base,e)},t.type="log",t}(Fe),yy=pf.prototype;yy.getMinorTicks=bn.getMinorTicks;yy.getLabel=bn.getLabel;function Ca(r,t){return fC(r,Le(t))}Fe.registerClass(pf);const cC=pf;var dC=function(){function r(t,e,i){this._prepareParams(t,e,i)}return r.prototype._prepareParams=function(t,e,i){i[1]<i[0]&&(i=[NaN,NaN]),this._dataMin=i[0],this._dataMax=i[1];var n=this._isOrdinal=t.type==="ordinal";this._needCrossZero=t.type==="interval"&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=e.get("min",!0);a==null&&(a=e.get("startValue",!0));var o=this._modelMinRaw=a;$(o)?this._modelMinNum=Da(t,o({min:i[0],max:i[1]})):o!=="dataMin"&&(this._modelMinNum=Da(t,o));var s=this._modelMaxRaw=e.get("max",!0);if($(s)?this._modelMaxNum=Da(t,s({min:i[0],max:i[1]})):s!=="dataMax"&&(this._modelMaxNum=Da(t,s)),n)this._axisDataLen=e.getCategories().length;else{var l=e.get("boundaryGap"),u=N(l)?l:[l||0,l||0];typeof u[0]=="boolean"||typeof u[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Hr(u[0],1),Hr(u[1],1)]}},r.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,i=this._dataMax,n=this._axisDataLen,a=this._boundaryGapInner,o=t?null:i-e||Math.abs(e),s=this._modelMinRaw==="dataMin"?e:this._modelMinNum,l=this._modelMaxRaw==="dataMax"?i:this._modelMaxNum,u=s!=null,f=l!=null;s==null&&(s=t?n?0:NaN:e-a[0]*o),l==null&&(l=t?n?n-1:NaN:i+a[1]*o),(s==null||!isFinite(s))&&(s=NaN),(l==null||!isFinite(l))&&(l=NaN);var h=Xa(s)||Xa(l)||t&&!n;this._needCrossZero&&(s>0&&l>0&&!u&&(s=0),s<0&&l<0&&!f&&(l=0));var c=this._determinedMin,v=this._determinedMax;return c!=null&&(s=c,u=!0),v!=null&&(l=v,f=!0),{min:s,max:l,minFixed:u,maxFixed:f,isBlank:h}},r.prototype.modifyDataMinMax=function(t,e){this[gC[t]]=e},r.prototype.setDeterminedMinMax=function(t,e){var i=pC[t];this[i]=e},r.prototype.freeze=function(){this.frozen=!0},r}(),pC={min:"_determinedMin",max:"_determinedMax"},gC={min:"_dataMin",max:"_dataMax"};function yC(r,t,e){var i=r.rawExtentInfo;return i||(i=new dC(r,t,e),r.rawExtentInfo=i,i)}function Da(r,t){return t==null?null:Xa(t)?NaN:r.parse(t)}function my(r,t){var e=r.type,i=yC(r,t,r.getExtent()).calculate();r.setBlank(i.isBlank);var n=i.min,a=i.max,o=t.ecModel;if(o&&e==="time"){var s=vy("bar",o),l=!1;if(A(s,function(h){l=l||h.getBaseAxis()===t.axis}),l){var u=cy(s),f=mC(n,a,t,u);n=f.min,a=f.max}}return{extent:[n,a],fixMin:i.minFixed,fixMax:i.maxFixed}}function mC(r,t,e,i){var n=e.axis.getExtent(),a=Math.abs(n[1]-n[0]),o=jT(i,e.axis);if(o===void 0)return{min:r,max:t};var s=1/0;A(o,function(v){s=Math.min(v.offset,s)});var l=-1/0;A(o,function(v){l=Math.max(v.offset+v.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,f=t-r,h=1-(s+l)/a,c=f/h-f;return t+=c*(l/u),r-=c*(s/u),{min:r,max:t}}function lc(r,t){var e=t,i=my(r,e),n=i.extent,a=e.get("splitNumber");r instanceof cC&&(r.base=e.get("logBase"));var o=r.type,s=e.get("interval"),l=o==="interval"||o==="time";r.setExtent(n[0],n[1]),r.calcNiceExtent({splitNumber:a,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:l?e.get("minInterval"):null,maxInterval:l?e.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function _C(r,t){if(t=t||r.get("type"),t)switch(t){case"category":return new uy({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new uC({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new(Fe.getClass(t)||$n)}}function SC(r){var t=r.scale.getExtent(),e=t[0],i=t[1];return!(e>0&&i>0||e<0&&i<0)}function ki(r){var t=r.getLabelModel().get("formatter"),e=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(i){return function(n,a){return r.scale.getFormattedLabel(n,a,i)}}(t):z(t)?function(i){return function(n){var a=r.scale.getLabel(n),o=i.replace("{value}",a??"");return o}}(t):$(t)?function(i){return function(n,a){return e!=null&&(a=n.value-e),i(gf(r,n),a,n.level!=null?{level:n.level}:null)}}(t):function(i){return r.scale.getLabel(i)}}function gf(r,t){return r.type==="category"?r.scale.getLabel(t):t.value}function wC(r){var t=r.model,e=r.scale;if(!(!t.get(["axisLabel","show"])||e.isBlank())){var i,n,a=e.getExtent();e instanceof uy?n=e.count():(i=e.getTicks(),n=i.length);var o=r.getLabelModel(),s=ki(r),l,u=1;n>40&&(u=Math.ceil(n/40));for(var f=0;f<n;f+=u){var h=i?i[f]:{value:a[0]+f},c=s(h,f),v=o.getTextRect(c),d=bC(v,o.get("rotate")||0);l?l.union(d):l=d}return l}}function bC(r,t){var e=t*Math.PI/180,i=r.width,n=r.height,a=i*Math.abs(Math.cos(e))+Math.abs(n*Math.sin(e)),o=i*Math.abs(Math.sin(e))+Math.abs(n*Math.cos(e)),s=new et(r.x,r.y,a,o);return s}function yf(r){var t=r.get("interval");return t??"auto"}function _y(r){return r.type==="category"&&yf(r.getLabelModel())===0}function xC(r,t){var e={};return A(r.mapDimensionsAll(t),function(i){e[FT(r,i)]=!0}),ht(e)}var TC=function(){function r(){}return r.prototype.getNeedCrossZero=function(){var t=this.option;return!t.scale},r.prototype.getCoordSysModel=function(){},r}(),uc=[],CC={registerPreprocessor:Jg,registerProcessor:jg,registerPostInit:lT,registerPostUpdate:uT,registerUpdateLifecycle:hf,registerAction:Ei,registerCoordinateSystem:fT,registerLayout:hT,registerVisual:Zr,registerTransform:cT,registerLoading:ty,registerMap:vT,registerImpl:Vx,PRIORITY:eT,ComponentModel:ut,ComponentView:Be,SeriesModel:kn,ChartView:rr,registerComponentModel:function(r){ut.registerClass(r)},registerComponentView:function(r){Be.registerClass(r)},registerSeriesModel:function(r){kn.registerClass(r)},registerChartView:function(r){rr.registerClass(r)},registerSubTypeDefaulter:function(r,t){ut.registerSubTypeDefaulter(r,t)},registerPainter:function(r,t){r_(r,t)}};function $r(r){if(N(r)){A(r,function(t){$r(t)});return}at(uc,r)>=0||(uc.push(r),$(r)&&(r={install:r}),r.install(CC))}var Nn=yt();function Sy(r,t){var e=V(t,function(i){return r.scale.parse(i)});return r.type==="time"&&e.length>0&&(e.sort(),e.unshift(e[0]),e.push(e[e.length-1])),e}function DC(r){var t=r.getLabelModel().get("customValues");if(t){var e=ki(r),i=r.scale.getExtent(),n=Sy(r,t),a=wt(n,function(o){return o>=i[0]&&o<=i[1]});return{labels:V(a,function(o){var s={value:o};return{formattedLabel:e(s),rawLabel:r.scale.getLabel(s),tickValue:o}})}}return r.type==="category"?AC(r):IC(r)}function MC(r,t){var e=r.getTickModel().get("customValues");if(e){var i=r.scale.getExtent(),n=Sy(r,e);return{ticks:wt(n,function(a){return a>=i[0]&&a<=i[1]})}}return r.type==="category"?LC(r,t):{ticks:V(r.scale.getTicks(),function(a){return a.value})}}function AC(r){var t=r.getLabelModel(),e=wy(r,t);return!t.get("show")||r.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e}function wy(r,t){var e=by(r,"labels"),i=yf(t),n=xy(e,i);if(n)return n;var a,o;return $(i)?a=Dy(r,i):(o=i==="auto"?PC(r):i,a=Cy(r,o)),Ty(e,i,{labels:a,labelCategoryInterval:o})}function LC(r,t){var e=by(r,"ticks"),i=yf(t),n=xy(e,i);if(n)return n;var a,o;if((!t.get("show")||r.scale.isBlank())&&(a=[]),$(i))a=Dy(r,i,!0);else if(i==="auto"){var s=wy(r,r.getLabelModel());o=s.labelCategoryInterval,a=V(s.labels,function(l){return l.tickValue})}else o=i,a=Cy(r,o,!0);return Ty(e,i,{ticks:a,tickCategoryInterval:o})}function IC(r){var t=r.scale.getTicks(),e=ki(r);return{labels:V(t,function(i,n){return{level:i.level,formattedLabel:e(i,n),rawLabel:r.scale.getLabel(i),tickValue:i.value}})}}function by(r,t){return Nn(r)[t]||(Nn(r)[t]=[])}function xy(r,t){for(var e=0;e<r.length;e++)if(r[e].key===t)return r[e].value}function Ty(r,t,e){return r.push({key:t,value:e}),e}function PC(r){var t=Nn(r).autoInterval;return t??(Nn(r).autoInterval=r.calculateCategoryInterval())}function RC(r){var t=EC(r),e=ki(r),i=(t.axisRotate-t.labelRotate)/180*Math.PI,n=r.scale,a=n.getExtent(),o=n.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=a[0],u=r.dataToCoord(l+1)-r.dataToCoord(l),f=Math.abs(u*Math.cos(i)),h=Math.abs(u*Math.sin(i)),c=0,v=0;l<=a[1];l+=s){var d=0,y=0,p=bu(e({value:l}),t.font,"center","top");d=p.width*1.3,y=p.height*1.3,c=Math.max(c,d,7),v=Math.max(v,y,7)}var g=c/f,m=v/h;isNaN(g)&&(g=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(g,m))),S=Nn(r.model),b=r.getExtent(),w=S.lastAutoInterval,x=S.lastTickCount;return w!=null&&x!=null&&Math.abs(w-_)<=1&&Math.abs(x-o)<=1&&w>_&&S.axisExtent0===b[0]&&S.axisExtent1===b[1]?_=w:(S.lastTickCount=o,S.lastAutoInterval=_,S.axisExtent0=b[0],S.axisExtent1=b[1]),_}function EC(r){var t=r.getLabelModel();return{axisRotate:r.getRotate?r.getRotate():r.isHorizontal&&!r.isHorizontal()?90:0,labelRotate:t.get("rotate")||0,font:t.getFont()}}function Cy(r,t,e){var i=ki(r),n=r.scale,a=n.getExtent(),o=r.getLabelModel(),s=[],l=Math.max((t||0)+1,1),u=a[0],f=n.count();u!==0&&l>1&&f/l>2&&(u=Math.round(Math.ceil(u/l)*l));var h=_y(r),c=o.get("showMinLabel")||h,v=o.get("showMaxLabel")||h;c&&u!==a[0]&&y(a[0]);for(var d=u;d<=a[1];d+=l)y(d);v&&d-l!==a[1]&&y(a[1]);function y(p){var g={value:p};s.push(e?p:{formattedLabel:i(g),rawLabel:n.getLabel(g),tickValue:p})}return s}function Dy(r,t,e){var i=r.scale,n=ki(r),a=[];return A(i.getTicks(),function(o){var s=i.getLabel(o),l=o.value;t(o.value,s)&&a.push(e?l:{formattedLabel:n(o),rawLabel:s,tickValue:l})}),a}var fc=[0,1],kC=function(){function r(t,e,i){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=i||[0,0]}return r.prototype.contain=function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&t<=n},r.prototype.containData=function(t){return this.scale.contain(t)},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.getPixelPrecision=function(t){return a_(t||this.scale.getExtent(),this._extent)},r.prototype.setExtent=function(t,e){var i=this._extent;i[0]=t,i[1]=e},r.prototype.dataToCoord=function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&n.type==="ordinal"&&(i=i.slice(),hc(i,n.count())),rh(t,fc,i,e)},r.prototype.coordToData=function(t,e){var i=this._extent,n=this.scale;this.onBand&&n.type==="ordinal"&&(i=i.slice(),hc(i,n.count()));var a=rh(t,i,fc,e);return this.scale.scale(a)},r.prototype.pointToData=function(t,e){},r.prototype.getTicksCoords=function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=MC(this,e),n=i.ticks,a=V(n,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=e.get("alignWithLabel");return OC(this,a,o,t.clamp),a},r.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&e<100||(e=5);var i=this.scale.getMinorTicks(e),n=V(i,function(a){return V(a,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return n},r.prototype.getViewLabels=function(){return DC(this).labels},r.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},r.prototype.getTickModel=function(){return this.model.getModel("axisTick")},r.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);i===0&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},r.prototype.calculateCategoryInterval=function(){return RC(this)},r}();function hc(r,t){var e=r[1]-r[0],i=t,n=e/i/2;r[0]+=n,r[1]-=n}function OC(r,t,e,i){var n=t.length;if(!r.onBand||e||!n)return;var a=r.getExtent(),o,s;if(n===1)t[0].coord=a[0],o=t[1]={coord:a[1],tickValue:t[0].tickValue};else{var l=t[n-1].tickValue-t[0].tickValue,u=(t[n-1].coord-t[0].coord)/l;A(t,function(v){v.coord-=u/2});var f=r.scale.getExtent();s=1+f[1]-t[n-1].tickValue,o={coord:t[n-1].coord+u*s,tickValue:f[1]+1},t.push(o)}var h=a[0]>a[1];c(t[0].coord,a[0])&&(i?t[0].coord=a[0]:t.shift()),i&&c(a[0],t[0].coord)&&t.unshift({coord:a[0]}),c(a[1],o.coord)&&(i?o.coord=a[1]:t.pop()),i&&c(o.coord,a[1])&&t.push({coord:a[1]});function c(v,d){return v=pt(v),d=pt(d),h?v>d:v<d}}const BC=kC;function NC(r){for(var t=[],e=0;e<r.length;e++){var i=r[e];if(!i.defaultAttr.ignore){var n=i.label,a=n.getComputedTransform(),o=n.getBoundingRect(),s=!a||a[1]<1e-5&&a[2]<1e-5,l=n.style.margin||0,u=o.clone();u.applyTransform(a),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var f=s?new no(o,a):null;t.push({label:n,labelLine:i.labelLine,rect:u,localRect:o,obb:f,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:a})}}return t}function FC(r,t,e,i,n,a){var o=r.length;if(o<2)return;r.sort(function(C,T){return C.rect[t]-T.rect[t]});for(var s=0,l,u=!1,f=0,h=0;h<o;h++){var c=r[h],v=c.rect;l=v[t]-s,l<0&&(v[t]-=l,c.label[t]-=l,u=!0);var d=Math.max(-l,0);f+=d,s=v[t]+v[e]}f>0&&a&&b(-f/o,0,o);var y=r[0],p=r[o-1],g,m;_(),g<0&&w(-g,.8),m<0&&w(m,.8),_(),S(g,m,1),S(m,g,-1),_(),g<0&&x(-g),m<0&&x(m);function _(){g=y.rect[t]-i,m=n-p.rect[t]-p.rect[e]}function S(C,T,D){if(C<0){var M=Math.min(T,-C);if(M>0){b(M*D,0,o);var L=M+C;L<0&&w(-L*D,1)}else w(-C*D,1)}}function b(C,T,D){C!==0&&(u=!0);for(var M=T;M<D;M++){var L=r[M],I=L.rect;I[t]+=C,L.label[t]+=C}}function w(C,T){for(var D=[],M=0,L=1;L<o;L++){var I=r[L-1].rect,P=Math.max(r[L].rect[t]-I[t]-I[e],0);D.push(P),M+=P}if(M){var R=Math.min(Math.abs(C)/M,T);if(C>0)for(var L=0;L<o-1;L++){var E=D[L]*R;b(E,0,L+1)}else for(var L=o-1;L>0;L--){var E=D[L-1]*R;b(-E,L,o)}}}function x(C){var T=C<0?-1:1;C=Math.abs(C);for(var D=Math.ceil(C/(o-1)),M=0;M<o-1;M++)if(T>0?b(D,0,M+1):b(-D,o-M-1,o),C-=D,C<=0)return}return u}function FA(r,t,e,i){return FC(r,"y","height",t,e,i)}function zC(r){var t=[];r.sort(function(y,p){return p.priority-y.priority});var e=new et(0,0,0,0);function i(y){if(!y.ignore){var p=y.ensureState("emphasis");p.ignore==null&&(p.ignore=!1)}y.ignore=!0}for(var n=0;n<r.length;n++){var a=r[n],o=a.axisAligned,s=a.localRect,l=a.transform,u=a.label,f=a.labelLine;e.copy(a.rect),e.width-=.1,e.height-=.1,e.x+=.05,e.y+=.05;for(var h=a.obb,c=!1,v=0;v<t.length;v++){var d=t[v];if(e.intersect(d.rect)){if(o&&d.axisAligned){c=!0;break}if(d.obb||(d.obb=new no(d.localRect,d.transform)),h||(h=new no(s,l)),h.intersect(d.obb)){c=!0;break}}}c?(i(u),f&&i(f)):(u.attr("ignore",a.defaultAttr.ignore),f&&f.attr("ignore",a.defaultAttr.labelGuideIgnore),t.push(a))}}function vc(r,t,e){var i=Li.createCanvas(),n=t.getWidth(),a=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",r)),i.width=n*e,i.height=a*e,i}var HC=function(r){O(t,r);function t(e,i,n){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;n=n||ja,typeof e=="string"?o=vc(e,i,n):H(e)&&(o=e,e=o.id),a.id=e,a.dom=o;var s=o.style;return s&&(sd(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=vc("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,l=!1,u=new et(0,0,0,0);function f(m){if(!(!m.isFinite()||m.isZero()))if(o.length===0){var _=new et(0,0,0,0);_.copy(m),o.push(_)}else{for(var S=!1,b=1/0,w=0,x=0;x<o.length;++x){var C=o[x];if(C.intersect(m)){var T=new et(0,0,0,0);T.copy(C),T.union(m),o[x]=T,S=!0;break}else if(l){u.copy(m),u.union(C);var D=m.width*m.height,M=C.width*C.height,L=u.width*u.height,I=L-D-M;I<b&&(b=I,w=x)}}if(l&&(o[w].union(m),S=!0),!S){var _=new et(0,0,0,0);_.copy(m),o.push(_)}l||(l=o.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){var c=e[h];if(c){var v=c.shouldBePainted(n,a,!0,!0),d=c.__isRendered&&(c.__dirty&$t||!v)?c.getPrevPaintRect():null;d&&f(d);var y=v&&(c.__dirty&$t||!c.__isRendered)?c.getPaintRect():null;y&&f(y)}}for(var h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var c=i[h],v=c&&c.shouldBePainted(n,a,!0,!0);if(c&&(!v||!c.__zr)&&c.__isRendered){var d=c.getPrevPaintRect();d&&f(d)}}var p;do{p=!1;for(var h=0;h<o.length;){if(o[h].isZero()){o.splice(h,1);continue}for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(p=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(p);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,i){var n=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=e+"px",o.height=i+"px"),a.width=e*n,a.height=i*n,s&&(s.width=e*n,s.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(e,i,n){var a=this.dom,o=this.ctx,s=a.width,l=a.height;i=i||this.clearColor;var u=this.motionBlur&&!e,f=this.lastFrameAlpha,h=this.dpr,c=this;u&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/h,l/h));var v=this.domBack;function d(y,p,g,m){if(o.clearRect(y,p,g,m),i&&i!=="transparent"){var _=void 0;if(yo(i)){var S=i.global||i.__width===g&&i.__height===m;_=S&&i.__canvasGradient||Yl(o,i,{x:0,y:0,width:g,height:m}),i.__canvasGradient=_,i.__width=g,i.__height=m}else Cm(i)&&(i.scaleX=i.scaleX||h,i.scaleY=i.scaleY||h,_=Xl(o,i,{dirty:function(){c.setUnpainted(),c.painter.refresh()}}));o.save(),o.fillStyle=_||i,o.fillRect(y,p,g,m),o.restore()}u&&(o.save(),o.globalAlpha=f,o.drawImage(v,y,p,g,m),o.restore())}!n||u?d(0,0,s,l):n.length&&A(n,function(y){d(y.x*h,y.y*h,y.width*h,y.height*h)})},t}(Te);const Ks=HC;var cc=1e5,Lr=314159,Ma=.01,GC=.001;function VC(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function WC(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var UC=function(){function r(t,e,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=k({},i||{}),this.dpr=i.devicePixelRatio||ja,this._singleCanvas=a,this.root=t;var o=t.style;o&&(sd(t),t.innerHTML=""),this.storage=e;var s=this._zlevelList;this._prevDisplayList=[];var l=this._layers;if(a){var f=t,h=f.width,c=f.height;i.width!=null&&(h=i.width),i.height!=null&&(c=i.height),this.dpr=i.devicePixelRatio||1,f.width=h*this.dpr,f.height=c*this.dpr,this._width=h,this._height=c;var v=new Ks(f,this,this.dpr);v.__builtin__=!0,v.initContext(),l[Lr]=v,v.zlevel=Lr,s.push(Lr),this._domRoot=t}else{this._width=_a(t,0,i),this._height=_a(t,1,i);var u=this._domRoot=WC(this._width,this._height);t.appendChild(u)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var a=0;a<n.length;a++){var o=n[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var l=a===0?this._backgroundColor:null;s.refresh(l)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,i=this._hoverlayer;if(i&&i.clear(),!!e){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<e;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(cc)),a||(a=i.ctx,a.save()),Er(a,s,n,o===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(cc)},r.prototype.paintOne=function(t,e){Og(t,e)},r.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,i),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(u){u.afterBrush&&u.afterBrush()});else{var l=this;dl(function(){l._paintList(t,e,i,n)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(Lr).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},r.prototype._doPaintList=function(t,e,i){for(var n=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||i)&&a.push(u)}for(var f=!0,h=!1,c=function(y){var p=a[y],g=p.ctx,m=o&&p.createRepaintRects(t,e,v._width,v._height),_=i?p.__startIndex:p.__drawIndex,S=!i&&p.incremental&&Date.now,b=S&&Date.now(),w=p.zlevel===v._zlevelList[0]?v._backgroundColor:null;if(p.__startIndex===p.__endIndex)p.clear(!1,w,m);else if(_===p.__startIndex){var x=t[_];(!x.incremental||!x.notClear||i)&&p.clear(!1,w,m)}_===-1&&(console.error("For some unknown reason. drawIndex is -1"),_=p.__startIndex);var C,T=function(I){var P={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(C=_;C<p.__endIndex;C++){var R=t[C];if(R.__inHover&&(h=!0),n._doPaintEl(R,p,o,I,P,C===p.__endIndex-1),S){var E=Date.now()-b;if(E>15)break}}P.prevElClipPaths&&g.restore()};if(m)if(m.length===0)C=p.__endIndex;else for(var D=v.dpr,M=0;M<m.length;++M){var L=m[M];g.save(),g.beginPath(),g.rect(L.x*D,L.y*D,L.width*D,L.height*D),g.clip(),T(L),g.restore()}else g.save(),T(),g.restore();p.__drawIndex=C,p.__drawIndex<p.__endIndex&&(f=!1)},v=this,d=0;d<a.length;d++)c(d);return Y.wxa&&A(this._layers,function(y){y&&y.ctx&&y.ctx.draw&&y.ctx.draw()}),{finished:f,needsRefreshHover:h}},r.prototype._doPaintEl=function(t,e,i,n,a,o){var s=e.ctx;if(i){var l=t.getPaintRect();(!n||l&&l.intersect(n))&&(Er(s,t,a,o),t.setPrevPaintRect(l))}else Er(s,t,a,o)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Lr);var i=this._layers[t];return i||(i=new Ks("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?tt(i,this._layerConfig[t],!0):this._layerConfig[t-Ma]&&tt(i,this._layerConfig[t-Ma],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},r.prototype.insertLayer=function(t,e){var i=this._layers,n=this._zlevelList,a=n.length,o=this._domRoot,s=null,l=-1;if(!i[t]&&VC(e)){if(a>0&&t>n[0]){for(l=0;l<a-1&&!(n[l]<t&&n[l+1]>t);l++);s=i[n[l]]}if(n.splice(l+1,0,t),i[t]=e,!e.virtual)if(s){var u=s.dom;u.nextSibling?o.insertBefore(e.dom,u.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__&&t.call(e,o,a)}},r.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__||t.call(e,o,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(h,c){h.__dirty=h.__used=!1});function e(h){a&&(a.__endIndex!==h&&(a.__dirty=!0),a.__endIndex=h)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,l;for(l=0;l<t.length;l++){var n=t[l],u=n.zlevel,f=void 0;s!==u&&(s=u,o=0),n.incremental?(f=this.getLayer(u+GC,this._needsManuallyCompositing),f.incremental=!0,o=1):f=this.getLayer(u+(o>0?Ma:0),this._needsManuallyCompositing),f.__builtin__||du("ZLevel "+u+" has been used by unkown layer "+f.id),f!==a&&(f.__used=!0,f.__startIndex!==l&&(f.__dirty=!0),f.__startIndex=l,f.incremental?f.__drawIndex=-1:f.__drawIndex=l,e(l),a=f),n.__dirty&$t&&!n.__inHover&&(f.__dirty=!0,f.incremental&&f.__drawIndex<0&&(f.__drawIndex=l))}e(l),this.eachBuiltinLayer(function(h,c){!h.__used&&h.getElementCount()>0&&(h.__dirty=!0,h.__startIndex=h.__endIndex=h.__drawIndex=0),h.__dirty&&h.__drawIndex<0&&(h.__drawIndex=h.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,A(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?tt(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+Ma){var o=this._layers[a];tt(o,i[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(at(i,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),e!=null&&(n.height=e),t=_a(a,0,n),e=_a(a,1,n),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(Lr).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Lr].dom;var e=new Ks("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,a=e.dom.height;this.eachLayer(function(h){h.__builtin__?i.drawImage(h.dom,0,0,n,a):h.renderToCanvas&&(i.save(),h.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),l=0,u=s.length;l<u;l++){var f=s[l];Er(i,f,o,l===u-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();const $C=UC;function zA(r){r.registerPainter("canvas",$C)}var YC=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.hasSymbolVisual=!0,e}return t.prototype.getInitialData=function(e){return GT(null,this,{useEncodeDefaulter:!0})},t.prototype.getLegendIcon=function(e){var i=new Et,n=Mi("line",0,e.itemHeight/2,e.itemWidth,0,e.lineStyle.stroke,!1);i.add(n),n.setStyle(e.lineStyle);var a=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=a==="none"?"circle":a,l=e.itemHeight*.8,u=Mi(s,(e.itemWidth-l)/2,(e.itemHeight-l)/2,l,l,e.itemStyle.fill);i.add(u),u.setStyle(e.itemStyle);var f=e.iconRotate==="inherit"?o:e.iconRotate||0;return u.rotation=f*Math.PI/180,u.setOrigin([e.itemWidth/2,e.itemHeight/2]),s.indexOf("empty")>-1&&(u.style.stroke=u.style.fill,u.style.fill="#fff",u.style.lineWidth=2),i},t.type="series.line",t.dependencies=["grid","polar"],t.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},t}(kn);const XC=YC;function My(r,t){var e=r.mapDimensionsAll("defaultedLabel"),i=e.length;if(i===1){var n=Di(r,t,e[0]);return n!=null?n+"":null}else if(i){for(var a=[],o=0;o<e.length;o++)a.push(Di(r,t,e[o]));return a.join(" ")}}function ZC(r,t){var e=r.mapDimensionsAll("defaultedLabel");if(!N(t))return t+"";for(var i=[],n=0;n<e.length;n++){var a=r.getDimensionIndex(e[n]);a>=0&&i.push(t[a])}return i.join(" ")}var qC=function(r){O(t,r);function t(e,i,n,a){var o=r.call(this)||this;return o.updateData(e,i,n,a),o}return t.prototype._createSymbol=function(e,i,n,a,o){this.removeAll();var s=Mi(e,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:a[0]/2,scaleY:a[1]/2}),s.drift=KC,this._symbolType=e,this.add(s)},t.prototype.stopSymbolAnimation=function(e){this.childAt(0).stopAnimation(null,e)},t.prototype.getSymbolType=function(){return this._symbolType},t.prototype.getSymbolPath=function(){return this.childAt(0)},t.prototype.highlight=function(){ro(this.childAt(0))},t.prototype.downplay=function(){io(this.childAt(0))},t.prototype.setZ=function(e,i){var n=this.childAt(0);n.zlevel=e,n.z=i},t.prototype.setDraggable=function(e,i){var n=this.childAt(0);n.draggable=e,n.cursor=!i&&e?"move":n.cursor},t.prototype.updateData=function(e,i,n,a){this.silent=!1;var o=e.getItemVisual(i,"symbol")||"circle",s=e.hostModel,l=t.getSymbolSize(e,i),u=o!==this._symbolType,f=a&&a.disableAnimation;if(u){var h=e.getItemVisual(i,"symbolKeepAspect");this._createSymbol(o,e,i,l,h)}else{var c=this.childAt(0);c.silent=!1;var v={scaleX:l[0]/2,scaleY:l[1]/2};f?c.attr(v):nr(c,v,s,i),LS(c)}if(this._updateCommon(e,i,l,n,a),u){var c=this.childAt(0);if(!f){var v={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:c.style.opacity}};c.scaleX=c.scaleY=0,c.style.opacity=0,Un(c,v,s,i)}}f&&this.childAt(0).stopAnimation("leave")},t.prototype._updateCommon=function(e,i,n,a,o){var s=this.childAt(0),l=e.hostModel,u,f,h,c,v,d,y,p,g;if(a&&(u=a.emphasisItemStyle,f=a.blurItemStyle,h=a.selectItemStyle,c=a.focus,v=a.blurScope,y=a.labelStatesModels,p=a.hoverScale,g=a.cursorStyle,d=a.emphasisDisabled),!a||e.hasItemOption){var m=a&&a.itemModel?a.itemModel:e.getItemModel(i),_=m.getModel("emphasis");u=_.getModel("itemStyle").getItemStyle(),h=m.getModel(["select","itemStyle"]).getItemStyle(),f=m.getModel(["blur","itemStyle"]).getItemStyle(),c=_.get("focus"),v=_.get("blurScope"),d=_.get("disabled"),y=Vu(m),p=_.getShallow("scale"),g=m.getShallow("cursor")}var S=e.getItemVisual(i,"symbolRotate");s.attr("rotation",(S||0)*Math.PI/180||0);var b=Pg(e.getItemVisual(i,"symbolOffset"),n);b&&(s.x=b[0],s.y=b[1]),g&&s.attr("cursor",g);var w=e.getItemVisual(i,"style"),x=w.fill;if(s instanceof Yr){var C=s.style;s.useStyle(k({image:C.image,x:C.x,y:C.y,width:C.width,height:C.height},w))}else s.__isEmptyBrush?s.useStyle(k({},w)):s.useStyle(w),s.style.decal=null,s.setColor(x,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var T=e.getItemVisual(i,"liftZ"),D=this._z2;T!=null?D==null&&(this._z2=s.z2,s.z2+=T):D!=null&&(s.z2=D,this._z2=null);var M=o&&o.useNameLabel;Gu(s,y,{labelFetcher:l,labelDataIndex:i,defaultText:L,inheritColor:x,defaultOpacity:w.opacity});function L(R){return M?e.getName(R):My(e,R)}this._sizeX=n[0]/2,this._sizeY=n[1]/2;var I=s.ensureState("emphasis");I.style=u,s.ensureState("select").style=h,s.ensureState("blur").style=f;var P=p==null||p===!0?Math.max(1.1,3/this._sizeY):isFinite(p)&&p>0?+p:1;I.scaleX=this._sizeX*P,I.scaleY=this._sizeY*P,this.setSymbolScale(1),Rl(this,c,v,d)},t.prototype.setSymbolScale=function(e){this.scaleX=this.scaleY=e},t.prototype.fadeOut=function(e,i,n){var a=this.childAt(0),o=rt(this).dataIndex,s=n&&n.animation;if(this.silent=a.silent=!0,n&&n.fadeLabel){var l=a.getTextContent();l&&ao(l,{style:{opacity:0}},i,{dataIndex:o,removeOpt:s,cb:function(){a.removeTextContent()}})}else a.removeTextContent();ao(a,{style:{opacity:0},scaleX:0,scaleY:0},i,{dataIndex:o,cb:e,removeOpt:s})},t.getSymbolSize=function(e,i){return Tx(e.getItemVisual(i,"symbolSize"))},t}(Et);function KC(r,t){this.parent.drift(r,t)}const mf=qC;function Qs(r,t,e,i){return t&&!isNaN(t[0])&&!isNaN(t[1])&&!(i.isIgnore&&i.isIgnore(e))&&!(i.clipShape&&!i.clipShape.contain(t[0],t[1]))&&r.getItemVisual(e,"symbol")!=="none"}function dc(r){return r!=null&&!H(r)&&(r={isIgnore:r}),r||{}}function pc(r){var t=r.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:Vu(t),cursorStyle:t.get("cursor")}}var QC=function(){function r(t){this.group=new Et,this._SymbolCtor=t||mf}return r.prototype.updateData=function(t,e){this._progressiveEls=null,e=dc(e);var i=this.group,n=t.hostModel,a=this._data,o=this._SymbolCtor,s=e.disableAnimation,l=pc(t),u={disableAnimation:s},f=e.getSymbolPoint||function(h){return t.getItemLayout(h)};a||i.removeAll(),t.diff(a).add(function(h){var c=f(h);if(Qs(t,c,h,e)){var v=new o(t,h,l,u);v.setPosition(c),t.setItemGraphicEl(h,v),i.add(v)}}).update(function(h,c){var v=a.getItemGraphicEl(c),d=f(h);if(!Qs(t,d,h,e)){i.remove(v);return}var y=t.getItemVisual(h,"symbol")||"circle",p=v&&v.getSymbolType&&v.getSymbolType();if(!v||p&&p!==y)i.remove(v),v=new o(t,h,l,u),v.setPosition(d);else{v.updateData(t,h,l,u);var g={x:d[0],y:d[1]};s?v.attr(g):nr(v,g,n)}i.add(v),t.setItemGraphicEl(h,v)}).remove(function(h){var c=a.getItemGraphicEl(h);c&&c.fadeOut(function(){i.remove(c)},n)}).execute(),this._getSymbolPoint=f,this._data=t},r.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl(function(i,n){var a=t._getSymbolPoint(n);i.setPosition(a),i.markRedraw()})},r.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=pc(t),this._data=null,this.group.removeAll()},r.prototype.incrementalUpdate=function(t,e,i){this._progressiveEls=[],i=dc(i);function n(l){l.isGroup||(l.incremental=!0,l.ensureState("emphasis").hoverLayer=!0)}for(var a=t.start;a<t.end;a++){var o=e.getItemLayout(a);if(Qs(e,o,a,i)){var s=new this._SymbolCtor(e,a,this._seriesScope);s.traverse(n),s.setPosition(o),this.group.add(s),e.setItemGraphicEl(a,s),this._progressiveEls.push(s)}}},r.prototype.eachRendered=function(t){Hu(this._progressiveEls||this.group,t)},r.prototype.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(n){n.fadeOut(function(){e.remove(n)},i.hostModel)}):e.removeAll()},r}();const JC=QC;function Ay(r,t,e){var i=r.getBaseAxis(),n=r.getOtherAxis(i),a=jC(n,e),o=i.dim,s=n.dim,l=t.mapDimension(s),u=t.mapDimension(o),f=s==="x"||s==="radius"?1:0,h=V(r.dimensions,function(d){return t.mapDimension(d)}),c=!1,v=t.getCalculationInfo("stackResultDimension");return Ai(t,h[0])&&(c=!0,h[0]=v),Ai(t,h[1])&&(c=!0,h[1]=v),{dataDimsForPoint:h,valueStart:a,valueAxisDim:s,baseAxisDim:o,stacked:!!c,valueDim:l,baseDim:u,baseDataOffset:f,stackedOverDimension:t.getCalculationInfo("stackedOverDimension")}}function jC(r,t){var e=0,i=r.scale.getExtent();return t==="start"?e=i[0]:t==="end"?e=i[1]:vt(t)&&!isNaN(t)?e=t:i[0]>0?e=i[0]:i[1]<0&&(e=i[1]),e}function Ly(r,t,e,i){var n=NaN;r.stacked&&(n=e.get(e.getCalculationInfo("stackedOverDimension"),i)),isNaN(n)&&(n=r.valueStart);var a=r.baseDataOffset,o=[];return o[a]=e.get(r.baseDim,i),o[1-a]=n,t.dataToPoint(o)}function tD(r,t){var e=[];return t.diff(r).add(function(i){e.push({cmd:"+",idx:i})}).update(function(i,n){e.push({cmd:"=",idx:n,idx1:i})}).remove(function(i){e.push({cmd:"-",idx:i})}).execute(),e}function eD(r,t,e,i,n,a,o,s){for(var l=tD(r,t),u=[],f=[],h=[],c=[],v=[],d=[],y=[],p=Ay(n,t,o),g=r.getLayout("points")||[],m=t.getLayout("points")||[],_=0;_<l.length;_++){var S=l[_],b=!0,w=void 0,x=void 0;switch(S.cmd){case"=":w=S.idx*2,x=S.idx1*2;var C=g[w],T=g[w+1],D=m[x],M=m[x+1];(isNaN(C)||isNaN(T))&&(C=D,T=M),u.push(C,T),f.push(D,M),h.push(e[w],e[w+1]),c.push(i[x],i[x+1]),y.push(t.getRawIndex(S.idx1));break;case"+":var L=S.idx,I=p.dataDimsForPoint,P=n.dataToPoint([t.get(I[0],L),t.get(I[1],L)]);x=L*2,u.push(P[0],P[1]),f.push(m[x],m[x+1]);var R=Ly(p,n,t,L);h.push(R[0],R[1]),c.push(i[x],i[x+1]),y.push(t.getRawIndex(L));break;case"-":b=!1}b&&(v.push(S),d.push(d.length))}d.sort(function(ct,ae){return y[ct]-y[ae]});for(var E=u.length,G=Ie(E),B=Ie(E),F=Ie(E),W=Ie(E),q=[],_=0;_<d.length;_++){var K=d[_],nt=_*2,lt=K*2;G[nt]=u[lt],G[nt+1]=u[lt+1],B[nt]=f[lt],B[nt+1]=f[lt+1],F[nt]=h[lt],F[nt+1]=h[lt+1],W[nt]=c[lt],W[nt+1]=c[lt+1],q[_]=v[K]}return{current:G,next:B,stackedOnCurrent:F,stackedOnNext:W,status:q}}var $e=Math.min,Ye=Math.max;function Br(r,t){return isNaN(r)||isNaN(t)}function tu(r,t,e,i,n,a,o,s,l){for(var u,f,h,c,v,d,y=e,p=0;p<i;p++){var g=t[y*2],m=t[y*2+1];if(y>=n||y<0)break;if(Br(g,m)){if(l){y+=a;continue}break}if(y===e)r[a>0?"moveTo":"lineTo"](g,m),h=g,c=m;else{var _=g-u,S=m-f;if(_*_+S*S<.5){y+=a;continue}if(o>0){for(var b=y+a,w=t[b*2],x=t[b*2+1];w===g&&x===m&&p<i;)p++,b+=a,y+=a,w=t[b*2],x=t[b*2+1],g=t[y*2],m=t[y*2+1],_=g-u,S=m-f;var C=p+1;if(l)for(;Br(w,x)&&C<i;)C++,b+=a,w=t[b*2],x=t[b*2+1];var T=.5,D=0,M=0,L=void 0,I=void 0;if(C>=i||Br(w,x))v=g,d=m;else{D=w-u,M=x-f;var P=g-u,R=w-g,E=m-f,G=x-m,B=void 0,F=void 0;if(s==="x"){B=Math.abs(P),F=Math.abs(R);var W=D>0?1:-1;v=g-W*B*o,d=m,L=g+W*F*o,I=m}else if(s==="y"){B=Math.abs(E),F=Math.abs(G);var q=M>0?1:-1;v=g,d=m-q*B*o,L=g,I=m+q*F*o}else B=Math.sqrt(P*P+E*E),F=Math.sqrt(R*R+G*G),T=F/(F+B),v=g-D*o*(1-T),d=m-M*o*(1-T),L=g+D*o*T,I=m+M*o*T,L=$e(L,Ye(w,g)),I=$e(I,Ye(x,m)),L=Ye(L,$e(w,g)),I=Ye(I,$e(x,m)),D=L-g,M=I-m,v=g-D*B/F,d=m-M*B/F,v=$e(v,Ye(u,g)),d=$e(d,Ye(f,m)),v=Ye(v,$e(u,g)),d=Ye(d,$e(f,m)),D=g-v,M=m-d,L=g+D*F/B,I=m+M*F/B}r.bezierCurveTo(h,c,v,d,g,m),h=L,c=I}else r.lineTo(g,m)}u=g,f=m,y+=a}return p}var Iy=function(){function r(){this.smooth=0,this.smoothConstraint=!0}return r}(),rD=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polyline",i}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Iy},t.prototype.buildPath=function(e,i){var n=i.points,a=0,o=n.length/2;if(i.connectNulls){for(;o>0&&Br(n[o*2-2],n[o*2-1]);o--);for(;a<o&&Br(n[a*2],n[a*2+1]);a++);}for(;a<o;)a+=tu(e,n,a,o,o,1,i.smooth,i.smoothMonotone,i.connectNulls)+1},t.prototype.getPointOn=function(e,i){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n=this.path,a=n.data,o=Vr.CMD,s,l,u=i==="x",f=[],h=0;h<a.length;){var c=a[h++],v=void 0,d=void 0,y=void 0,p=void 0,g=void 0,m=void 0,_=void 0;switch(c){case o.M:s=a[h++],l=a[h++];break;case o.L:if(v=a[h++],d=a[h++],_=u?(e-s)/(v-s):(e-l)/(d-l),_<=1&&_>=0){var S=u?(d-l)*_+l:(v-s)*_+s;return u?[e,S]:[S,e]}s=v,l=d;break;case o.C:v=a[h++],d=a[h++],y=a[h++],p=a[h++],g=a[h++],m=a[h++];var b=u?qa(s,v,y,g,e,f):qa(l,d,p,m,e,f);if(b>0)for(var w=0;w<b;w++){var x=f[w];if(x<=1&&x>=0){var S=u?gt(l,d,p,m,x):gt(s,v,y,g,x);return u?[e,S]:[S,e]}}s=g,l=m;break}}},t}(st),iD=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Iy),nD=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polygon",i}return t.prototype.getDefaultShape=function(){return new iD},t.prototype.buildPath=function(e,i){var n=i.points,a=i.stackedOnPoints,o=0,s=n.length/2,l=i.smoothMonotone;if(i.connectNulls){for(;s>0&&Br(n[s*2-2],n[s*2-1]);s--);for(;o<s&&Br(n[o*2],n[o*2+1]);o++);}for(;o<s;){var u=tu(e,n,o,s,s,1,i.smooth,l,i.connectNulls);tu(e,a,o+u-1,u,s,-1,i.stackedOnSmooth,l,i.connectNulls),o+=u+1,e.closePath()}},t}(st);function Py(r,t,e,i,n){var a=r.getArea(),o=a.x,s=a.y,l=a.width,u=a.height,f=e.get(["lineStyle","width"])||0;o-=f/2,s-=f/2,l+=f,u+=f,l=Math.ceil(l),o!==Math.floor(o)&&(o=Math.floor(o),l++);var h=new bt({shape:{x:o,y:s,width:l,height:u}});if(t){var c=r.getBaseAxis(),v=c.isHorizontal(),d=c.inverse;v?(d&&(h.shape.x+=l),h.shape.width=0):(d||(h.shape.y+=u),h.shape.height=0);var y=$(n)?function(p){n(p,h)}:null;Un(h,{shape:{width:l,height:u,x:o,y:s}},e,null,i,y)}return h}function Ry(r,t,e){var i=r.getArea(),n=pt(i.r0,1),a=pt(i.r,1),o=new ku({shape:{cx:pt(r.cx,1),cy:pt(r.cy,1),r0:n,r:a,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});if(t){var s=r.getBaseAxis().dim==="angle";s?o.shape.endAngle=i.startAngle:o.shape.r=n,Un(o,{shape:{endAngle:i.endAngle,r:a}},e)}return o}function HA(r,t,e,i,n){if(r){if(r.type==="polar")return Ry(r,t,e);if(r.type==="cartesian2d")return Py(r,t,e,i,n)}else return null;return null}function aD(r,t){return r.type===t}function gc(r,t){if(r.length===t.length){for(var e=0;e<r.length;e++)if(r[e]!==t[e])return;return!0}}function yc(r){for(var t=1/0,e=1/0,i=-1/0,n=-1/0,a=0;a<r.length;){var o=r[a++],s=r[a++];isNaN(o)||(t=Math.min(o,t),i=Math.max(o,i)),isNaN(s)||(e=Math.min(s,e),n=Math.max(s,n))}return[[t,e],[i,n]]}function mc(r,t){var e=yc(r),i=e[0],n=e[1],a=yc(t),o=a[0],s=a[1];return Math.max(Math.abs(i[0]-o[0]),Math.abs(i[1]-o[1]),Math.abs(n[0]-s[0]),Math.abs(n[1]-s[1]))}function _c(r){return vt(r)?r:r?.5:0}function oD(r,t,e){if(!e.valueDim)return[];for(var i=t.count(),n=Ie(i*2),a=0;a<i;a++){var o=Ly(e,r,t,a);n[a*2]=o[0],n[a*2+1]=o[1]}return n}function Xe(r,t,e,i,n){var a=e.getBaseAxis(),o=a.dim==="x"||a.dim==="radius"?0:1,s=[],l=0,u=[],f=[],h=[],c=[];if(n){for(l=0;l<r.length;l+=2){var v=t||r;!isNaN(v[l])&&!isNaN(v[l+1])&&c.push(r[l],r[l+1])}r=c}for(l=0;l<r.length-2;l+=2)switch(h[0]=r[l+2],h[1]=r[l+3],f[0]=r[l],f[1]=r[l+1],s.push(f[0],f[1]),i){case"end":u[o]=h[o],u[1-o]=f[1-o],s.push(u[0],u[1]);break;case"middle":var d=(f[o]+h[o])/2,y=[];u[o]=y[o]=d,u[1-o]=f[1-o],y[1-o]=h[1-o],s.push(u[0],u[1]),s.push(y[0],y[1]);break;default:u[o]=f[o],u[1-o]=h[1-o],s.push(u[0],u[1])}return s.push(r[l++],r[l++]),s}function sD(r,t){var e=[],i=r.length,n,a;function o(f,h,c){var v=f.coord,d=(c-v)/(h.coord-v),y=C0(d,[f.color,h.color]);return{coord:c,color:y}}for(var s=0;s<i;s++){var l=r[s],u=l.coord;if(u<0)n=l;else if(u>t){a?e.push(o(a,l,t)):n&&e.push(o(n,l,0),o(n,l,t));break}else n&&(e.push(o(n,l,0)),n=null),e.push(l),a=l}return e}function lD(r,t,e){var i=r.getVisual("visualMeta");if(!(!i||!i.length||!r.count())&&t.type==="cartesian2d"){for(var n,a,o=i.length-1;o>=0;o--){var s=r.getDimensionInfo(i[o].dimension);if(n=s&&s.coordDim,n==="x"||n==="y"){a=i[o];break}}if(a){var l=t.getAxis(n),u=V(a.stops,function(_){return{coord:l.toGlobalCoord(l.dataToCoord(_.value)),color:_.color}}),f=u.length,h=a.outerColors.slice();f&&u[0].coord>u[f-1].coord&&(u.reverse(),h.reverse());var c=sD(u,n==="x"?e.getWidth():e.getHeight()),v=c.length;if(!v&&f)return u[0].coord<0?h[1]?h[1]:u[f-1].color:h[0]?h[0]:u[0].color;var d=10,y=c[0].coord-d,p=c[v-1].coord+d,g=p-y;if(g<.001)return"transparent";A(c,function(_){_.offset=(_.coord-y)/g}),c.push({offset:v?c[v-1].offset:.5,color:h[1]||"transparent"}),c.unshift({offset:v?c[0].offset:.5,color:h[0]||"transparent"});var m=new Lp(0,0,0,0,c,!0);return m[n]=y,m[n+"2"]=p,m}}}function uD(r,t,e){var i=r.get("showAllSymbol"),n=i==="auto";if(!(i&&!n)){var a=e.getAxesByScale("ordinal")[0];if(a&&!(n&&fD(a,t))){var o=t.mapDimension(a.dim),s={};return A(a.getViewLabels(),function(l){var u=a.scale.getRawOrdinalNumber(l.tickValue);s[u]=1}),function(l){return!s.hasOwnProperty(t.get(o,l))}}}}function fD(r,t){var e=r.getExtent(),i=Math.abs(e[1]-e[0])/r.scale.count();isNaN(i)&&(i=0);for(var n=t.count(),a=Math.max(1,Math.round(n/5)),o=0;o<n;o+=a)if(mf.getSymbolSize(t,o)[r.isHorizontal()?1:0]*1.5>i)return!1;return!0}function hD(r,t){return isNaN(r)||isNaN(t)}function vD(r){for(var t=r.length/2;t>0&&hD(r[t*2-2],r[t*2-1]);t--);return t-1}function Sc(r,t){return[r[t*2],r[t*2+1]]}function cD(r,t,e){for(var i=r.length/2,n=e==="x"?0:1,a,o,s=0,l=-1,u=0;u<i;u++)if(o=r[u*2+n],!(isNaN(o)||isNaN(r[u*2+1-n]))){if(u===0){a=o;continue}if(a<=t&&o>=t||a>=t&&o<=t){l=u;break}s=u,a=o}return{range:[s,l],t:(t-a)/(o-a)}}function Ey(r){if(r.get(["endLabel","show"]))return!0;for(var t=0;t<be.length;t++)if(r.get([be[t],"endLabel","show"]))return!0;return!1}function Js(r,t,e,i){if(aD(t,"cartesian2d")){var n=i.getModel("endLabel"),a=n.get("valueAnimation"),o=i.getData(),s={lastFrameIndex:0},l=Ey(i)?function(v,d){r._endLabelOnDuring(v,d,o,s,a,n,t)}:null,u=t.getBaseAxis().isHorizontal(),f=Py(t,e,i,function(){var v=r._endLabel;v&&e&&s.originalX!=null&&v.attr({x:s.originalX,y:s.originalY})},l);if(!i.get("clip",!0)){var h=f.shape,c=Math.max(h.width,h.height);u?(h.y-=c,h.height+=c*2):(h.x-=c,h.width+=c*2)}return l&&l(1,f),f}else return Ry(t,e,i)}function dD(r,t){var e=t.getBaseAxis(),i=e.isHorizontal(),n=e.inverse,a=i?n?"right":"left":"center",o=i?"middle":n?"top":"bottom";return{normal:{align:r.get("align")||a,verticalAlign:r.get("verticalAlign")||o}}}var pD=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(){var e=new Et,i=new JC;this.group.add(i.group),this._symbolDraw=i,this._lineGroup=e,this._changePolyState=ft(this._changePolyState,this)},t.prototype.render=function(e,i,n){var a=e.coordinateSystem,o=this.group,s=e.getData(),l=e.getModel("lineStyle"),u=e.getModel("areaStyle"),f=s.getLayout("points")||[],h=a.type==="polar",c=this._coordSys,v=this._symbolDraw,d=this._polyline,y=this._polygon,p=this._lineGroup,g=!i.ssr&&e.get("animation"),m=!u.isEmpty(),_=u.get("origin"),S=Ay(a,s,_),b=m&&oD(a,s,S),w=e.get("showSymbol"),x=e.get("connectNulls"),C=w&&!h&&uD(e,s,a),T=this._data;T&&T.eachItemGraphicEl(function(ct,ae){ct.__temp&&(o.remove(ct),T.setItemGraphicEl(ae,null))}),w||v.remove(),o.add(p);var D=h?!1:e.get("step"),M;a&&a.getArea&&e.get("clip",!0)&&(M=a.getArea(),M.width!=null?(M.x-=.1,M.y-=.1,M.width+=.2,M.height+=.2):M.r0&&(M.r0-=.5,M.r+=.5)),this._clipShapeForSymbol=M;var L=lD(s,a,n)||s.getVisual("style")[s.getVisual("drawType")];if(!(d&&c.type===a.type&&D===this._step))w&&v.updateData(s,{isIgnore:C,clipShape:M,disableAnimation:!0,getSymbolPoint:function(ct){return[f[ct*2],f[ct*2+1]]}}),g&&this._initSymbolLabelAnimation(s,a,M),D&&(b&&(b=Xe(b,f,a,D,x)),f=Xe(f,null,a,D,x)),d=this._newPolyline(f),m?y=this._newPolygon(f,b):y&&(p.remove(y),y=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,Ur(L)),p.setClipPath(Js(this,a,!0,e));else{m&&!y?y=this._newPolygon(f,b):y&&!m&&(p.remove(y),y=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,Ur(L));var I=p.getClipPath();if(I){var P=Js(this,a,!1,e);Un(I,{shape:P.shape},e)}else p.setClipPath(Js(this,a,!0,e));w&&v.updateData(s,{isIgnore:C,clipShape:M,disableAnimation:!0,getSymbolPoint:function(ct){return[f[ct*2],f[ct*2+1]]}}),(!gc(this._stackedOnPoints,b)||!gc(this._points,f))&&(g?this._doUpdateAnimation(s,b,a,n,D,_,x):(D&&(b&&(b=Xe(b,f,a,D,x)),f=Xe(f,null,a,D,x)),d.setShape({points:f}),y&&y.setShape({points:f,stackedOnPoints:b})))}var R=e.getModel("emphasis"),E=R.get("focus"),G=R.get("blurScope"),B=R.get("disabled");if(d.useStyle(it(l.getLineStyle(),{fill:"none",stroke:L,lineJoin:"bevel"})),Rh(d,e,"lineStyle"),d.style.lineWidth>0&&e.get(["emphasis","lineStyle","width"])==="bolder"){var F=d.getState("emphasis").style;F.lineWidth=+d.style.lineWidth+1}rt(d).seriesIndex=e.seriesIndex,Rl(d,E,G,B);var W=_c(e.get("smooth")),q=e.get("smoothMonotone");if(d.setShape({smooth:W,smoothMonotone:q,connectNulls:x}),y){var K=s.getCalculationInfo("stackedOnSeries"),nt=0;y.useStyle(it(u.getAreaStyle(),{fill:L,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),K&&(nt=_c(K.get("smooth"))),y.setShape({smooth:W,stackedOnSmooth:nt,smoothMonotone:q,connectNulls:x}),Rh(y,e,"areaStyle"),rt(y).seriesIndex=e.seriesIndex,Rl(y,E,G,B)}var lt=this._changePolyState;s.eachItemGraphicEl(function(ct){ct&&(ct.onHoverStateChange=lt)}),this._polyline.onHoverStateChange=lt,this._data=s,this._coordSys=a,this._stackedOnPoints=b,this._points=f,this._step=D,this._valueOrigin=_,e.get("triggerLineEvent")&&(this.packEventData(e,d),y&&this.packEventData(e,y))},t.prototype.packEventData=function(e,i){rt(i).eventData={componentType:"series",componentSubType:"line",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"line"}},t.prototype.highlight=function(e,i,n,a){var o=e.getData(),s=Gr(o,a);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var l=o.getLayout("points"),u=o.getItemGraphicEl(s);if(!u){var f=l[s*2],h=l[s*2+1];if(isNaN(f)||isNaN(h)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(f,h))return;var c=e.get("zlevel")||0,v=e.get("z")||0;u=new mf(o,s),u.x=f,u.y=h,u.setZ(c,v);var d=u.getSymbolPath().getTextContent();d&&(d.zlevel=c,d.z=v,d.z2=this._polyline.z2+1),u.__temp=!0,o.setItemGraphicEl(s,u),u.stopSymbolAnimation(!0),this.group.add(u)}u.highlight()}else rr.prototype.highlight.call(this,e,i,n,a)},t.prototype.downplay=function(e,i,n,a){var o=e.getData(),s=Gr(o,a);if(this._changePolyState("normal"),s!=null&&s>=0){var l=o.getItemGraphicEl(s);l&&(l.__temp?(o.setItemGraphicEl(s,null),this.group.remove(l)):l.downplay())}else rr.prototype.downplay.call(this,e,i,n,a)},t.prototype._changePolyState=function(e){var i=this._polygon;Mh(this._polyline,e),i&&Mh(i,e)},t.prototype._newPolyline=function(e){var i=this._polyline;return i&&this._lineGroup.remove(i),i=new rD({shape:{points:e},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(i),this._polyline=i,i},t.prototype._newPolygon=function(e,i){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new nD({shape:{points:e,stackedOnPoints:i},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},t.prototype._initSymbolLabelAnimation=function(e,i,n){var a,o,s=i.getBaseAxis(),l=s.inverse;i.type==="cartesian2d"?(a=s.isHorizontal(),o=!1):i.type==="polar"&&(a=s.dim==="angle",o=!0);var u=e.hostModel,f=u.get("animationDuration");$(f)&&(f=f(null));var h=u.get("animationDelay")||0,c=$(h)?h(null):h;e.eachItemGraphicEl(function(v,d){var y=v;if(y){var p=[v.x,v.y],g=void 0,m=void 0,_=void 0;if(n)if(o){var S=n,b=i.pointToCoord(p);a?(g=S.startAngle,m=S.endAngle,_=-b[1]/180*Math.PI):(g=S.r0,m=S.r,_=b[0])}else{var w=n;a?(g=w.x,m=w.x+w.width,_=v.x):(g=w.y+w.height,m=w.y,_=v.y)}var x=m===g?0:(_-g)/(m-g);l&&(x=1-x);var C=$(h)?h(d):f*x+c,T=y.getSymbolPath(),D=T.getTextContent();y.attr({scaleX:0,scaleY:0}),y.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:C}),D&&D.animateFrom({style:{opacity:0}},{duration:300,delay:C}),T.disableLabelAnimation=!0}})},t.prototype._initOrUpdateEndLabel=function(e,i,n){var a=e.getModel("endLabel");if(Ey(e)){var o=e.getData(),s=this._polyline,l=o.getLayout("points");if(!l){s.removeTextContent(),this._endLabel=null;return}var u=this._endLabel;u||(u=this._endLabel=new kt({z2:200}),u.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var f=vD(l);f>=0&&(Gu(s,Vu(e,"endLabel"),{inheritColor:n,labelFetcher:e,labelDataIndex:f,defaultText:function(h,c,v){return v!=null?ZC(o,v):My(o,h)},enableTextSetter:!0},dD(a,i)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},t.prototype._endLabelOnDuring=function(e,i,n,a,o,s,l){var u=this._endLabel,f=this._polyline;if(u){e<1&&a.originalX==null&&(a.originalX=u.x,a.originalY=u.y);var h=n.getLayout("points"),c=n.hostModel,v=c.get("connectNulls"),d=s.get("precision"),y=s.get("distance")||0,p=l.getBaseAxis(),g=p.isHorizontal(),m=p.inverse,_=i.shape,S=m?g?_.x:_.y+_.height:g?_.x+_.width:_.y,b=(g?y:0)*(m?-1:1),w=(g?0:-y)*(m?-1:1),x=g?"x":"y",C=cD(h,S,x),T=C.range,D=T[1]-T[0],M=void 0;if(D>=1){if(D>1&&!v){var L=Sc(h,T[0]);u.attr({x:L[0]+b,y:L[1]+w}),o&&(M=c.getRawValue(T[0]))}else{var L=f.getPointOn(S,x);L&&u.attr({x:L[0]+b,y:L[1]+w});var I=c.getRawValue(T[0]),P=c.getRawValue(T[1]);o&&(M=T_(n,d,I,P,C.t))}a.lastFrameIndex=T[0]}else{var R=e===1||a.lastFrameIndex>0?T[0]:0,L=Sc(h,R);o&&(M=c.getRawValue(R)),u.attr({x:L[0]+b,y:L[1]+w})}if(o){var E=Wu(u);typeof E.setLabelText=="function"&&E.setLabelText(M)}}},t.prototype._doUpdateAnimation=function(e,i,n,a,o,s,l){var u=this._polyline,f=this._polygon,h=e.hostModel,c=eD(this._data,e,this._stackedOnPoints,i,this._coordSys,n,this._valueOrigin),v=c.current,d=c.stackedOnCurrent,y=c.next,p=c.stackedOnNext;if(o&&(d=Xe(c.stackedOnCurrent,c.current,n,o,l),v=Xe(c.current,null,n,o,l),p=Xe(c.stackedOnNext,c.next,n,o,l),y=Xe(c.next,null,n,o,l)),mc(v,y)>3e3||f&&mc(d,p)>3e3){u.stopAnimation(),u.setShape({points:y}),f&&(f.stopAnimation(),f.setShape({points:y,stackedOnPoints:p}));return}u.shape.__points=c.current,u.shape.points=v;var g={shape:{points:y}};c.current!==v&&(g.shape.__points=c.next),u.stopAnimation(),nr(u,g,h),f&&(f.setShape({points:v,stackedOnPoints:d}),f.stopAnimation(),nr(f,{shape:{stackedOnPoints:p}},h),u.shape.points!==f.shape.points&&(f.shape.points=u.shape.points));for(var m=[],_=c.status,S=0;S<_.length;S++){var b=_[S].cmd;if(b==="="){var w=e.getItemGraphicEl(_[S].idx1);w&&m.push({el:w,ptIdx:S})}}u.animators&&u.animators.length&&u.animators[0].during(function(){f&&f.dirtyShape();for(var x=u.shape.__points,C=0;C<m.length;C++){var T=m[C].el,D=m[C].ptIdx*2;T.x=x[D],T.y=x[D+1],T.markRedraw()}})},t.prototype.remove=function(e){var i=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(a,o){a.__temp&&(i.remove(a),n.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},t.type="line",t}(rr);const gD=pD;function yD(r,t){return{seriesType:r,plan:af(),reset:function(e){var i=e.getData(),n=e.coordinateSystem,a=e.pipelineContext,o=t||a.large;if(n){var s=V(n.dimensions,function(v){return i.mapDimension(v)}).slice(0,2),l=s.length,u=i.getCalculationInfo("stackResultDimension");Ai(i,s[0])&&(s[0]=u),Ai(i,s[1])&&(s[1]=u);var f=i.getStore(),h=i.getDimensionIndex(s[0]),c=i.getDimensionIndex(s[1]);return l&&{progress:function(v,d){for(var y=v.end-v.start,p=o&&Ie(y*l),g=[],m=[],_=v.start,S=0;_<v.end;_++){var b=void 0;if(l===1){var w=f.get(h,_);b=n.dataToPoint(w,null,m)}else g[0]=f.get(h,_),g[1]=f.get(c,_),b=n.dataToPoint(g,null,m);o?(p[S++]=b[0],p[S++]=b[1]):d.setItemLayout(_,b.slice())}o&&d.setLayout("points",p)}}}}}}var mD={average:function(r){for(var t=0,e=0,i=0;i<r.length;i++)isNaN(r[i])||(t+=r[i],e++);return e===0?NaN:t/e},sum:function(r){for(var t=0,e=0;e<r.length;e++)t+=r[e]||0;return t},max:function(r){for(var t=-1/0,e=0;e<r.length;e++)r[e]>t&&(t=r[e]);return isFinite(t)?t:NaN},min:function(r){for(var t=1/0,e=0;e<r.length;e++)r[e]<t&&(t=r[e]);return isFinite(t)?t:NaN},nearest:function(r){return r[0]}},_D=function(r){return Math.round(r.length/2)};function SD(r){return{seriesType:r,reset:function(t,e,i){var n=t.getData(),a=t.get("sampling"),o=t.coordinateSystem,s=n.count();if(s>10&&o.type==="cartesian2d"&&a){var l=o.getBaseAxis(),u=o.getOtherAxis(l),f=l.getExtent(),h=i.getDevicePixelRatio(),c=Math.abs(f[1]-f[0])*(h||1),v=Math.round(s/c);if(isFinite(v)&&v>1){a==="lttb"?t.setData(n.lttbDownSample(n.mapDimension(u.dim),1/v)):a==="minmax"&&t.setData(n.minmaxDownSample(n.mapDimension(u.dim),1/v));var d=void 0;z(a)?d=mD[a]:$(a)&&(d=a),d&&t.setData(n.downSample(n.mapDimension(u.dim),1/v,d,_D))}}}}}function GA(r){r.registerChartView(gD),r.registerSeriesModel(XC),r.registerLayout(yD("line",!0)),r.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),i=t.getModel("lineStyle").getLineStyle();i&&!i.stroke&&(i.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",i)}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,SD("line"))}var wD=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.type="grid",t.dependencies=["xAxis","yAxis"],t.layoutMode="box",t.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},t}(ut);const bD=wD;var eu=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",he).models[0]},t.type="cartesian2dAxis",t}(ut);xe(eu,TC);var ky={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},xD=tt({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},ky),_f=tt({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},ky),TD=tt({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},_f),CD=it({logBase:10},_f);const DD={category:xD,value:_f,time:TD,log:CD};var MD={value:1,category:1,time:1,log:1};function wc(r,t,e,i){A(MD,function(n,a){var o=tt(tt({},DD[a],!0),i,!0),s=function(l){O(u,l);function u(){var f=l!==null&&l.apply(this,arguments)||this;return f.type=t+"Axis."+a,f}return u.prototype.mergeDefaultAndTheme=function(f,h){var c=Pn(this),v=c?Oo(f):{},d=h.getTheme();tt(f,d.get(a+"Axis")),tt(f,this.getDefaultOption()),f.type=bc(f),c&&Ci(f,v,c)},u.prototype.optionUpdated=function(){var f=this.option;f.type==="category"&&(this.__ordinalMeta=Jl.createByAxisModel(this))},u.prototype.getCategories=function(f){var h=this.option;if(h.type==="category")return f?h.data:this.__ordinalMeta.categories},u.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},u.type=t+"Axis."+a,u.defaultOption=o,u}(e);r.registerComponentModel(s)}),r.registerSubTypeDefaulter(t+"Axis",bc)}function bc(r){return r.type||(r.data?"category":"value")}var AD=function(){function r(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return r.prototype.getAxis=function(t){return this._axes[t]},r.prototype.getAxes=function(){return V(this._dimList,function(t){return this._axes[t]},this)},r.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),wt(this.getAxes(),function(e){return e.scale.type===t})},r.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},r}();const LD=AD;var ru=["x","y"];function xc(r){return r.type==="interval"||r.type==="time"}var ID=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=ru,e}return t.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var e=this.getAxis("x").scale,i=this.getAxis("y").scale;if(!(!xc(e)||!xc(i))){var n=e.getExtent(),a=i.getExtent(),o=this.dataToPoint([n[0],a[0]]),s=this.dataToPoint([n[1],a[1]]),l=n[1]-n[0],u=a[1]-a[0];if(!(!l||!u)){var f=(s[0]-o[0])/l,h=(s[1]-o[1])/u,c=o[0]-n[0]*f,v=o[1]-a[0]*h,d=this._transform=[f,0,0,h,c,v];this._invTransform=_u([],d)}}},t.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},t.prototype.containPoint=function(e){var i=this.getAxis("x"),n=this.getAxis("y");return i.contain(i.toLocalCoord(e[0]))&&n.contain(n.toLocalCoord(e[1]))},t.prototype.containData=function(e){return this.getAxis("x").containData(e[0])&&this.getAxis("y").containData(e[1])},t.prototype.containZone=function(e,i){var n=this.dataToPoint(e),a=this.dataToPoint(i),o=this.getArea(),s=new et(n[0],n[1],a[0]-n[0],a[1]-n[1]);return o.intersect(s)},t.prototype.dataToPoint=function(e,i,n){n=n||[];var a=e[0],o=e[1];if(this._transform&&a!=null&&isFinite(a)&&o!=null&&isFinite(o))return ie(n,e,this._transform);var s=this.getAxis("x"),l=this.getAxis("y");return n[0]=s.toGlobalCoord(s.dataToCoord(a,i)),n[1]=l.toGlobalCoord(l.dataToCoord(o,i)),n},t.prototype.clampData=function(e,i){var n=this.getAxis("x").scale,a=this.getAxis("y").scale,o=n.getExtent(),s=a.getExtent(),l=n.parse(e[0]),u=a.parse(e[1]);return i=i||[],i[0]=Math.min(Math.max(Math.min(o[0],o[1]),l),Math.max(o[0],o[1])),i[1]=Math.min(Math.max(Math.min(s[0],s[1]),u),Math.max(s[0],s[1])),i},t.prototype.pointToData=function(e,i){var n=[];if(this._invTransform)return ie(n,e,this._invTransform);var a=this.getAxis("x"),o=this.getAxis("y");return n[0]=a.coordToData(a.toLocalCoord(e[0]),i),n[1]=o.coordToData(o.toLocalCoord(e[1]),i),n},t.prototype.getOtherAxis=function(e){return this.getAxis(e.dim==="x"?"y":"x")},t.prototype.getArea=function(e){e=e||0;var i=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),a=Math.min(i[0],i[1])-e,o=Math.min(n[0],n[1])-e,s=Math.max(i[0],i[1])-a+e,l=Math.max(n[0],n[1])-o+e;return new et(a,o,s,l)},t}(LD),PD=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this,e,i,n)||this;return s.index=0,s.type=a||"value",s.position=o||"bottom",s}return t.prototype.isHorizontal=function(){var e=this.position;return e==="top"||e==="bottom"},t.prototype.getGlobalExtent=function(e){var i=this.getExtent();return i[0]=this.toGlobalCoord(i[0]),i[1]=this.toGlobalCoord(i[1]),e&&i[0]>i[1]&&i.reverse(),i},t.prototype.pointToData=function(e,i){return this.coordToData(this.toLocalCoord(e[this.dim==="x"?0:1]),i)},t.prototype.setCategorySortInfo=function(e){if(this.type!=="category")return!1;this.model.option.categorySortInfo=e,this.scale.setSortInfo(e)},t}(BC);const RD=PD;function iu(r,t,e){e=e||{};var i=r.coordinateSystem,n=t.axis,a={},o=n.getAxesOnZeroOf()[0],s=n.position,l=o?"onZero":s,u=n.dim,f=i.getRect(),h=[f.x,f.x+f.width,f.y,f.y+f.height],c={left:0,right:1,top:0,bottom:1,onZero:2},v=t.get("offset")||0,d=u==="x"?[h[2]-v,h[3]+v]:[h[0]-v,h[1]+v];if(o){var y=o.toGlobalCoord(o.dataToCoord(0));d[c.onZero]=Math.max(Math.min(y,d[1]),d[0])}a.position=[u==="y"?d[c[l]]:h[0],u==="x"?d[c[l]]:h[3]],a.rotation=Math.PI/2*(u==="x"?0:1);var p={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=p[s],a.labelOffset=o?d[c[s]]-d[c.onZero]:0,t.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),Tn(e.labelInside,t.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var g=t.get(["axisLabel","rotate"]);return a.labelRotate=l==="top"?-g:g,a.z2=1,a}function Tc(r){return r.get("coordinateSystem")==="cartesian2d"}function Cc(r){var t={xAxisModel:null,yAxisModel:null};return A(t,function(e,i){var n=i.replace(/Model$/,""),a=r.getReferringComponents(n,he).models[0];t[i]=a}),t}var js=Math.log;function ED(r,t,e){var i=$n.prototype,n=i.getTicks.call(e),a=i.getTicks.call(e,!0),o=n.length-1,s=i.getInterval.call(e),l=my(r,t),u=l.extent,f=l.fixMin,h=l.fixMax;if(r.type==="log"){var c=js(r.base);u=[js(u[0])/c,js(u[1])/c]}r.setExtent(u[0],u[1]),r.calcNiceExtent({splitNumber:o,fixMin:f,fixMax:h});var v=i.getExtent.call(r);f&&(u[0]=v[0]),h&&(u[1]=v[1]);var d=i.getInterval.call(r),y=u[0],p=u[1];if(f&&h)d=(p-y)/o;else if(f)for(p=u[0]+d*o;p<u[1]&&isFinite(p)&&isFinite(u[1]);)d=qs(d),p=u[0]+d*o;else if(h)for(y=u[1]-d*o;y>u[0]&&isFinite(y)&&isFinite(u[0]);)d=qs(d),y=u[1]-d*o;else{var g=r.getTicks().length-1;g>o&&(d=qs(d));var m=d*o;p=Math.ceil(u[1]/d)*d,y=pt(p-m),y<0&&u[0]>=0?(y=0,p=pt(m)):p>0&&u[1]<=0&&(p=0,y=-pt(m))}var _=(n[0].value-a[0].value)/s,S=(n[o].value-a[o].value)/s;i.setExtent.call(r,y+d*_,p+d*S),i.setInterval.call(r,d),(_||S)&&i.setNiceExtent.call(r,y+d,p-d)}var kD=function(){function r(t,e,i){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=ru,this._initCartesian(t,e,i),this.model=t}return r.prototype.getRect=function(){return this._rect},r.prototype.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model);function n(o){var s,l=ht(o),u=l.length;if(u){for(var f=[],h=u-1;h>=0;h--){var c=+l[h],v=o[c],d=v.model,y=v.scale;jl(y)&&d.get("alignTicks")&&d.get("interval")==null?f.push(v):(lc(y,d),jl(y)&&(s=v))}f.length&&(s||(s=f.pop(),lc(s.scale,s.model)),A(f,function(p){ED(p.scale,p.model,s.scale)}))}}n(i.x),n(i.y);var a={};A(i.x,function(o){Dc(i,"y",o,a)}),A(i.y,function(o){Dc(i,"x",o,a)}),this.resize(this.model,e)},r.prototype.resize=function(t,e,i){var n=t.getBoxLayoutParams(),a=!i&&t.get("containLabel"),o=In(n,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var s=this._axesList;l(),a&&(A(s,function(u){if(!u.model.get(["axisLabel","inside"])){var f=wC(u);if(f){var h=u.isHorizontal()?"height":"width",c=u.model.get(["axisLabel","margin"]);o[h]-=f[h]+c,u.position==="top"?o.y+=f.height+c:u.position==="left"&&(o.x+=f.width+c)}}}),l()),A(this._coordsList,function(u){u.calcAffineTransform()});function l(){A(s,function(u){var f=u.isHorizontal(),h=f?[0,o.width]:[0,o.height],c=u.inverse?1:0;u.setExtent(h[c],h[1-c]),OD(u,f?o.x:o.y)})}},r.prototype.getAxis=function(t,e){var i=this._axesMap[t];if(i!=null)return i[e||0]},r.prototype.getAxes=function(){return this._axesList.slice()},r.prototype.getCartesian=function(t,e){if(t!=null&&e!=null){var i="x"+t+"y"+e;return this._coordsMap[i]}H(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,a=this._coordsList;n<a.length;n++)if(a[n].getAxis("x").index===t||a[n].getAxis("y").index===e)return a[n]},r.prototype.getCartesians=function(){return this._coordsList.slice()},r.prototype.convertToPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},r.prototype.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},r.prototype._findConvertTarget=function(t){var e=t.seriesModel,i=t.xAxisModel||e&&e.getReferringComponents("xAxis",he).models[0],n=t.yAxisModel||e&&e.getReferringComponents("yAxis",he).models[0],a=t.gridModel,o=this._coordsList,s,l;if(e)s=e.coordinateSystem,at(o,s)<0&&(s=null);else if(i&&n)s=this.getCartesian(i.componentIndex,n.componentIndex);else if(i)l=this.getAxis("x",i.componentIndex);else if(n)l=this.getAxis("y",n.componentIndex);else if(a){var u=a.coordinateSystem;u===this&&(s=this._coordsList[0])}return{cartesian:s,axis:l}},r.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},r.prototype._initCartesian=function(t,e,i){var n=this,a=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},l={x:0,y:0};if(e.eachComponent("xAxis",u("x"),this),e.eachComponent("yAxis",u("y"),this),!l.x||!l.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,A(s.x,function(f,h){A(s.y,function(c,v){var d="x"+h+"y"+v,y=new ID(d);y.master=n,y.model=t,n._coordsMap[d]=y,n._coordsList.push(y),y.addAxis(f),y.addAxis(c)})});function u(f){return function(h,c){if(tl(h,t)){var v=h.get("position");f==="x"?v!=="top"&&v!=="bottom"&&(v=o.bottom?"top":"bottom"):v!=="left"&&v!=="right"&&(v=o.left?"right":"left"),o[v]=!0;var d=new RD(f,_C(h),[0,0],h.get("type"),v),y=d.type==="category";d.onBand=y&&h.get("boundaryGap"),d.inverse=h.get("inverse"),h.axis=d,d.model=h,d.grid=a,d.index=c,a._axesList.push(d),s[f][c]=d,l[f]++}}}},r.prototype._updateScale=function(t,e){A(this._axesList,function(n){if(n.scale.setExtent(1/0,-1/0),n.type==="category"){var a=n.model.get("categorySortInfo");n.scale.setSortInfo(a)}}),t.eachSeries(function(n){if(Tc(n)){var a=Cc(n),o=a.xAxisModel,s=a.yAxisModel;if(!tl(o,e)||!tl(s,e))return;var l=this.getCartesian(o.componentIndex,s.componentIndex),u=n.getData(),f=l.getAxis("x"),h=l.getAxis("y");i(u,f),i(u,h)}},this);function i(n,a){A(xC(n,a.dim),function(o){a.scale.unionExtentFromData(n,o)})}},r.prototype.getTooltipAxes=function(t){var e=[],i=[];return A(this.getCartesians(),function(n){var a=t!=null&&t!=="auto"?n.getAxis(t):n.getBaseAxis(),o=n.getOtherAxis(a);at(e,a)<0&&e.push(a),at(i,o)<0&&i.push(o)}),{baseAxes:e,otherAxes:i}},r.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,a){var o=new r(n,t,e);o.name="grid_"+a,o.resize(n,e,!0),n.coordinateSystem=o,i.push(o)}),t.eachSeries(function(n){if(Tc(n)){var a=Cc(n),o=a.xAxisModel,s=a.yAxisModel,l=o.getCoordSysModel(),u=l.coordinateSystem;n.coordinateSystem=u.getCartesian(o.componentIndex,s.componentIndex)}}),i},r.dimensions=ru,r}();function tl(r,t){return r.getCoordSysModel()===t}function Dc(r,t,e,i){e.getAxesOnZeroOf=function(){return a?[a]:[]};var n=r[t],a,o=e.model,s=o.get(["axisLine","onZero"]),l=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(l!=null)Mc(n[l])&&(a=n[l]);else for(var u in n)if(n.hasOwnProperty(u)&&Mc(n[u])&&!i[f(n[u])]){a=n[u];break}a&&(i[f(a)]=!0);function f(h){return h.dim+"_"+h.index}}function Mc(r){return r&&r.type!=="category"&&r.type!=="time"&&SC(r)}function OD(r,t){var e=r.getExtent(),i=e[0]+e[1];r.toGlobalCoord=r.dim==="x"?function(n){return n+t}:function(n){return i-n+t},r.toLocalCoord=r.dim==="x"?function(n){return n-t}:function(n){return i-n+t}}const BD=kD;var Je=Math.PI,Nr=function(){function r(t,e){this.group=new Et,this.opt=e,this.axisModel=t,it(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var i=new Et({x:e.position[0],y:e.position[1],rotation:e.rotation});i.updateTransform(),this._transformGroup=i}return r.prototype.hasBuilder=function(t){return!!Ac[t]},r.prototype.add=function(t){Ac[t](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(t,e,i){var n=Rd(e-t),a,o;return to(n)?(o=i>0?"top":"bottom",a="center"):to(n-Je)?(o=i>0?"bottom":"top",a="center"):(o="middle",n>0&&n<Je?a=i>0?"right":"left":a=i>0?"left":"right"),{rotation:n,textAlign:a,textVerticalAlign:o}},r.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},r.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},r}(),Ac={axisLine:function(r,t,e,i){var n=t.get(["axisLine","show"]);if(n==="auto"&&r.handleAutoShown&&(n=r.handleAutoShown("axisLine")),!!n){var a=t.axis.getExtent(),o=i.transform,s=[a[0],0],l=[a[1],0],u=s[0]>l[0];o&&(ie(s,s,o),ie(l,l,o));var f=k({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new Wr({shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});An(h.shape,h.style.lineWidth),h.anid="line",e.add(h);var c=t.get(["axisLine","symbol"]);if(c!=null){var v=t.get(["axisLine","symbolSize"]);z(c)&&(c=[c,c]),(z(v)||vt(v))&&(v=[v,v]);var d=Pg(t.get(["axisLine","symbolOffset"])||0,v),y=v[0],p=v[1];A([{rotate:r.rotation+Math.PI/2,offset:d[0],r:0},{rotate:r.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],function(g,m){if(c[m]!=="none"&&c[m]!=null){var _=Mi(c[m],-y/2,-p/2,y,p,f.stroke,!0),S=g.r+g.offset,b=u?l:s;_.attr({rotation:g.rotate,x:b[0]+S*Math.cos(r.rotation),y:b[1]-S*Math.sin(r.rotation),silent:!0,z2:11}),e.add(_)}})}}},axisTickLabel:function(r,t,e,i){var n=zD(e,i,t,r),a=GD(e,i,t,r);if(FD(t,a,n),HD(e,i,t,r.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=NC(V(a,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));zC(o)}},axisName:function(r,t,e,i){var n=Tn(r.axisName,t.get("name"));if(n){var a=t.get("nameLocation"),o=r.nameDirection,s=t.getModel("nameTextStyle"),l=t.get("nameGap")||0,u=t.axis.getExtent(),f=u[0]>u[1]?-1:1,h=[a==="start"?u[0]-f*l:a==="end"?u[1]+f*l:(u[0]+u[1])/2,Ic(a)?r.labelOffset+o*l:0],c,v=t.get("nameRotate");v!=null&&(v=v*Je/180);var d;Ic(a)?c=Nr.innerTextLayout(r.rotation,v??r.rotation,o):(c=ND(r.rotation,a,v||0,u),d=r.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(c.rotation)),!isFinite(d)&&(d=null)));var y=s.getFont(),p=t.get("nameTruncate",!0)||{},g=p.ellipsis,m=Tn(r.nameTruncateMaxWidth,p.maxWidth,d),_=new kt({x:h[0],y:h[1],rotation:c.rotation,silent:Nr.isLabelSilent(t),style:ar(s,{text:n,font:y,overflow:"truncate",width:m,ellipsis:g,fill:s.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:s.get("align")||c.textAlign,verticalAlign:s.get("verticalAlign")||c.textVerticalAlign}),z2:1});if(Do({el:_,componentModel:t,itemName:n}),_.__fullText=n,_.anid="name",t.get("triggerEvent")){var S=Nr.makeAxisEventDataBase(t);S.targetType="axisName",S.name=n,rt(_).eventData=S}i.add(_),_.updateTransform(),e.add(_),_.decomposeTransform()}}};function ND(r,t,e,i){var n=Rd(e-r),a,o,s=i[0]>i[1],l=t==="start"&&!s||t!=="start"&&s;return to(n-Je/2)?(o=l?"bottom":"top",a="center"):to(n-Je*1.5)?(o=l?"top":"bottom",a="center"):(o="middle",n<Je*1.5&&n>Je/2?a=l?"left":"right":a=l?"right":"left"),{rotation:n,textAlign:a,textVerticalAlign:o}}function FD(r,t,e){if(!_y(r.axis)){var i=r.get(["axisLabel","showMinLabel"]),n=r.get(["axisLabel","showMaxLabel"]);t=t||[],e=e||[];var a=t[0],o=t[1],s=t[t.length-1],l=t[t.length-2],u=e[0],f=e[1],h=e[e.length-1],c=e[e.length-2];i===!1?(Zt(a),Zt(u)):Lc(a,o)&&(i?(Zt(o),Zt(f)):(Zt(a),Zt(u))),n===!1?(Zt(s),Zt(h)):Lc(l,s)&&(n?(Zt(l),Zt(c)):(Zt(s),Zt(h)))}}function Zt(r){r&&(r.ignore=!0)}function Lc(r,t){var e=r&&r.getBoundingRect().clone(),i=t&&t.getBoundingRect().clone();if(!(!e||!i)){var n=yu([]);return mu(n,n,-r.rotation),e.applyTransform(Si([],n,r.getLocalTransform())),i.applyTransform(Si([],n,t.getLocalTransform())),e.intersect(i)}}function Ic(r){return r==="middle"||r==="center"}function Oy(r,t,e,i,n){for(var a=[],o=[],s=[],l=0;l<r.length;l++){var u=r[l].coord;o[0]=u,o[1]=0,s[0]=u,s[1]=e,t&&(ie(o,o,t),ie(s,s,t));var f=new Wr({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});An(f.shape,f.style.lineWidth),f.anid=n+"_"+r[l].tickValue,a.push(f)}return a}function zD(r,t,e,i){var n=e.axis,a=e.getModel("axisTick"),o=a.get("show");if(o==="auto"&&i.handleAutoShown&&(o=i.handleAutoShown("axisTick")),!(!o||n.scale.isBlank())){for(var s=a.getModel("lineStyle"),l=i.tickDirection*a.get("length"),u=n.getTicksCoords(),f=Oy(u,t.transform,l,it(s.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<f.length;h++)r.add(f[h]);return f}}function HD(r,t,e,i){var n=e.axis,a=e.getModel("minorTick");if(!(!a.get("show")||n.scale.isBlank())){var o=n.getMinorTicksCoords();if(o.length)for(var s=a.getModel("lineStyle"),l=i*a.get("length"),u=it(s.getLineStyle(),it(e.getModel("axisTick").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var h=Oy(o[f],t.transform,l,u,"minorticks_"+f),c=0;c<h.length;c++)r.add(h[c])}}function GD(r,t,e,i){var n=e.axis,a=Tn(i.axisLabelShow,e.get(["axisLabel","show"]));if(!(!a||n.scale.isBlank())){var o=e.getModel("axisLabel"),s=o.get("margin"),l=n.getViewLabels(),u=(Tn(i.labelRotate,o.get("rotate"))||0)*Je/180,f=Nr.innerTextLayout(i.rotation,u,i.labelDirection),h=e.getCategories&&e.getCategories(!0),c=[],v=Nr.isLabelSilent(e),d=e.get("triggerEvent");return A(l,function(y,p){var g=n.scale.type==="ordinal"?n.scale.getRawOrdinalNumber(y.tickValue):y.tickValue,m=y.formattedLabel,_=y.rawLabel,S=o;if(h&&h[g]){var b=h[g];H(b)&&b.textStyle&&(S=new At(b.textStyle,o,e.ecModel))}var w=S.getTextColor()||e.get(["axisLine","lineStyle","color"]),x=n.dataToCoord(g),C=S.getShallow("align",!0)||f.textAlign,T=Z(S.getShallow("alignMinLabel",!0),C),D=Z(S.getShallow("alignMaxLabel",!0),C),M=S.getShallow("verticalAlign",!0)||S.getShallow("baseline",!0)||f.textVerticalAlign,L=Z(S.getShallow("verticalAlignMinLabel",!0),M),I=Z(S.getShallow("verticalAlignMaxLabel",!0),M),P=new kt({x,y:i.labelOffset+i.labelDirection*s,rotation:f.rotation,silent:v,z2:10+(y.level||0),style:ar(S,{text:m,align:p===0?T:p===l.length-1?D:C,verticalAlign:p===0?L:p===l.length-1?I:M,fill:$(w)?w(n.type==="category"?_:n.type==="value"?g+"":g,p):w})});if(P.anid="label_"+g,Do({el:P,componentModel:e,itemName:m,formatterParamsExtra:{isTruncated:function(){return P.isTruncated},value:_,tickIndex:p}}),d){var R=Nr.makeAxisEventDataBase(e);R.targetType="axisLabel",R.value=_,R.tickIndex=p,n.type==="category"&&(R.dataIndex=g),rt(P).eventData=R}t.add(P),P.updateTransform(),c.push(P),r.add(P),P.decomposeTransform()}),c}}const By=Nr;function VD(r,t){var e={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return WD(e,r,t),e.seriesInvolved&&$D(e,r),e}function WD(r,t,e){var i=t.getComponent("tooltip"),n=t.getComponent("axisPointer"),a=n.get("link",!0)||[],o=[];A(e.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var l=Fn(s.model),u=r.coordSysAxesInfo[l]={};r.coordSysMap[l]=s;var f=s.model,h=f.getModel("tooltip",i);if(A(s.getAxes(),mt(y,!1,null)),s.getTooltipAxes&&i&&h.get("show")){var c=h.get("trigger")==="axis",v=h.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(h.get(["axisPointer","axis"]));(c||v)&&A(d.baseAxes,mt(y,v?"cross":!0,c)),v&&A(d.otherAxes,mt(y,"cross",!1))}function y(p,g,m){var _=m.model.getModel("axisPointer",n),S=_.get("show");if(!(!S||S==="auto"&&!p&&!nu(_))){g==null&&(g=_.get("triggerTooltip")),_=p?UD(m,h,n,t,p,g):_;var b=_.get("snap"),w=_.get("triggerEmphasis"),x=Fn(m.model),C=g||b||m.type==="category",T=r.axesInfo[x]={key:x,axis:m,coordSys:s,axisPointerModel:_,triggerTooltip:g,triggerEmphasis:w,involveSeries:C,snap:b,useHandle:nu(_),seriesModels:[],linkGroup:null};u[x]=T,r.seriesInvolved=r.seriesInvolved||C;var D=YD(a,m);if(D!=null){var M=o[D]||(o[D]={axesInfo:{}});M.axesInfo[x]=T,M.mapper=a[D].mapper,T.linkGroup=M}}}})}function UD(r,t,e,i,n,a){var o=t.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],l={};A(s,function(c){l[c]=J(o.get(c))}),l.snap=r.type!=="category"&&!!a,o.get("type")==="cross"&&(l.type="line");var u=l.label||(l.label={});if(u.show==null&&(u.show=!1),n==="cross"){var f=o.get(["label","show"]);if(u.show=f??!0,!a){var h=l.lineStyle=o.get("crossStyle");h&&it(u,h.textStyle)}}return r.model.getModel("axisPointer",new At(l,e,i))}function $D(r,t){t.eachSeries(function(e){var i=e.coordinateSystem,n=e.get(["tooltip","trigger"],!0),a=e.get(["tooltip","show"],!0);!i||n==="none"||n===!1||n==="item"||a===!1||e.get(["axisPointer","show"],!0)===!1||A(r.coordSysAxesInfo[Fn(i.model)],function(o){var s=o.axis;i.getAxis(s.dim)===s&&(o.seriesModels.push(e),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=e.getData().count())})})}function YD(r,t){for(var e=t.model,i=t.dim,n=0;n<r.length;n++){var a=r[n]||{};if(el(a[i+"AxisId"],e.id)||el(a[i+"AxisIndex"],e.componentIndex)||el(a[i+"AxisName"],e.name))return n}}function el(r,t){return r==="all"||N(r)&&at(r,t)>=0||r===t}function XD(r){var t=Sf(r);if(t){var e=t.axisPointerModel,i=t.axis.scale,n=e.option,a=e.get("status"),o=e.get("value");o!=null&&(o=i.parse(o));var s=nu(e);a==null&&(n.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(o==null||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),n.value=o,s&&(n.status=t.axis.scale.isBlank()?"hide":"show")}}function Sf(r){var t=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return t&&t.axesInfo[Fn(r)]}function ZD(r){var t=Sf(r);return t&&t.axisPointerModel}function nu(r){return!!r.get(["handle","show"])}function Fn(r){return r.type+"||"+r.id}var Pc={},qD=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n,a){this.axisPointerClass&&XD(e),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,n,!0)},t.prototype.updateAxisPointer=function(e,i,n,a){this._doUpdateAxisPointerClass(e,n,!1)},t.prototype.remove=function(e,i){var n=this._axisPointer;n&&n.remove(i)},t.prototype.dispose=function(e,i){this._disposeAxisPointer(i),r.prototype.dispose.apply(this,arguments)},t.prototype._doUpdateAxisPointerClass=function(e,i,n){var a=t.getAxisPointerClass(this.axisPointerClass);if(a){var o=ZD(e);o?(this._axisPointer||(this._axisPointer=new a)).render(e,o,i,n):this._disposeAxisPointer(i)}},t.prototype._disposeAxisPointer=function(e){this._axisPointer&&this._axisPointer.dispose(e),this._axisPointer=null},t.registerAxisPointerClass=function(e,i){Pc[e]=i},t.getAxisPointerClass=function(e){return e&&Pc[e]},t.type="axis",t}(Be);const Ny=qD;var au=yt();function KD(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitArea"),o=a.getModel("areaStyle"),s=o.get("color"),l=i.coordinateSystem.getRect(),u=n.getTicksCoords({tickModel:a,clamp:!0});if(u.length){var f=s.length,h=au(r).splitAreaColors,c=X(),v=0;if(h)for(var d=0;d<u.length;d++){var y=h.get(u[d].tickValue);if(y!=null){v=(y+(f-1)*d)%f;break}}var p=n.toGlobalCoord(u[0].coord),g=o.getAreaStyle();s=N(s)?s:[s];for(var d=1;d<u.length;d++){var m=n.toGlobalCoord(u[d].coord),_=void 0,S=void 0,b=void 0,w=void 0;n.isHorizontal()?(_=p,S=l.y,b=m-_,w=l.height,p=_+b):(_=l.x,S=p,b=l.width,w=m-S,p=S+w);var x=u[d-1].tickValue;x!=null&&c.set(x,v),t.add(new bt({anid:x!=null?"area_"+x:null,shape:{x:_,y:S,width:b,height:w},style:it({fill:s[v]},g),autoBatch:!0,silent:!0})),v=(v+1)%f}au(r).splitAreaColors=c}}}function QD(r){au(r).splitAreaColors=null}var JD=["axisLine","axisTickLabel","axisName"],jD=["splitArea","splitLine","minorSplitLine"],Fy=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.axisPointerClass="CartesianAxisPointer",e}return t.prototype.render=function(e,i,n,a){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Et,this.group.add(this._axisGroup),!!e.get("show")){var s=e.getCoordSysModel(),l=iu(s,e),u=new By(e,k({handleAutoShown:function(h){for(var c=s.coordinateSystem.getCartesians(),v=0;v<c.length;v++)if(jl(c[v].getOtherAxis(e.axis).scale))return!0;return!1}},l));A(JD,u.add,u),this._axisGroup.add(u.getGroup()),A(jD,function(h){e.get([h,"show"])&&tM[h](this,this._axisGroup,e,s)},this);var f=a&&a.type==="changeAxisOrder"&&a.isInitSort;f||Ep(o,this._axisGroup,e),r.prototype.render.call(this,e,i,n,a)}},t.prototype.remove=function(){QD(this)},t.type="cartesianAxis",t}(Ny),tM={splitLine:function(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitLine"),o=a.getModel("lineStyle"),s=o.get("color"),l=a.get("showMinLine")!==!1,u=a.get("showMaxLine")!==!1;s=N(s)?s:[s];for(var f=i.coordinateSystem.getRect(),h=n.isHorizontal(),c=0,v=n.getTicksCoords({tickModel:a}),d=[],y=[],p=o.getLineStyle(),g=0;g<v.length;g++){var m=n.toGlobalCoord(v[g].coord);if(!(g===0&&!l||g===v.length-1&&!u)){var _=v[g].tickValue;h?(d[0]=m,d[1]=f.y,y[0]=m,y[1]=f.y+f.height):(d[0]=f.x,d[1]=m,y[0]=f.x+f.width,y[1]=m);var S=c++%s.length,b=new Wr({anid:_!=null?"line_"+_:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:y[0],y2:y[1]},style:it({stroke:s[S]},p),silent:!0});An(b.shape,p.lineWidth),t.add(b)}}}},minorSplitLine:function(r,t,e,i){var n=e.axis,a=e.getModel("minorSplitLine"),o=a.getModel("lineStyle"),s=i.coordinateSystem.getRect(),l=n.isHorizontal(),u=n.getMinorTicksCoords();if(u.length)for(var f=[],h=[],c=o.getLineStyle(),v=0;v<u.length;v++)for(var d=0;d<u[v].length;d++){var y=n.toGlobalCoord(u[v][d].coord);l?(f[0]=y,f[1]=s.y,h[0]=y,h[1]=s.y+s.height):(f[0]=s.x,f[1]=y,h[0]=s.x+s.width,h[1]=y);var p=new Wr({anid:"minor_line_"+u[v][d].tickValue,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:h[0],y2:h[1]},style:c,silent:!0});An(p.shape,c.lineWidth),t.add(p)}},splitArea:function(r,t,e,i){KD(r,t,e,i)}},zy=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="xAxis",t}(Fy),eM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=zy.type,e}return t.type="yAxis",t}(Fy),rM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="grid",e}return t.prototype.render=function(e,i){this.group.removeAll(),e.get("show")&&this.group.add(new bt({shape:e.coordinateSystem.getRect(),style:it({fill:e.get("backgroundColor")},e.getItemStyle()),silent:!0,z2:-1}))},t.type="grid",t}(Be),Rc={offset:0};function iM(r){r.registerComponentView(rM),r.registerComponentModel(bD),r.registerCoordinateSystem("cartesian2d",BD),wc(r,"x",eu,Rc),wc(r,"y",eu,Rc),r.registerComponentView(zy),r.registerComponentView(eM),r.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}var Ir=yt(),Ec=J,rl=ft,nM=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(t,e,i,n){var a=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,!(!n&&this._lastValue===a&&this._lastStatus===o)){this._lastValue=a,this._lastStatus=o;var s=this._group,l=this._handle;if(!o||o==="hide"){s&&s.hide(),l&&l.hide();return}s&&s.show(),l&&l.show();var u={};this.makeElOption(u,a,t,e,i);var f=u.graphicKey;f!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=f;var h=this._moveAnimation=this.determineAnimation(t,e);if(!s)s=this._group=new Et,this.createPointerEl(s,u,t,e),this.createLabelEl(s,u,t,e),i.getZr().add(s);else{var c=mt(kc,e,h);this.updatePointerEl(s,u,c),this.updateLabelEl(s,u,c,e)}Bc(s,e,!0),this._renderHandle(a)}},r.prototype.remove=function(t){this.clear(t)},r.prototype.dispose=function(t){this.clear(t)},r.prototype.determineAnimation=function(t,e){var i=e.get("animation"),n=t.axis,a=n.type==="category",o=e.get("snap");if(!o&&!a)return!1;if(i==="auto"||i==null){var s=this.animationThreshold;if(a&&n.getBandWidth()>s)return!0;if(o){var l=Sf(t).seriesDataCount,u=n.getExtent();return Math.abs(u[0]-u[1])/l>s}return!1}return i===!0},r.prototype.makeElOption=function(t,e,i,n,a){},r.prototype.createPointerEl=function(t,e,i,n){var a=e.pointer;if(a){var o=Ir(t).pointerEl=new US[a.type](Ec(e.pointer));t.add(o)}},r.prototype.createLabelEl=function(t,e,i,n){if(e.label){var a=Ir(t).labelEl=new kt(Ec(e.label));t.add(a),Oc(a,n)}},r.prototype.updatePointerEl=function(t,e,i){var n=Ir(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},r.prototype.updateLabelEl=function(t,e,i,n){var a=Ir(t).labelEl;a&&(a.setStyle(e.label.style),i(a,{x:e.label.x,y:e.label.y}),Oc(a,n))},r.prototype._renderHandle=function(t){if(!(this._dragging||!this.updateHandleTransform)){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,a=e.getModel("handle"),o=e.get("status");if(!a.get("show")||!o||o==="hide"){n&&i.remove(n),this._handle=null;return}var s;this._handle||(s=!0,n=this._handle=zu(a.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(u){ud(u.event)},onmousedown:rl(this._onHandleDragMove,this,0,0),drift:rl(this._onHandleDragMove,this),ondragend:rl(this._onHandleDragEnd,this)}),i.add(n)),Bc(n,e,!1),n.setStyle(a.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var l=a.get("size");N(l)||(l=[l,l]),n.scaleX=l[0]/2,n.scaleY=l[1]/2,xg(this,"_doDispatchAxisPointer",a.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,s)}},r.prototype._moveHandleToValue=function(t,e){kc(this._axisPointerModel,!e&&this._moveAnimation,this._handle,il(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(il(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(il(n)),Ir(i).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),Wl(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}},r}();function kc(r,t,e,i){Hy(Ir(e).lastProp,i)||(Ir(e).lastProp=i,t?nr(e,i,r):(e.stopAnimation(),e.attr(i)))}function Hy(r,t){if(H(r)&&H(t)){var e=!0;return A(t,function(i,n){e=e&&Hy(r[n],i)}),!!e}else return r===t}function Oc(r,t){r[t.get(["label","show"])?"show":"hide"]()}function il(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function Bc(r,t,e){var i=t.get("z"),n=t.get("zlevel");r&&r.traverse(function(a){a.type!=="group"&&(i!=null&&(a.z=i),n!=null&&(a.zlevel=n),a.silent=e)})}const aM=nM;function oM(r){var t=r.get("type"),e=r.getModel(t+"Style"),i;return t==="line"?(i=e.getLineStyle(),i.fill=null):t==="shadow"&&(i=e.getAreaStyle(),i.stroke=null),i}function sM(r,t,e,i,n){var a=e.get("value"),o=Gy(a,t.axis,t.ecModel,e.get("seriesDataIndices"),{precision:e.get(["label","precision"]),formatter:e.get(["label","formatter"])}),s=e.getModel("label"),l=ko(s.get("padding")||0),u=s.getFont(),f=bu(o,u),h=n.position,c=f.width+l[1]+l[3],v=f.height+l[0]+l[2],d=n.align;d==="right"&&(h[0]-=c),d==="center"&&(h[0]-=c/2);var y=n.verticalAlign;y==="bottom"&&(h[1]-=v),y==="middle"&&(h[1]-=v/2),lM(h,c,v,i);var p=s.get("backgroundColor");(!p||p==="auto")&&(p=t.get(["axisLine","lineStyle","color"])),r.label={x:h[0],y:h[1],style:ar(s,{text:o,font:u,fill:s.getTextColor(),padding:l,backgroundColor:p}),z2:10}}function lM(r,t,e,i){var n=i.getWidth(),a=i.getHeight();r[0]=Math.min(r[0]+t,n)-t,r[1]=Math.min(r[1]+e,a)-e,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function Gy(r,t,e,i,n){r=t.scale.parse(r);var a=t.scale.getLabel({value:r},{precision:n.precision}),o=n.formatter;if(o){var s={value:gf(t,{value:r}),axisDimension:t.dim,axisIndex:t.index,seriesData:[]};A(i,function(l){var u=e.getSeriesByIndex(l.seriesIndex),f=l.dataIndexInside,h=u&&u.getDataParams(f);h&&s.seriesData.push(h)}),z(o)?a=o.replace("{value}",a):$(o)&&(a=o(s))}return a}function Vy(r,t,e){var i=_i();return mu(i,i,e.rotation),cl(i,i,e.position),Fu([r.dataToCoord(t),(e.labelOffset||0)+(e.labelDirection||1)*(e.labelMargin||0)],i)}function uM(r,t,e,i,n,a){var o=By.innerTextLayout(e.rotation,0,e.labelDirection);e.labelMargin=n.get(["label","margin"]),sM(t,i,n,a,{position:Vy(i.axis,r,e),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function fM(r,t,e){return e=e||0,{x1:r[e],y1:r[1-e],x2:t[e],y2:t[1-e]}}function hM(r,t,e){return e=e||0,{x:r[e],y:r[1-e],width:t[e],height:t[1-e]}}var vM=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.makeElOption=function(e,i,n,a,o){var s=n.axis,l=s.grid,u=a.get("type"),f=Nc(l,s).getOtherAxis(s).getGlobalExtent(),h=s.toGlobalCoord(s.dataToCoord(i,!0));if(u&&u!=="none"){var c=oM(a),v=cM[u](s,h,f);v.style=c,e.graphicKey=v.type,e.pointer=v}var d=iu(l.model,n);uM(i,e,d,n,a,o)},t.prototype.getHandleTransform=function(e,i,n){var a=iu(i.axis.grid.model,i,{labelInside:!1});a.labelMargin=n.get(["handle","margin"]);var o=Vy(i.axis,e,a);return{x:o[0],y:o[1],rotation:a.rotation+(a.labelDirection<0?Math.PI:0)}},t.prototype.updateHandleTransform=function(e,i,n,a){var o=n.axis,s=o.grid,l=o.getGlobalExtent(!0),u=Nc(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,h=[e.x,e.y];h[f]+=i[f],h[f]=Math.min(l[1],h[f]),h[f]=Math.max(l[0],h[f]);var c=(u[1]+u[0])/2,v=[c,c];v[f]=h[f];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:e.rotation,cursorPoint:v,tooltipOption:d[f]}},t}(aM);function Nc(r,t){var e={};return e[t.dim+"AxisIndex"]=t.index,r.getCartesian(e)}var cM={line:function(r,t,e){var i=fM([t,e[0]],[t,e[1]],Fc(r));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(r,t,e){var i=Math.max(1,r.getBandWidth()),n=e[1]-e[0];return{type:"Rect",shape:hM([t-i/2,e[0]],[i,n],Fc(r))}}};function Fc(r){return r.dim==="x"?0:1}const dM=vM;var pM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="axisPointer",t.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},t}(ut);const gM=pM;var Pe=yt(),yM=A;function Wy(r,t,e){if(!Y.node){var i=t.getZr();Pe(i).records||(Pe(i).records={}),mM(i,t);var n=Pe(i).records[r]||(Pe(i).records[r]={});n.handler=e}}function mM(r,t){if(Pe(r).initialized)return;Pe(r).initialized=!0,e("click",mt(zc,"click")),e("mousemove",mt(zc,"mousemove")),e("globalout",SM);function e(i,n){r.on(i,function(a){var o=wM(t);yM(Pe(r).records,function(s){s&&n(s,a,o.dispatchAction)}),_M(o.pendings,t)})}}function _M(r,t){var e=r.showTip.length,i=r.hideTip.length,n;e?n=r.showTip[e-1]:i&&(n=r.hideTip[i-1]),n&&(n.dispatchAction=null,t.dispatchAction(n))}function SM(r,t,e){r.handler("leave",null,e)}function zc(r,t,e,i){t.handler(r,e,i)}function wM(r){var t={showTip:[],hideTip:[]},e=function(i){var n=t[i.type];n?n.push(i):(i.dispatchAction=e,r.dispatchAction(i))};return{dispatchAction:e,pendings:t}}function ou(r,t){if(!Y.node){var e=t.getZr(),i=(Pe(e).records||{})[r];i&&(Pe(e).records[r]=null)}}var bM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){var a=i.getComponent("tooltip"),o=e.get("triggerOn")||a&&a.get("triggerOn")||"mousemove|click";Wy("axisPointer",n,function(s,l,u){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&u({type:"updateAxisPointer",currTrigger:s,x:l&&l.offsetX,y:l&&l.offsetY})})},t.prototype.remove=function(e,i){ou("axisPointer",i)},t.prototype.dispose=function(e,i){ou("axisPointer",i)},t.type="axisPointer",t}(Be);const xM=bM;function Uy(r,t){var e=[],i=r.seriesIndex,n;if(i==null||!(n=t.getSeriesByIndex(i)))return{point:[]};var a=n.getData(),o=Gr(a,r);if(o==null||o<0||N(o))return{point:[]};var s=a.getItemGraphicEl(o),l=n.coordinateSystem;if(n.getTooltipPosition)e=n.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)if(r.isStacked){var u=l.getBaseAxis(),f=l.getOtherAxis(u),h=f.dim,c=u.dim,v=h==="x"||h==="radius"?1:0,d=a.mapDimension(c),y=[];y[v]=a.get(d,o),y[1-v]=a.get(a.getCalculationInfo("stackResultDimension"),o),e=l.dataToPoint(y)||[]}else e=l.dataToPoint(a.getValues(V(l.dimensions,function(g){return a.mapDimension(g)}),o))||[];else if(s){var p=s.getBoundingRect().clone();p.applyTransform(s.transform),e=[p.x+p.width/2,p.y+p.height/2]}return{point:e,el:s}}var Hc=yt();function TM(r,t,e){var i=r.currTrigger,n=[r.x,r.y],a=r,o=r.dispatchAction||ft(e.dispatchAction,e),s=t.getComponent("axisPointer").coordSysAxesInfo;if(s){$a(n)&&(n=Uy({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},t).point);var l=$a(n),u=a.axesInfo,f=s.axesInfo,h=i==="leave"||$a(n),c={},v={},d={list:[],map:{}},y={showPointer:mt(DM,v),showTooltip:mt(MM,d)};A(s.coordSysMap,function(g,m){var _=l||g.containPoint(n);A(s.coordSysAxesInfo[m],function(S,b){var w=S.axis,x=PM(u,S);if(!h&&_&&(!u||x)){var C=x&&x.value;C==null&&!l&&(C=w.pointToData(n)),C!=null&&Gc(S,C,y,!1,c)}})});var p={};return A(f,function(g,m){var _=g.linkGroup;_&&!v[m]&&A(_.axesInfo,function(S,b){var w=v[b];if(S!==g&&w){var x=w.value;_.mapper&&(x=g.axis.scale.parse(_.mapper(x,Vc(S),Vc(g)))),p[g.key]=x}})}),A(p,function(g,m){Gc(f[m],g,y,!0,c)}),AM(v,f,c),LM(d,n,r,o),IM(f,o,e),c}}function Gc(r,t,e,i,n){var a=r.axis;if(!(a.scale.isBlank()||!a.containData(t))){if(!r.involveSeries){e.showPointer(r,t);return}var o=CM(t,r),s=o.payloadBatch,l=o.snapToValue;s[0]&&n.seriesIndex==null&&k(n,s[0]),!i&&r.snap&&a.containData(l)&&l!=null&&(t=l),e.showPointer(r,t,s),e.showTooltip(r,o,l)}}function CM(r,t){var e=t.axis,i=e.dim,n=r,a=[],o=Number.MAX_VALUE,s=-1;return A(t.seriesModels,function(l,u){var f=l.getData().mapDimensionsAll(i),h,c;if(l.getAxisTooltipData){var v=l.getAxisTooltipData(f,r,e);c=v.dataIndices,h=v.nestestValue}else{if(c=l.getData().indicesOfNearest(f[0],r,e.type==="category"?.5:null),!c.length)return;h=l.getData().get(f[0],c[0])}if(!(h==null||!isFinite(h))){var d=r-h,y=Math.abs(d);y<=o&&((y<o||d>=0&&s<0)&&(o=y,s=d,n=h,a.length=0),A(c,function(p){a.push({seriesIndex:l.seriesIndex,dataIndexInside:p,dataIndex:l.getData().getRawIndex(p)})}))}}),{payloadBatch:a,snapToValue:n}}function DM(r,t,e,i){r[t.key]={value:e,payloadBatch:i}}function MM(r,t,e,i){var n=e.payloadBatch,a=t.axis,o=a.model,s=t.axisPointerModel;if(!(!t.triggerTooltip||!n.length)){var l=t.coordSys.model,u=Fn(l),f=r.map[u];f||(f=r.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:n.slice()})}}function AM(r,t,e){var i=e.axesInfo=[];A(t,function(n,a){var o=n.axisPointerModel.option,s=r[a];s?(!n.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!n.useHandle&&(o.status="hide"),o.status==="show"&&i.push({axisDim:n.axis.dim,axisIndex:n.axis.model.componentIndex,value:o.value})})}function LM(r,t,e,i){if($a(t)||!r.list.length){i({type:"hideTip"});return}var n=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:e.tooltipOption,position:e.position,dataIndexInside:n.dataIndexInside,dataIndex:n.dataIndex,seriesIndex:n.seriesIndex,dataByCoordSys:r.list})}function IM(r,t,e){var i=e.getZr(),n="axisPointerLastHighlights",a=Hc(i)[n]||{},o=Hc(i)[n]={};A(r,function(u,f){var h=u.axisPointerModel.option;h.status==="show"&&u.triggerEmphasis&&A(h.seriesDataIndices,function(c){var v=c.seriesIndex+" | "+c.dataIndex;o[v]=c})});var s=[],l=[];A(a,function(u,f){!o[f]&&l.push(u)}),A(o,function(u,f){!a[f]&&s.push(u)}),l.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:l}),s.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function PM(r,t){for(var e=0;e<(r||[]).length;e++){var i=r[e];if(t.axis.dim===i.axisDim&&t.axis.model.componentIndex===i.axisIndex)return i}}function Vc(r){var t=r.axis.model,e={},i=e.axisDim=r.axis.dim;return e.axisIndex=e[i+"AxisIndex"]=t.componentIndex,e.axisName=e[i+"AxisName"]=t.name,e.axisId=e[i+"AxisId"]=t.id,e}function $a(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function $y(r){Ny.registerAxisPointerClass("CartesianAxisPointer",dM),r.registerComponentModel(gM),r.registerComponentView(xM),r.registerPreprocessor(function(t){if(t){(!t.axisPointer||t.axisPointer.length===0)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!N(e)&&(t.axisPointer.link=[e])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=VD(t,e)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},TM)}function VA(r){$r(iM),$r($y)}function RM(r,t){var e=ko(t.get("padding")),i=t.getItemStyle(["color","opacity"]);return i.fill=t.get("backgroundColor"),r=new bt({shape:{x:r.x-e[3],y:r.y-e[0],width:r.width+e[1]+e[3],height:r.height+e[0]+e[2],r:t.get("borderRadius")},style:i,silent:!0,z2:-1}),r}var EM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}(ut);const kM=EM;function Yy(r){var t=r.get("confine");return t!=null?!!t:r.get("renderMode")==="richText"}function Xy(r){if(Y.domSupported){for(var t=document.documentElement.style,e=0,i=r.length;e<i;e++)if(r[e]in t)return r[e]}}var Zy=Xy(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),OM=Xy(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function qy(r,t){if(!r)return t;t=Zp(t,!0);var e=r.indexOf(t);return r=e===-1?t:"-"+r.slice(0,e)+"-"+t,r.toLowerCase()}function BM(r,t){var e=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return e?t?e[t]:e:null}var NM=qy(OM,"transition"),wf=qy(Zy,"transform"),FM="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(Y.transform3dSupported?"will-change:transform;":"");function zM(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function HM(r,t,e){if(!z(e)||e==="inside")return"";var i=r.get("backgroundColor"),n=r.get("borderWidth");t=Ur(t);var a=zM(e),o=Math.max(Math.round(n)*1.5,6),s="",l=wf+":",u;at(["left","right"],a)>-1?(s+="top:50%",l+="translateY(-50%) rotate("+(u=a==="left"?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(u=a==="top"?225:45)+"deg)");var f=u*Math.PI/180,h=o+n,c=h*Math.abs(Math.cos(f))+h*Math.abs(Math.sin(f)),v=Math.round(((c-Math.SQRT2*n)/2+Math.SQRT2*n-(c-h)/2)*100)/100;s+=";"+a+":-"+v+"px";var d=t+" solid "+n+"px;",y=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+l+";","border-bottom:"+d,"border-right:"+d,"background-color:"+i+";"];return'<div style="'+y.join("")+'"></div>'}function GM(r,t){var e="cubic-bezier(0.23,1,0.32,1)",i=" "+r/2+"s "+e,n="opacity"+i+",visibility"+i;return t||(i=" "+r+"s "+e,n+=Y.transformSupported?","+wf+i:",left"+i+",top"+i),NM+":"+n}function Wc(r,t,e){var i=r.toFixed(0)+"px",n=t.toFixed(0)+"px";if(!Y.transformSupported)return e?"top:"+n+";left:"+i+";":[["top",n],["left",i]];var a=Y.transform3dSupported,o="translate"+(a?"3d":"")+"("+i+","+n+(a?",0":"")+")";return e?"top:0;left:0;"+wf+":"+o+";":[["top",0],["left",0],[Zy,o]]}function VM(r){var t=[],e=r.get("fontSize"),i=r.getTextColor();i&&t.push("color:"+i),t.push("font:"+r.getFont());var n=Z(r.get("lineHeight"),Math.round(e*3/2));e&&t.push("line-height:"+n+"px");var a=r.get("textShadowColor"),o=r.get("textShadowBlur")||0,s=r.get("textShadowOffsetX")||0,l=r.get("textShadowOffsetY")||0;return a&&o&&t.push("text-shadow:"+s+"px "+l+"px "+o+"px "+a),A(["decoration","align"],function(u){var f=r.get(u);f&&t.push("text-"+u+":"+f)}),t.join(";")}function WM(r,t,e){var i=[],n=r.get("transitionDuration"),a=r.get("backgroundColor"),o=r.get("shadowBlur"),s=r.get("shadowColor"),l=r.get("shadowOffsetX"),u=r.get("shadowOffsetY"),f=r.getModel("textStyle"),h=wg(r,"html"),c=l+"px "+u+"px "+o+"px "+s;return i.push("box-shadow:"+c),t&&n&&i.push(GM(n,e)),a&&i.push("background-color:"+a),A(["width","color","radius"],function(v){var d="border-"+v,y=Zp(d),p=r.get(y);p!=null&&i.push(d+":"+p+(v==="color"?"":"px"))}),i.push(VM(f)),h!=null&&i.push("padding:"+ko(h).join("px ")+"px"),i.join(";")+";"}function Uc(r,t,e,i,n){var a=t&&t.painter;if(e){var o=a&&a.getViewportRoot();o&&Vm(r,o,e,i,n)}else{r[0]=i,r[1]=n;var s=a&&a.getViewportRootOffset();s&&(r[0]+=s.offsetLeft,r[1]+=s.offsetTop)}r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var UM=function(){function r(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,Y.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var n=this._zr=t.getZr(),a=e.appendTo,o=a&&(z(a)?document.querySelector(a):xn(a)?a:$(a)&&a(t.getDom()));Uc(this._styleCoord,n,o,t.getWidth()/2,t.getHeight()/2),(o||t.getDom()).appendChild(i),this._api=t,this._container=o;var s=this;i.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},i.onmousemove=function(l){if(l=l||window.event,!s._enterable){var u=n.handler,f=n.painter.getViewportRoot();Kt(f,l,!0),u.dispatch("mousemove",l)}},i.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return r.prototype.update=function(t){if(!this._container){var e=this._api.getDom(),i=BM(e,"position"),n=e.style;n.position!=="absolute"&&i!=="absolute"&&(n.position="relative")}var a=t.get("alwaysShowContent");a&&this._moveIfResized(),this._alwaysShowContent=a,this.el.className=t.get("className")||""},r.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var i=this.el,n=i.style,a=this._styleCoord;i.innerHTML?n.cssText=FM+WM(t,!this._firstShow,this._longHide)+Wc(a[0],a[1],!0)+("border-color:"+Ur(e)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):n.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(t,e,i,n,a){var o=this.el;if(t==null){o.innerHTML="";return}var s="";if(z(a)&&i.get("trigger")==="item"&&!Yy(i)&&(s=HM(i,n,a)),z(t))o.innerHTML=t+s;else if(t){o.innerHTML="",N(t)||(t=[t]);for(var l=0;l<t.length;l++)xn(t[l])&&t[l].parentNode!==o&&o.appendChild(t[l]);if(s&&o.childNodes.length){var u=document.createElement("div");u.innerHTML=s,o.appendChild(u)}}},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},r.prototype.moveTo=function(t,e){if(this.el){var i=this._styleCoord;if(Uc(i,this._zr,this._container,t,e),i[0]!=null&&i[1]!=null){var n=this.el.style,a=Wc(i[0],i[1]);A(a,function(o){n[o[0]]=o[1]})}}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",Y.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ft(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},r}();const $M=UM;var YM=function(){function r(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),Yc(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return r.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized(),this._alwaysShowContent=e},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(t,e,i,n,a){var o=this;H(t)&&Ft(""),this.el&&this._zr.remove(this.el);var s=i.getModel("textStyle");this.el=new kt({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:n,textShadowColor:s.get("textShadowColor"),fill:i.get(["textStyle","color"]),padding:wg(i,"richText"),verticalAlign:"top",align:"left"},z:i.get("z")}),A(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(u){o.el.style[u]=i.get(u)}),A(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(u){o.el.style[u]=s.get(u)||0}),this._zr.add(this.el);var l=this;this.el.on("mouseover",function(){l._enterable&&(clearTimeout(l._hideTimeout),l._show=!0),l._inContent=!0}),this.el.on("mouseout",function(){l._enterable&&l._show&&l.hideLater(l._hideDelay),l._inContent=!1})},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),i=$c(t.style);return[e.width+i.left+i.right,e.height+i.top+i.bottom]},r.prototype.moveTo=function(t,e){var i=this.el;if(i){var n=this._styleCoord;Yc(n,this._zr,t,e),t=n[0],e=n[1];var a=i.style,o=Ke(a.borderWidth||0),s=$c(a);i.x=t+o+s.left,i.y=e+o+s.top,i.markRedraw()}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ft(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function Ke(r){return Math.max(0,r)}function $c(r){var t=Ke(r.shadowBlur||0),e=Ke(r.shadowOffsetX||0),i=Ke(r.shadowOffsetY||0);return{left:Ke(t-e),right:Ke(t+e),top:Ke(t-i),bottom:Ke(t+i)}}function Yc(r,t,e,i){r[0]=e,r[1]=i,r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}const XM=YM;var ZM=new bt({shape:{x:-1,y:-1,width:2,height:2}}),qM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.init=function(e,i){if(!(Y.node||!i.getDom())){var n=e.getComponent("tooltip"),a=this._renderMode=x_(n.get("renderMode"));this._tooltipContent=a==="richText"?new XM(i):new $M(i,{appendTo:n.get("appendToBody",!0)?"body":n.get("appendTo",!0)})}},t.prototype.render=function(e,i,n){if(!(Y.node||!n.getDom())){this.group.removeAll(),this._tooltipModel=e,this._ecModel=i,this._api=n;var a=this._tooltipContent;a.update(e),a.setEnterable(e.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&e.get("transitionDuration")?xg(this,"_updatePosition",50,"fixRate"):Wl(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var e=this._tooltipModel,i=e.get("triggerOn");Wy("itemTooltip",this._api,ft(function(n,a,o){i!=="none"&&(i.indexOf(n)>=0?this._tryShow(a,o):n==="leave"&&this._hide(o))},this))},t.prototype._keepShow=function(){var e=this._tooltipModel,i=this._ecModel,n=this._api,a=e.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&a!=="none"&&a!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&o.manuallyShowTip(e,i,n,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(e,i,n,a){if(!(a.from===this.uid||Y.node||!n.getDom())){var o=Xc(a,n);this._ticket="";var s=a.dataByCoordSys,l=jM(a,i,n);if(l){var u=l.el.getBoundingRect().clone();u.applyTransform(l.el.transform),this._tryShow({offsetX:u.x+u.width/2,offsetY:u.y+u.height/2,target:l.el,position:a.position,positionDefault:"bottom"},o)}else if(a.tooltip&&a.x!=null&&a.y!=null){var f=ZM;f.x=a.x,f.y=a.y,f.update(),rt(f).tooltipConfig={name:null,option:a.tooltip},this._tryShow({offsetX:a.x,offsetY:a.y,target:f},o)}else if(s)this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,dataByCoordSys:s,tooltipOption:a.tooltipOption},o);else if(a.seriesIndex!=null){if(this._manuallyAxisShowTip(e,i,n,a))return;var h=Uy(a,i),c=h.point[0],v=h.point[1];c!=null&&v!=null&&this._tryShow({offsetX:c,offsetY:v,target:h.el,position:a.position,positionDefault:"bottom"},o)}else a.x!=null&&a.y!=null&&(n.dispatchAction({type:"updateAxisPointer",x:a.x,y:a.y}),this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,target:n.getZr().findHover(a.x,a.y).target},o))}},t.prototype.manuallyHideTip=function(e,i,n,a){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,a.from!==this.uid&&this._hide(Xc(a,n))},t.prototype._manuallyAxisShowTip=function(e,i,n,a){var o=a.seriesIndex,s=a.dataIndex,l=i.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||l==null)){var u=i.getSeriesByIndex(o);if(u){var f=u.getData(),h=ji([f.getItemModel(s),u,(u.coordinateSystem||{}).model],this._tooltipModel);if(h.get("trigger")==="axis")return n.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:a.position}),!0}}},t.prototype._tryShow=function(e,i){var n=e.target,a=this._tooltipModel;if(a){this._lastX=e.offsetX,this._lastY=e.offsetY;var o=e.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,e);else if(n){var s=rt(n);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var l,u;un(n,function(f){if(rt(f).dataIndex!=null)return l=f,!0;if(rt(f).tooltipConfig!=null)return u=f,!0},!0),l?this._showSeriesItemTooltip(e,l,i):u?this._showComponentItemTooltip(e,u,i):this._hide(i)}else this._lastDataByCoordSys=null,this._hide(i)}},t.prototype._showOrMove=function(e,i){var n=e.get("showDelay");i=ft(i,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(i,n):i()},t.prototype._showAxisTooltip=function(e,i){var n=this._ecModel,a=this._tooltipModel,o=[i.offsetX,i.offsetY],s=ji([i.tooltipOption],a),l=this._renderMode,u=[],f=En("section",{blocks:[],noHeader:!0}),h=[],c=new Ns;A(e,function(m){A(m.dataByAxis,function(_){var S=n.getComponent(_.axisDim+"Axis",_.axisIndex),b=_.value;if(!(!S||b==null)){var w=Gy(b,S.axis,n,_.seriesDataIndices,_.valueLabelOpt),x=En("section",{header:w,noHeader:!_e(w),sortBlocks:!0,blocks:[]});f.blocks.push(x),A(_.seriesDataIndices,function(C){var T=n.getSeriesByIndex(C.seriesIndex),D=C.dataIndexInside,M=T.getDataParams(D);if(!(M.dataIndex<0)){M.axisDim=_.axisDim,M.axisIndex=_.axisIndex,M.axisType=_.axisType,M.axisId=_.axisId,M.axisValue=gf(S.axis,{value:b}),M.axisValueLabel=w,M.marker=c.makeTooltipMarker("item",Ur(M.color),l);var L=yv(T.formatTooltip(D,!0,null)),I=L.frag;if(I){var P=ji([T],a).get("valueFormatter");x.blocks.push(P?k({valueFormatter:P},I):I)}L.text&&h.push(L.text),u.push(M)}})}})}),f.blocks.reverse(),h.reverse();var v=i.position,d=s.get("order"),y=bv(f,c,l,d,n.get("useUTC"),s.get("textStyle"));y&&h.unshift(y);var p=l==="richText"?`

`:"<br/>",g=h.join(p);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(e,u)?this._updatePosition(s,v,o[0],o[1],this._tooltipContent,u):this._showTooltipContent(s,g,u,Math.random()+"",o[0],o[1],v,null,c)})},t.prototype._showSeriesItemTooltip=function(e,i,n){var a=this._ecModel,o=rt(i),s=o.seriesIndex,l=a.getSeriesByIndex(s),u=o.dataModel||l,f=o.dataIndex,h=o.dataType,c=u.getData(h),v=this._renderMode,d=e.positionDefault,y=ji([c.getItemModel(f),u,l&&(l.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),p=y.get("trigger");if(!(p!=null&&p!=="item")){var g=u.getDataParams(f,h),m=new Ns;g.marker=m.makeTooltipMarker("item",Ur(g.color),v);var _=yv(u.formatTooltip(f,!1,h)),S=y.get("order"),b=y.get("valueFormatter"),w=_.frag,x=w?bv(b?k({valueFormatter:b},w):w,m,v,S,a.get("useUTC"),y.get("textStyle")):_.text,C="item_"+u.name+"_"+f;this._showOrMove(y,function(){this._showTooltipContent(y,x,g,C,e.offsetX,e.offsetY,e.position,e.target,m)}),n({type:"showTip",dataIndexInside:f,dataIndex:c.getRawIndex(f),seriesIndex:s,from:this.uid})}},t.prototype._showComponentItemTooltip=function(e,i,n){var a=this._renderMode==="html",o=rt(i),s=o.tooltipConfig,l=s.option||{},u=l.encodeHTMLContent;if(z(l)){var f=l;l={content:f,formatter:f},u=!0}u&&a&&l.content&&(l=J(l),l.content=Nt(l.content));var h=[l],c=this._ecModel.getComponent(o.componentMainType,o.componentIndex);c&&h.push(c),h.push({formatter:l.content});var v=e.positionDefault,d=ji(h,this._tooltipModel,v?{position:v}:null),y=d.get("content"),p=Math.random()+"",g=new Ns;this._showOrMove(d,function(){var m=J(d.get("formatterParams")||{});this._showTooltipContent(d,y,m,p,e.offsetX,e.offsetY,e.position,i,g)}),n({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(e,i,n,a,o,s,l,u,f){if(this._ticket="",!(!e.get("showContent")||!e.get("show"))){var h=this._tooltipContent;h.setEnterable(e.get("enterable"));var c=e.get("formatter");l=l||e.get("position");var v=i,d=this._getNearestPoint([o,s],n,e.get("trigger"),e.get("borderColor")),y=d.color;if(c)if(z(c)){var p=e.ecModel.get("useUTC"),g=N(n)?n[0]:n,m=g&&g.axisType&&g.axisType.indexOf("time")>=0;v=c,m&&(v=Lo(g.axisValue,v,p)),v=qp(v,n,!0)}else if($(c)){var _=ft(function(S,b){S===this._ticket&&(h.setContent(b,f,e,y,l),this._updatePosition(e,l,o,s,h,n,u))},this);this._ticket=a,v=c(n,a,_)}else v=c;h.setContent(v,f,e,y,l),h.show(e,y),this._updatePosition(e,l,o,s,h,n,u)}},t.prototype._getNearestPoint=function(e,i,n,a){if(n==="axis"||N(i))return{color:a||(this._renderMode==="html"?"#fff":"none")};if(!N(i))return{color:a||i.color||i.borderColor}},t.prototype._updatePosition=function(e,i,n,a,o,s,l){var u=this._api.getWidth(),f=this._api.getHeight();i=i||e.get("position");var h=o.getSize(),c=e.get("align"),v=e.get("verticalAlign"),d=l&&l.getBoundingRect().clone();if(l&&d.applyTransform(l.transform),$(i)&&(i=i([n,a],s,o.el,d,{viewSize:[u,f],contentSize:h.slice()})),N(i))n=Pt(i[0],u),a=Pt(i[1],f);else if(H(i)){var y=i;y.width=h[0],y.height=h[1];var p=In(y,{width:u,height:f});n=p.x,a=p.y,c=null,v=null}else if(z(i)&&l){var g=JM(i,d,h,e.get("borderWidth"));n=g[0],a=g[1]}else{var g=KM(n,a,o,u,f,c?null:20,v?null:20);n=g[0],a=g[1]}if(c&&(n-=Zc(c)?h[0]/2:c==="right"?h[0]:0),v&&(a-=Zc(v)?h[1]/2:v==="bottom"?h[1]:0),Yy(e)){var g=QM(n,a,o,u,f);n=g[0],a=g[1]}o.moveTo(n,a)},t.prototype._updateContentNotChangedOnAxis=function(e,i){var n=this._lastDataByCoordSys,a=this._cbParamsList,o=!!n&&n.length===e.length;return o&&A(n,function(s,l){var u=s.dataByAxis||[],f=e[l]||{},h=f.dataByAxis||[];o=o&&u.length===h.length,o&&A(u,function(c,v){var d=h[v]||{},y=c.seriesDataIndices||[],p=d.seriesDataIndices||[];o=o&&c.value===d.value&&c.axisType===d.axisType&&c.axisId===d.axisId&&y.length===p.length,o&&A(y,function(g,m){var _=p[m];o=o&&g.seriesIndex===_.seriesIndex&&g.dataIndex===_.dataIndex}),a&&A(c.seriesDataIndices,function(g){var m=g.seriesIndex,_=i[m],S=a[m];_&&S&&S.data!==_.data&&(o=!1)})})}),this._lastDataByCoordSys=e,this._cbParamsList=i,!!o},t.prototype._hide=function(e){this._lastDataByCoordSys=null,e({type:"hideTip",from:this.uid})},t.prototype.dispose=function(e,i){Y.node||!i.getDom()||(Wl(this,"_updatePosition"),this._tooltipContent.dispose(),ou("itemTooltip",i))},t.type="tooltip",t}(Be);function ji(r,t,e){var i=t.ecModel,n;e?(n=new At(e,i,i),n=new At(t.option,n,i)):n=t;for(var a=r.length-1;a>=0;a--){var o=r[a];o&&(o instanceof At&&(o=o.get("tooltip",!0)),z(o)&&(o={formatter:o}),o&&(n=new At(o,n,i)))}return n}function Xc(r,t){return r.dispatchAction||ft(t.dispatchAction,t)}function KM(r,t,e,i,n,a,o){var s=e.getSize(),l=s[0],u=s[1];return a!=null&&(r+l+a+2>i?r-=l+a:r+=a),o!=null&&(t+u+o>n?t-=u+o:t+=o),[r,t]}function QM(r,t,e,i,n){var a=e.getSize(),o=a[0],s=a[1];return r=Math.min(r+o,i)-o,t=Math.min(t+s,n)-s,r=Math.max(r,0),t=Math.max(t,0),[r,t]}function JM(r,t,e,i){var n=e[0],a=e[1],o=Math.ceil(Math.SQRT2*i)+8,s=0,l=0,u=t.width,f=t.height;switch(r){case"inside":s=t.x+u/2-n/2,l=t.y+f/2-a/2;break;case"top":s=t.x+u/2-n/2,l=t.y-a-o;break;case"bottom":s=t.x+u/2-n/2,l=t.y+f+o;break;case"left":s=t.x-n-o,l=t.y+f/2-a/2;break;case"right":s=t.x+u+o,l=t.y+f/2-a/2}return[s,l]}function Zc(r){return r==="center"||r==="middle"}function jM(r,t,e){var i=Mu(r).queryOptionMap,n=i.keys()[0];if(!(!n||n==="series")){var a=Vn(t,n,i.get(n),{useDefault:!1,enableAll:!1,enableNone:!1}),o=a.models[0];if(o){var s=e.getViewOfComponentModel(o),l;if(s.group.traverse(function(u){var f=rt(u).tooltipConfig;if(f&&f.name===r.name)return l=u,!0}),l)return{componentMainType:n,componentIndex:o.componentIndex,el:l}}}}const tA=qM;function WA(r){$r($y),r.registerComponentModel(kM),r.registerComponentView(tA),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Ht),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Ht)}var eA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.type="title",t.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},t}(ut),rA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){if(this.group.removeAll(),!!e.get("show")){var a=this.group,o=e.getModel("textStyle"),s=e.getModel("subtextStyle"),l=e.get("textAlign"),u=Z(e.get("textBaseline"),e.get("textVerticalAlign")),f=new kt({style:ar(o,{text:e.get("text"),fill:o.getTextColor()},{disableBox:!0}),z2:10}),h=f.getBoundingRect(),c=e.get("subtext"),v=new kt({style:ar(s,{text:c,fill:s.getTextColor(),y:h.height+e.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),d=e.get("link"),y=e.get("sublink"),p=e.get("triggerEvent",!0);f.silent=!d&&!p,v.silent=!y&&!p,d&&f.on("click",function(){jh(d,"_"+e.get("target"))}),y&&v.on("click",function(){jh(y,"_"+e.get("subtarget"))}),rt(f).eventData=rt(v).eventData=p?{componentType:"title",componentIndex:e.componentIndex}:null,a.add(f),c&&a.add(v);var g=a.getBoundingRect(),m=e.getBoxLayoutParams();m.width=g.width,m.height=g.height;var _=In(m,{width:n.getWidth(),height:n.getHeight()},e.get("padding"));l||(l=e.get("left")||e.get("right"),l==="middle"&&(l="center"),l==="right"?_.x+=_.width:l==="center"&&(_.x+=_.width/2)),u||(u=e.get("top")||e.get("bottom"),u==="center"&&(u="middle"),u==="bottom"?_.y+=_.height:u==="middle"&&(_.y+=_.height/2),u=u||"top"),a.x=_.x,a.y=_.y,a.markRedraw();var S={align:l,verticalAlign:u};f.setStyle(S),v.setStyle(S),g=a.getBoundingRect();var b=_.margin,w=e.getItemStyle(["color","opacity"]);w.fill=e.get("backgroundColor");var x=new bt({shape:{x:g.x-b[3],y:g.y-b[0],width:g.width+b[1]+b[3],height:g.height+b[0]+b[2],r:e.get("borderRadius")},style:w,subPixelOptimize:!0,silent:!0});a.add(x)}},t.type="title",t}(Be);function UA(r){r.registerComponentModel(eA),r.registerComponentView(rA)}var iA=function(r,t){if(t==="all")return{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])};if(t==="inverse")return{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}},nA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n),e.selected=e.selected||{},this._updateSelector(e)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),this._updateSelector(e)},t.prototype._updateSelector=function(e){var i=e.selector,n=this.ecModel;i===!0&&(i=e.selector=["all","inverse"]),N(i)&&A(i,function(a,o){z(a)&&(a={type:a}),i[o]=tt(a,iA(n,a.type))})},t.prototype.optionUpdated=function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&this.get("selectedMode")==="single"){for(var i=!1,n=0;n<e.length;n++){var a=e[n].get("name");if(this.isSelected(a)){this.select(a),i=!0;break}}!i&&this.select(e[0].get("name"))}},t.prototype._updateData=function(e){var i=[],n=[];e.eachRawSeries(function(l){var u=l.name;n.push(u);var f;if(l.legendVisualProvider){var h=l.legendVisualProvider,c=h.getAllNames();e.isSeriesFiltered(l)||(n=n.concat(c)),c.length?i=i.concat(c):f=!0}else f=!0;f&&Du(l)&&i.push(l.name)}),this._availableNames=n;var a=this.get("data")||i,o=X(),s=V(a,function(l){return(z(l)||vt(l))&&(l={name:l}),o.get(l.name)?null:(o.set(l.name,!0),new At(l,this,this.ecModel))},this);this._data=wt(s,function(l){return!!l})},t.prototype.getData=function(){return this._data},t.prototype.select=function(e){var i=this.option.selected,n=this.get("selectedMode");if(n==="single"){var a=this._data;A(a,function(o){i[o.get("name")]=!1})}i[e]=!0},t.prototype.unSelect=function(e){this.get("selectedMode")!=="single"&&(this.option.selected[e]=!1)},t.prototype.toggleSelected=function(e){var i=this.option.selected;i.hasOwnProperty(e)||(i[e]=!0),this[i[e]?"unSelect":"select"](e)},t.prototype.allSelect=function(){var e=this._data,i=this.option.selected;A(e,function(n){i[n.get("name",!0)]=!0})},t.prototype.inverseSelect=function(){var e=this._data,i=this.option.selected;A(e,function(n){var a=n.get("name",!0);i.hasOwnProperty(a)||(i[a]=!0),i[a]=!i[a]})},t.prototype.isSelected=function(e){var i=this.option.selected;return!(i.hasOwnProperty(e)&&!i[e])&&at(this._availableNames,e)>=0},t.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},t.type="legend.plain",t.dependencies=["series"],t.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},t}(ut);const su=nA;var fi=mt,lu=A,Aa=Et,aA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!1,e}return t.prototype.init=function(){this.group.add(this._contentGroup=new Aa),this.group.add(this._selectorGroup=new Aa),this._isFirstRender=!0},t.prototype.getContentGroup=function(){return this._contentGroup},t.prototype.getSelectorGroup=function(){return this._selectorGroup},t.prototype.render=function(e,i,n){var a=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!e.get("show",!0)){var o=e.get("align"),s=e.get("orient");(!o||o==="auto")&&(o=e.get("left")==="right"&&s==="vertical"?"right":"left");var l=e.get("selector",!0),u=e.get("selectorPosition",!0);l&&(!u||u==="auto")&&(u=s==="horizontal"?"end":"start"),this.renderInner(o,e,i,n,l,s,u);var f=e.getBoxLayoutParams(),h={width:n.getWidth(),height:n.getHeight()},c=e.get("padding"),v=In(f,h,c),d=this.layoutInner(e,o,v,a,l,u),y=In(it({width:d.width,height:d.height},f),h,c);this.group.x=y.x-d.x,this.group.y=y.y-d.y,this.group.markRedraw(),this.group.add(this._backgroundEl=RM(d,e))}},t.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},t.prototype.renderInner=function(e,i,n,a,o,s,l){var u=this.getContentGroup(),f=X(),h=i.get("selectedMode"),c=[];n.eachRawSeries(function(v){!v.get("legendHoverLink")&&c.push(v.id)}),lu(i.getData(),function(v,d){var y=v.get("name");if(!this.newlineDisabled&&(y===""||y===`
`)){var p=new Aa;p.newline=!0,u.add(p);return}var g=n.getSeriesByName(y)[0];if(!f.get(y))if(g){var m=g.getData(),_=m.getVisual("legendLineStyle")||{},S=m.getVisual("legendIcon"),b=m.getVisual("style"),w=this._createItem(g,y,d,v,i,e,_,b,S,h,a);w.on("click",fi(qc,y,null,a,c)).on("mouseover",fi(uu,g.name,null,a,c)).on("mouseout",fi(fu,g.name,null,a,c)),n.ssr&&w.eachChild(function(x){var C=rt(x);C.seriesIndex=g.seriesIndex,C.dataIndex=d,C.ssrType="legend"}),f.set(y,!0)}else n.eachRawSeries(function(x){if(!f.get(y)&&x.legendVisualProvider){var C=x.legendVisualProvider;if(!C.containName(y))return;var T=C.indexOfName(y),D=C.getItemVisual(T,"style"),M=C.getItemVisual(T,"legendIcon"),L=Re(D.fill);L&&L[3]===0&&(L[3]=.2,D=k(k({},D),{fill:_o(L,"rgba")}));var I=this._createItem(x,y,d,v,i,e,{},D,M,h,a);I.on("click",fi(qc,null,y,a,c)).on("mouseover",fi(uu,null,y,a,c)).on("mouseout",fi(fu,null,y,a,c)),n.ssr&&I.eachChild(function(P){var R=rt(P);R.seriesIndex=x.seriesIndex,R.dataIndex=d,R.ssrType="legend"}),f.set(y,!0)}},this)},this),o&&this._createSelector(o,i,a,s,l)},t.prototype._createSelector=function(e,i,n,a,o){var s=this.getSelectorGroup();lu(e,function(u){var f=u.type,h=new kt({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:f==="all"?"legendAllSelect":"legendInverseSelect",legendId:i.id})}});s.add(h);var c=i.getModel("selectorLabel"),v=i.getModel(["emphasis","selectorLabel"]);Gu(h,{normal:c,emphasis:v},{defaultText:u.title}),Pl(h)})},t.prototype._createItem=function(e,i,n,a,o,s,l,u,f,h,c){var v=e.visualDrawType,d=o.get("itemWidth"),y=o.get("itemHeight"),p=o.isSelected(i),g=a.get("symbolRotate"),m=a.get("symbolKeepAspect"),_=a.get("icon");f=_||f||"roundRect";var S=oA(f,a,l,u,v,p,c),b=new Aa,w=a.getModel("textStyle");if($(e.getLegendIcon)&&(!_||_==="inherit"))b.add(e.getLegendIcon({itemWidth:d,itemHeight:y,icon:f,iconRotate:g,itemStyle:S.itemStyle,lineStyle:S.lineStyle,symbolKeepAspect:m}));else{var x=_==="inherit"&&e.getData().getVisual("symbol")?g==="inherit"?e.getData().getVisual("symbolRotate"):g:0;b.add(sA({itemWidth:d,itemHeight:y,icon:f,iconRotate:x,itemStyle:S.itemStyle,lineStyle:S.lineStyle,symbolKeepAspect:m}))}var C=s==="left"?d+5:-5,T=s,D=o.get("formatter"),M=i;z(D)&&D?M=D.replace("{name}",i??""):$(D)&&(M=D(i));var L=p?w.getTextColor():a.get("inactiveColor");b.add(new kt({style:ar(w,{text:M,x:C,y:y/2,fill:L,align:T,verticalAlign:"middle"},{inheritColor:L})}));var I=new bt({shape:b.getBoundingRect(),style:{fill:"transparent"}}),P=a.getModel("tooltip");return P.get("show")&&Do({el:I,componentModel:o,itemName:i,itemTooltipOption:P.option}),b.add(I),b.eachChild(function(R){R.silent=!0}),I.silent=!h,this.getContentGroup().add(b),Pl(b),b.__legendDataIndex=n,b},t.prototype.layoutInner=function(e,i,n,a,o,s){var l=this.getContentGroup(),u=this.getSelectorGroup();mn(e.get("orient"),l,e.get("itemGap"),n.width,n.height);var f=l.getBoundingRect(),h=[-f.x,-f.y];if(u.markRedraw(),l.markRedraw(),o){mn("horizontal",u,e.get("selectorItemGap",!0));var c=u.getBoundingRect(),v=[-c.x,-c.y],d=e.get("selectorButtonGap",!0),y=e.getOrient().index,p=y===0?"width":"height",g=y===0?"height":"width",m=y===0?"y":"x";s==="end"?v[y]+=f[p]+d:h[y]+=c[p]+d,v[1-y]+=f[g]/2-c[g]/2,u.x=v[0],u.y=v[1],l.x=h[0],l.y=h[1];var _={x:0,y:0};return _[p]=f[p]+d+c[p],_[g]=Math.max(f[g],c[g]),_[m]=Math.min(0,c[m]+v[1-y]),_}else return l.x=h[0],l.y=h[1],this.group.getBoundingRect()},t.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},t.type="legend.plain",t}(Be);function oA(r,t,e,i,n,a,o){function s(p,g){p.lineWidth==="auto"&&(p.lineWidth=g.lineWidth>0?2:0),lu(p,function(m,_){p[_]==="inherit"&&(p[_]=g[_])})}var l=t.getModel("itemStyle"),u=l.getItemStyle(),f=r.lastIndexOf("empty",0)===0?"fill":"stroke",h=l.getShallow("decal");u.decal=!h||h==="inherit"?i.decal:Zl(h,o),u.fill==="inherit"&&(u.fill=i[n]),u.stroke==="inherit"&&(u.stroke=i[f]),u.opacity==="inherit"&&(u.opacity=(n==="fill"?i:e).opacity),s(u,i);var c=t.getModel("lineStyle"),v=c.getLineStyle();if(s(v,e),u.fill==="auto"&&(u.fill=i.fill),u.stroke==="auto"&&(u.stroke=i.fill),v.stroke==="auto"&&(v.stroke=i.fill),!a){var d=t.get("inactiveBorderWidth"),y=u[f];u.lineWidth=d==="auto"?i.lineWidth>0&&y?2:0:u.lineWidth,u.fill=t.get("inactiveColor"),u.stroke=t.get("inactiveBorderColor"),v.stroke=c.get("inactiveColor"),v.lineWidth=c.get("inactiveWidth")}return{itemStyle:u,lineStyle:v}}function sA(r){var t=r.icon||"roundRect",e=Mi(t,0,0,r.itemWidth,r.itemHeight,r.itemStyle.fill,r.symbolKeepAspect);return e.setStyle(r.itemStyle),e.rotation=(r.iconRotate||0)*Math.PI/180,e.setOrigin([r.itemWidth/2,r.itemHeight/2]),t.indexOf("empty")>-1&&(e.style.stroke=e.style.fill,e.style.fill="#fff",e.style.lineWidth=2),e}function qc(r,t,e,i){fu(r,t,e,i),e.dispatchAction({type:"legendToggleSelect",name:r??t}),uu(r,t,e,i)}function Ky(r){for(var t=r.getZr().storage.getDisplayList(),e,i=0,n=t.length;i<n&&!(e=t[i].states.emphasis);)i++;return e&&e.hoverLayer}function uu(r,t,e,i){Ky(e)||e.dispatchAction({type:"highlight",seriesName:r,name:t,excludeSeriesId:i})}function fu(r,t,e,i){Ky(e)||e.dispatchAction({type:"downplay",seriesName:r,name:t,excludeSeriesId:i})}const Qy=aA;function lA(r){var t=r.findComponents({mainType:"legend"});t&&t.length&&r.filterSeries(function(e){for(var i=0;i<t.length;i++)if(!t[i].isSelected(e.name))return!1;return!0})}function tn(r,t,e){var i=r==="allSelect"||r==="inverseSelect",n={},a=[];e.eachComponent({mainType:"legend",query:t},function(s){i?s[r]():s[r](t.name),Kc(s,n),a.push(s.componentIndex)});var o={};return e.eachComponent("legend",function(s){A(n,function(l,u){s[l?"select":"unSelect"](u)}),Kc(s,o)}),i?{selected:o,legendIndex:a}:{name:t.name,selected:o}}function Kc(r,t){var e=t||{};return A(r.getData(),function(i){var n=i.get("name");if(!(n===`
`||n==="")){var a=r.isSelected(n);zr(e,n)?e[n]=e[n]&&a:e[n]=a}}),e}function uA(r){r.registerAction("legendToggleSelect","legendselectchanged",mt(tn,"toggleSelected")),r.registerAction("legendAllSelect","legendselectall",mt(tn,"allSelect")),r.registerAction("legendInverseSelect","legendinverseselect",mt(tn,"inverseSelect")),r.registerAction("legendSelect","legendselected",mt(tn,"select")),r.registerAction("legendUnSelect","legendunselected",mt(tn,"unSelect"))}function Jy(r){r.registerComponentModel(su),r.registerComponentView(Qy),r.registerProcessor(r.PRIORITY.PROCESSOR.SERIES_FILTER,lA),r.registerSubTypeDefaulter("legend",function(){return"plain"}),uA(r)}var fA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.setScrollDataIndex=function(e){this.option.scrollDataIndex=e},t.prototype.init=function(e,i,n){var a=Oo(e);r.prototype.init.call(this,e,i,n),Qc(this,e,a)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),Qc(this,this.option,e)},t.type="legend.scroll",t.defaultOption=aw(su.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),t}(su);function Qc(r,t,e){var i=r.getOrient(),n=[1,1];n[i.index]=0,Ci(t,e,{type:"box",ignoreSize:!!n})}const hA=fA;var Jc=Et,nl=["width","height"],al=["x","y"],vA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!0,e._currentIndex=0,e}return t.prototype.init=function(){r.prototype.init.call(this),this.group.add(this._containerGroup=new Jc),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Jc)},t.prototype.resetInner=function(){r.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},t.prototype.renderInner=function(e,i,n,a,o,s,l){var u=this;r.prototype.renderInner.call(this,e,i,n,a,o,s,l);var f=this._controllerGroup,h=i.get("pageIconSize",!0),c=N(h)?h:[h,h];d("pagePrev",0);var v=i.getModel("pageTextStyle");f.add(new kt({name:"pageText",style:{text:"xx/xx",fill:v.getTextColor(),font:v.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1);function d(y,p){var g=y+"DataIndex",m=zu(i.get("pageIcons",!0)[i.getOrient().name][p],{onclick:ft(u._pageGo,u,g,i,a)},{x:-c[0]/2,y:-c[1]/2,width:c[0],height:c[1]});m.name=y,f.add(m)}},t.prototype.layoutInner=function(e,i,n,a,o,s){var l=this.getSelectorGroup(),u=e.getOrient().index,f=nl[u],h=al[u],c=nl[1-u],v=al[1-u];o&&mn("horizontal",l,e.get("selectorItemGap",!0));var d=e.get("selectorButtonGap",!0),y=l.getBoundingRect(),p=[-y.x,-y.y],g=J(n);o&&(g[f]=n[f]-y[f]-d);var m=this._layoutContentAndController(e,a,g,u,f,c,v,h);if(o){if(s==="end")p[u]+=m[f]+d;else{var _=y[f]+d;p[u]-=_,m[h]-=_}m[f]+=y[f]+d,p[1-u]+=m[v]+m[c]/2-y[c]/2,m[c]=Math.max(m[c],y[c]),m[v]=Math.min(m[v],y[v]+p[1-u]),l.x=p[0],l.y=p[1],l.markRedraw()}return m},t.prototype._layoutContentAndController=function(e,i,n,a,o,s,l,u){var f=this.getContentGroup(),h=this._containerGroup,c=this._controllerGroup;mn(e.get("orient"),f,e.get("itemGap"),a?n.width:null,a?null:n.height),mn("horizontal",c,e.get("pageButtonItemGap",!0));var v=f.getBoundingRect(),d=c.getBoundingRect(),y=this._showController=v[o]>n[o],p=[-v.x,-v.y];i||(p[a]=f[u]);var g=[0,0],m=[-d.x,-d.y],_=Z(e.get("pageButtonGap",!0),e.get("itemGap",!0));if(y){var S=e.get("pageButtonPosition",!0);S==="end"?m[a]+=n[o]-d[o]:g[a]+=d[o]+_}m[1-a]+=v[s]/2-d[s]/2,f.setPosition(p),h.setPosition(g),c.setPosition(m);var b={x:0,y:0};if(b[o]=y?n[o]:v[o],b[s]=Math.max(v[s],d[s]),b[l]=Math.min(0,d[l]+m[1-a]),h.__rectSize=n[o],y){var w={x:0,y:0};w[o]=Math.max(n[o]-d[o]-_,0),w[s]=b[s],h.setClipPath(new bt({shape:w})),h.__rectSize=w[o]}else c.eachChild(function(C){C.attr({invisible:!0,silent:!0})});var x=this._getPageInfo(e);return x.pageIndex!=null&&nr(f,{x:x.contentPosition[0],y:x.contentPosition[1]},y?e:null),this._updatePageInfoView(e,x),b},t.prototype._pageGo=function(e,i,n){var a=this._getPageInfo(i)[e];a!=null&&n.dispatchAction({type:"legendScroll",scrollDataIndex:a,legendId:i.id})},t.prototype._updatePageInfoView=function(e,i){var n=this._controllerGroup;A(["pagePrev","pageNext"],function(f){var h=f+"DataIndex",c=i[h]!=null,v=n.childOfName(f);v&&(v.setStyle("fill",c?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),v.cursor=c?"pointer":"default")});var a=n.childOfName("pageText"),o=e.get("pageFormatter"),s=i.pageIndex,l=s!=null?s+1:0,u=i.pageCount;a&&o&&a.setStyle("text",z(o)?o.replace("{current}",l==null?"":l+"").replace("{total}",u==null?"":u+""):o({current:l,total:u}))},t.prototype._getPageInfo=function(e){var i=e.get("scrollDataIndex",!0),n=this.getContentGroup(),a=this._containerGroup.__rectSize,o=e.getOrient().index,s=nl[o],l=al[o],u=this._findTargetItemIndex(i),f=n.children(),h=f[u],c=f.length,v=c?1:0,d={contentPosition:[n.x,n.y],pageCount:v,pageIndex:v-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return d;var y=S(h);d.contentPosition[o]=-y.s;for(var p=u+1,g=y,m=y,_=null;p<=c;++p)_=S(f[p]),(!_&&m.e>g.s+a||_&&!b(_,g.s))&&(m.i>g.i?g=m:g=_,g&&(d.pageNextDataIndex==null&&(d.pageNextDataIndex=g.i),++d.pageCount)),m=_;for(var p=u-1,g=y,m=y,_=null;p>=-1;--p)_=S(f[p]),(!_||!b(m,_.s))&&g.i<m.i&&(m=g,d.pagePrevDataIndex==null&&(d.pagePrevDataIndex=g.i),++d.pageCount,++d.pageIndex),g=_;return d;function S(w){if(w){var x=w.getBoundingRect(),C=x[l]+w[l];return{s:C,e:C+x[s],i:w.__legendDataIndex}}}function b(w,x){return w.e>=x&&w.s<=x+a}},t.prototype._findTargetItemIndex=function(e){if(!this._showController)return 0;var i,n=this.getContentGroup(),a;return n.eachChild(function(o,s){var l=o.__legendDataIndex;a==null&&l!=null&&(a=s),l===e&&(i=s)}),i??a},t.type="legend.scroll",t}(Qy);const cA=vA;function dA(r){r.registerAction("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;i!=null&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(n){n.setScrollDataIndex(i)})})}function pA(r){$r(Jy),r.registerComponentModel(hA),r.registerComponentView(cA),dA(r)}function $A(r){$r(Jy),$r(pA)}const gA=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function yA(r){function t(i){return(...n)=>{if(!r.value)throw new Error("ECharts is not initialized yet.");return r.value[i].apply(r.value,n)}}function e(){const i=Object.create(null);return gA.forEach(n=>{i[n]=t(n)}),i}return e()}function mA(r,t,e){Ia([e,r,t],([i,n,a],o,s)=>{let l=null;if(i&&n&&a){const{offsetWidth:u,offsetHeight:f}=i,h=a===!0?{}:a,{throttle:c=100,onResize:v}=h;let d=!1;const y=()=>{n.resize(),v==null||v()},p=c?sf(y,c):y;l=new ResizeObserver(()=>{!d&&(d=!0,i.offsetWidth===u&&i.offsetHeight===f)||p()}),l.observe(i)}s(()=>{l&&(l.disconnect(),l=null)})})}const _A={autoresize:[Boolean,Object]},SA=/^on[^a-z]/,jy=r=>SA.test(r);function wA(r){const t={};for(const e in r)jy(e)||(t[e]=r[e]);return t}function Ya(r,t){const e=sm(r)?lm(r):r;return e&&typeof e=="object"&&"value"in e?e.value||t:e||t}const bA="ecLoadingOptions";function xA(r,t,e){const i=La(bA,{}),n=hi(()=>({...Ya(i,{}),...e==null?void 0:e.value}));td(()=>{const a=r.value;a&&(t.value?a.showLoading(n.value):a.hideLoading())})}const TA={loading:Boolean,loadingOptions:Object};let en=null;const tm="x-vue-echarts";function CA(){if(en!=null)return en;if(typeof HTMLElement>"u"||typeof customElements>"u")return en=!1;try{new Function("tag","class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);")(tm)}catch{return en=!1}return en=!0}document.head.appendChild(document.createElement("style")).textContent=`x-vue-echarts{display:block;width:100%;height:100%;min-width:0}
`;const DA=CA(),MA="ecTheme",AA="ecInitOptions",LA="ecUpdateOptions",jc=/(^&?~?!?)native:/;var YA=em({name:"echarts",props:{option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean,..._A,...TA},emits:{},inheritAttrs:!1,setup(r,{attrs:t}){const e=Wo(),i=Wo(),n=Wo(),a=La(MA,null),o=La(AA,null),s=La(LA,null),{autoresize:l,manualUpdate:u,loading:f,loadingOptions:h}=rm(r),c=hi(()=>n.value||r.option||null),v=hi(()=>r.theme||Ya(a,{})),d=hi(()=>r.initOptions||Ya(o,{})),y=hi(()=>r.updateOptions||Ya(s,{})),p=hi(()=>wA(t)),g={},m=im().proxy.$listeners,_={};m?Object.keys(m).forEach(T=>{jc.test(T)?g[T.replace(jc,"$1")]=m[T]:_[T]=m[T]}):Object.keys(t).filter(T=>jy(T)).forEach(T=>{let D=T.charAt(2).toLowerCase()+T.slice(3);if(D.indexOf("native:")===0){const M=`on${D.charAt(7).toUpperCase()}${D.slice(8)}`;g[M]=t[T];return}D.substring(D.length-4)==="Once"&&(D=`~${D.substring(0,D.length-4)}`),_[D]=t[T]});function S(T){if(!e.value)return;const D=i.value=oT(e.value,v.value,d.value);r.group&&(D.group=r.group),Object.keys(_).forEach(I=>{let P=_[I];if(!P)return;let R=I.toLowerCase();R.charAt(0)==="~"&&(R=R.substring(1),P.__once__=!0);let E=D;if(R.indexOf("zr:")===0&&(E=D.getZr(),R=R.substring(3)),P.__once__){delete P.__once__;const G=P;P=(...B)=>{G(...B),E.off(R,P)}}E.on(R,P)});function M(){D&&!D.isDisposed()&&D.resize()}function L(){const I=T||c.value;I&&D.setOption(I,y.value)}l.value?um(()=>{M(),L()}):L()}function b(T,D){r.manualUpdate&&(n.value=T),i.value?i.value.setOption(T,D||{}):S(T)}function w(){i.value&&(i.value.dispose(),i.value=void 0)}let x=null;Ia(u,T=>{typeof x=="function"&&(x(),x=null),T||(x=Ia(()=>r.option,(D,M)=>{D&&(i.value?i.value.setOption(D,{notMerge:D!==M,...y.value}):S())},{deep:!0}))},{immediate:!0}),Ia([v,d],()=>{w(),S()},{deep:!0}),td(()=>{r.group&&i.value&&(i.value.group=r.group)});const C=yA(i);return xA(i,f,h),mA(i,l,e),nm(()=>{S()}),am(()=>{DA&&e.value?e.value.__dispose=w:w()}),{chart:i,root:e,setOption:b,nonEventAttrs:p,nativeListeners:g,...C}},render(){const r={...this.nonEventAttrs,...this.nativeListeners};return r.ref="root",r.class=r.class?["echarts"].concat(r.class):"echarts",om(tm,r)}});export{J_ as $,Wu as A,AS as B,HA as C,Ch as D,YA as E,rt as F,Et as G,rr as H,k as I,Vu as J,Gu as K,My as L,EA as M,Rl as N,Rh as O,ot as P,sf as Q,bt as R,be as S,aD as T,ku as U,ZC as V,mt as W,NA as X,SD as Y,BA as Z,O as _,GA as a,rh as a0,In as a1,Pt as a2,yt as a3,FA as a4,kt as a5,Pa as a6,LT as a7,AT as a8,ft as a9,kA as aa,RA as ab,ah as ac,OA as ad,UA as b,WA as c,$A as d,VA as e,xp as f,it as g,Bm as h,zA as i,GT as j,A as k,PA as l,kn as m,aw as n,st as o,Dd as p,vt as q,Z as r,N as s,V as t,$r as u,Hr as v,Hu as w,Un as x,nr as y,LS as z};
