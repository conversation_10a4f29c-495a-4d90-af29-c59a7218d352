import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: 'https://hxljzz.kaoqin.zhuyibang.vip',
  timeout: 10000
})
/* // 创建axios实例
const api = axios.create({
  baseURL: 'http://*************:8083',
  timeout: 10000
}) */

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    } else {
      ElMessage.error(error.response?.data?.message || '请求失败')
    }
    return Promise.reject(error)
  }
)

// API接口定义
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 认证相关API
export const apiLogin = (username: string, password: string) => 
  api.post<any, ApiResponse>('/auth/login', { username, password })

export const apiGetProfile = () => 
  api.get<any, ApiResponse>('/auth/profile')

export const apiLogout = () =>
  api.post<any, ApiResponse>('/auth/logout')

// 个人信息相关API
export const apiUpdateProfile = (data: any) =>
  api.put<any, ApiResponse>('/auth/profile', data)

export const apiChangePassword = (data: { oldPassword: string; newPassword: string }) =>
  api.post<any, ApiResponse>('/users/change-password', data)

// 用户管理API
export const apiGetUsers = (params?: any) => 
  api.get<any, ApiResponse>('/users', { params })

export const apiCreateUser = (data: any) => 
  api.post<any, ApiResponse>('/users', data)

export const apiUpdateUser = (id: number, data: any) => 
  api.put<any, ApiResponse>(`/users/${id}`, data)

export const apiDeleteUser = (id: number) =>
  api.delete<any, ApiResponse>(`/users/${id}`)

export const apiResetPassword = (id: number, data: any) =>
  api.post<any, ApiResponse>(`/users/${id}/reset-password`, data)

// 工程管理API
export const apiGetProjects = (params?: any) =>
  api.get<any, ApiResponse>('/projects', { params })

export const apiGetProject = (id: number) =>
  api.get<any, ApiResponse>(`/projects/${id}`)

export const apiGetProjectManagers = () =>
  api.get<any, ApiResponse>('/projects/managers')

export const apiCreateProject = (data: any) =>
  api.post<any, ApiResponse>('/projects', data)

export const apiUpdateProject = (id: number, data: any) =>
  api.put<any, ApiResponse>(`/projects/${id}`, data)

export const apiDeleteProject = (id: number) =>
  api.delete<any, ApiResponse>(`/projects/${id}`)

export const apiGetProjectAssignments = (id: number) => 
  api.get<any, ApiResponse>(`/projects/${id}/assignments`)

export const apiAssignUserToProject = (id: number, data: any) =>
  api.post<any, ApiResponse>(`/projects/${id}/assignments`, data)

export const apiUpdateProjectAssignment = (projectId: number, assignmentId: number, data: any) =>
  api.put<any, ApiResponse>(`/projects/${projectId}/assignments/${assignmentId}`, data)

export const apiRemoveProjectAssignment = (projectId: number, assignmentId: number) =>
  api.delete<any, ApiResponse>(`/projects/${projectId}/assignments/${assignmentId}`)

// 地点管理API
export const apiGetLocations = (params?: any) =>
  api.get<any, ApiResponse>('/locations', { params })

export const apiGetAllLocations = (params?: any) =>
  api.get<any, ApiResponse>('/locations/all', { params })

export const apiCreateLocation = (data: any) => 
  api.post<any, ApiResponse>('/locations', data)

export const apiUpdateLocation = (id: number, data: any) => 
  api.put<any, ApiResponse>(`/locations/${id}`, data)

export const apiDeleteLocation = (id: number) => 
  api.delete<any, ApiResponse>(`/locations/${id}`)

// 打卡记录API
export const apiGetCheckinRecords = (params?: any) =>
  api.get<any, ApiResponse>('/checkin/records', { params })

// 报表相关API
export const apiGetAttendanceReport = (params?: any) =>
  api.get<any, ApiResponse>('/reports/attendance', { params })

export const apiGetDetailedReport = (params?: any) =>
  api.get<any, ApiResponse>('/reports/detailed', { params })

export const apiGetSummaryReport = (params?: any) =>
  api.get<any, ApiResponse>('/reports/summary', { params })

export const apiGetRealtimeData = () =>
  api.get<any, ApiResponse>('/reports/realtime')

export const apiExportReport = (data: any) =>
  api.post<any, ApiResponse>('/reports/export', data)

export const apiGetMonthlyAttendance = (params?: any) =>
  api.get<any, ApiResponse>('/reports/monthly', { params })

export const apiGetEmployeeProjectAttendance = (params?: any) =>
  api.get<any, ApiResponse>('/reports/employee-project', { params })

export const apiGetEmployeesWithRecords = (params?: any) =>
  api.get<any, ApiResponse>('/reports/employees-with-records', { params })

export const apiGetEmployeeDailyRecords = (params?: any) =>
  api.get<any, ApiResponse>('/reports/employee-daily', { params })

export const apiRecalculateAttendance = (data: { startDate: string, endDate: string }) =>
  api.post<any, ApiResponse>('/reports/recalculate', data)

// 详情数据API
export const apiGetTodayPresentEmployees = () =>
  api.get<any, ApiResponse>('/reports/today/present')

export const apiGetTodayAbsentEmployees = () =>
  api.get<any, ApiResponse>('/reports/today/absent')

export const apiGetTodayLateEmployees = () =>
  api.get<any, ApiResponse>('/reports/today/late')

export const apiGetTodayAbnormalRecords = () =>
  api.get<any, ApiResponse>('/reports/today/abnormal')

export const apiGetMonthlyWorkHoursDetail = () =>
  api.get<any, ApiResponse>('/reports/monthly/workhours')

// 系统日志相关API
export const apiGetSystemLogs = (params?: any) =>
  api.get<any, ApiResponse>('/logs', { params })

export const apiGetUserLogs = (userId: number, params?: any) =>
  api.get<any, ApiResponse>(`/logs/user/${userId}`, { params })

export const apiGetModuleLogs = (module: string, params?: any) =>
  api.get<any, ApiResponse>(`/logs/module/${module}`, { params })

// 补卡申请API
export const apiGetCheckinApplications = (params?: any) =>
  api.get<any, ApiResponse>('/checkin-applications', { params })

export const apiReviewCheckinApplication = (id: number, data: any) =>
  api.put<any, ApiResponse>(`/checkin-applications/${id}/review`, data)

export const apiGetCheckinApplication = (id: number) =>
  api.get<any, ApiResponse>(`/checkin-applications/${id}`)

export const apiSubmitCheckinApplication = (data: any) =>
  api.post<any, ApiResponse>('/checkin-applications', data)

// 版本管理API
export const apiGetAppVersions = (params?: any) =>
  api.get<any, ApiResponse>('/app/versions', { params })

export const apiCreateAppVersion = (data: any) =>
  api.post<any, ApiResponse>('/app/versions', data)

export const apiUpdateAppVersion = (id: number, data: any) =>
  api.put<any, ApiResponse>(`/app/versions/${id}`, data)

export const apiDeleteAppVersion = (id: number) =>
  api.delete<any, ApiResponse>(`/app/versions/${id}`)

export const apiCheckAppVersion = (params?: any) =>
  api.get<any, ApiResponse>('/app/version-check', { params })

export default api
