<template>
  <div class="reports-page">
    <div class="page-header">
      <h2>考勤报表</h2>
      <div class="header-actions">
        <el-button
          type="warning"
          :loading="recalculateLoading"
          @click="recalculateCurrentMonthData"
          style="margin-right: 10px;"
        >
          重新计算当前月数据
        </el-button>
        <el-dropdown @command="handleExportCommand" trigger="click">
          <el-button type="success" :icon="Download">
            导出报表
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="excel">
                <el-icon><Document /></el-icon>
                导出Excel
              </el-dropdown-item>
              <el-dropdown-item command="csv">
                <el-icon><Tickets /></el-icon>
                导出CSV
              </el-dropdown-item>
              <el-dropdown-item command="pdf">
                <el-icon><Printer /></el-icon>
                导出PDF
              </el-dropdown-item>
              <el-dropdown-item divided command="custom">
                <el-icon><Setting /></el-icon>
                自定义导出
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 实时统计概览 -->
    <el-row :gutter="20" class="realtime-stats">
      <el-col :span="4">
        <el-card class="stat-card realtime clickable" @click="handleCardClick('totalEmployees')">
          <div class="stat-content">
            <div class="stat-icon">👥</div>
            <div class="stat-value">{{ realtimeData.totalEmployees }}</div>
            <div class="stat-label">总员工数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card realtime clickable" @click="handleCardClick('presentToday')">
          <div class="stat-content">
            <div class="stat-icon">✅</div>
            <div class="stat-value">{{ realtimeData.presentToday }}</div>
            <div class="stat-label">今日出勤</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card realtime clickable" @click="handleCardClick('absentToday')">
          <div class="stat-content">
            <div class="stat-icon">❌</div>
            <div class="stat-value">{{ realtimeData.absentToday }}</div>
            <div class="stat-label">今日缺勤</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card realtime clickable" @click="handleCardClick('lateToday')">
          <div class="stat-content">
            <div class="stat-icon">⏰</div>
            <div class="stat-value">{{ realtimeData.lateToday }}</div>
            <div class="stat-label">今日迟到</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card realtime clickable" @click="handleCardClick('monthlyWorkHours')">
          <div class="stat-content">
            <div class="stat-icon">📊</div>
            <div class="stat-value">{{ realtimeData.monthlyWorkHours }}h</div>
            <div class="stat-label">本月总工时</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card realtime clickable" @click="handleCardClick('abnormalRecords')">
          <div class="stat-content">
            <div class="stat-icon">⚠️</div>
            <div class="stat-value">{{ realtimeData.abnormalRecords }}</div>
            <div class="stat-label">今日异常打卡</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="视图模式">
          <el-radio-group v-model="viewMode" @change="handleViewModeChange">
            <el-radio-button value="daily">按日统计</el-radio-button>
            <el-radio-button value="monthly">按月统计</el-radio-button>
            <el-radio-button value="employee-project">员工项目</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="viewMode === 'daily'" label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item v-if="viewMode === 'monthly'" label="选择月份">
          <el-date-picker
            v-model="selectedMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
        </el-form-item>
        <el-form-item v-if="viewMode === 'employee-project'" label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="工程">
          <el-select v-model="filterForm.projectId" placeholder="请选择工程" clearable>
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户">
          <el-select v-model="filterForm.userId" placeholder="请选择用户" clearable filterable>
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.realName"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="viewMode === 'daily'" label="状态">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="abnormal" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadReports">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="charts-row" v-if="viewMode !== 'employee-project'">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>出勤趋势图</span>
          </template>
          <v-chart
            class="chart"
            :option="trendChartOption"
            :loading="chartLoading"
            autoresize
          />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>出勤分布</span>
          </template>
          <v-chart
            class="chart"
            :option="pieChartOption"
            :loading="chartLoading"
            autoresize
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ summary.totalDays }}</div>
            <div class="stat-label">总天数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ summary.normalDays }}</div>
            <div class="stat-label">正常天数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ summary.abnormalDays }}</div>
            <div class="stat-label">异常天数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ summary.totalHours }}</div>
            <div class="stat-label">总工时</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 考勤详情表格 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>
            {{ viewMode === 'daily' ? '考勤详情' :
               viewMode === 'monthly' ? '月度统计' : '员工项目统计' }}
          </span>
          <span class="record-count">共 {{ pagination.total }} 条记录</span>
        </div>
      </template>

      <!-- 按日统计表格 -->
      <el-table
        v-if="viewMode === 'daily'"
        v-loading="loading"
        :data="reportData"
        style="width: 100%"
        :summary-method="getSummaries"
        show-summary
      >
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="userName" label="姓名" width="100" />
        <el-table-column prop="projectName" label="工程" width="150" />
        <el-table-column prop="firstCheckIn" label="首次签到" width="120">
          <template #default="{ row }">
            {{ row.firstCheckIn ? formatTime(row.firstCheckIn) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="lastCheckOut" label="最后签退" width="120">
          <template #default="{ row }">
            {{ row.lastCheckOut ? formatTime(row.lastCheckOut) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="checkInCount" label="签到次数" width="100" />
        <el-table-column prop="checkOutCount" label="签退次数" width="100" />
        <el-table-column prop="totalWorkMinutes" label="工作时长" width="120">
          <template #default="{ row }">
            {{ formatDuration(row.totalWorkMinutes) }}
          </template>
        </el-table-column>
        <el-table-column prop="isAbnormal" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.isAbnormal ? 'danger' : 'success'">
              {{ row.isAbnormal ? '异常' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button text type="primary" @click="viewDetails(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 月度统计表格 -->
      <el-table
        v-if="viewMode === 'monthly'"
        v-loading="loading"
        :data="monthlyData"
        style="width: 100%"
      >
        <el-table-column prop="userName" label="姓名" width="120" />
        <el-table-column prop="department" label="部门" width="120" />
        <el-table-column prop="attendanceDays" label="出勤天数" width="100" />
        <el-table-column prop="absentDays" label="缺勤天数" width="100" />
        <el-table-column prop="abnormalDays" label="异常天数" width="100" />
        <el-table-column prop="totalWorkHours" label="总工时" width="100">
          <template #default="{ row }">
            {{ row.totalWorkHours }}h
          </template>
        </el-table-column>
        <el-table-column prop="avgWorkHours" label="平均工时" width="100">
          <template #default="{ row }">
            {{ row.avgWorkHours }}h
          </template>
        </el-table-column>
        <el-table-column prop="attendanceRate" label="出勤率" width="100">
          <template #default="{ row }">
            <el-tag :type="row.attendanceRate >= 90 ? 'success' : row.attendanceRate >= 80 ? 'warning' : 'danger'">
              {{ row.attendanceRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button text type="primary" @click="viewEmployeeDetails(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 员工项目统计表格 -->
      <el-table
        v-if="viewMode === 'employee-project'"
        v-loading="loading"
        :data="employeeProjectData"
        style="width: 100%"
      >
        <el-table-column prop="userName" label="姓名" width="120" />
        <el-table-column prop="projectName" label="工程" width="150" />
        <el-table-column prop="clientName" label="客户" width="120" />
        <el-table-column prop="role" label="角色" width="100" />
        <el-table-column prop="attendanceDays" label="出勤天数" width="100" />
        <el-table-column prop="totalWorkHours" label="总工时" width="100">
          <template #default="{ row }">
            {{ row.totalWorkHours }}h
          </template>
        </el-table-column>
        <el-table-column prop="avgWorkHours" label="平均工时" width="100">
          <template #default="{ row }">
            {{ row.avgWorkHours }}h
          </template>
        </el-table-column>
        <el-table-column prop="attendanceRate" label="出勤率" width="100">
          <template #default="{ row }">
            <el-tag :type="row.attendanceRate >= 90 ? 'success' : row.attendanceRate >= 80 ? 'warning' : 'danger'">
              {{ row.attendanceRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button text type="primary" @click="viewProjectDetails(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日常考勤详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="考勤详情"
      width="800px"
    >
      <el-table :data="detailRecords" style="width: 100%">
        <el-table-column prop="checkType" label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="row.checkType === 'in' ? 'success' : 'warning'">
              {{ row.checkType === 'in' ? '签到' : '签退' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="locationName" label="地点" width="150" />
        <el-table-column prop="address" label="地址" min-width="200" />
        <el-table-column prop="distance" label="距离" width="80">
          <template #default="{ row }">
            {{ row.distance ? Math.round(row.distance) + 'm' : '-' }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 员工月度详情对话框 -->
    <el-dialog
      v-model="showEmployeeDetailDialog"
      :title="`${currentEmployee?.userName} - ${selectedMonth}月详细考勤记录`"
      width="1200px"
      top="5vh"
    >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">{{ currentEmployee?.userName }} - {{ selectedMonth }}月详细考勤记录</span>
          <div class="dialog-actions">
            <el-dropdown @command="handleEmployeeExport">
              <el-button type="primary" :icon="Download">
                导出报表<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="excel">导出Excel</el-dropdown-item>
                  <el-dropdown-item command="pdf">导出PDF</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
      <div class="employee-detail-content">
        <!-- 月度汇总信息 -->
        <el-card class="summary-card" shadow="never">
          <template #header>
            <span>月度汇总</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployee?.attendanceDays || 0 }}</div>
                <div class="summary-label">出勤天数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployee?.normalDays || 0 }}</div>
                <div class="summary-label">正常天数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployee?.abnormalDays || 0 }}</div>
                <div class="summary-label">异常天数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployee?.totalWorkHours || 0 }}h</div>
                <div class="summary-label">总工时</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 每日详细记录 -->
        <el-card class="detail-card">
          <template #header>
            <span>每日详细记录</span>
          </template>
          <el-table
            v-loading="employeeDetailLoading"
            :data="employeeDailyRecords"
            style="width: 100%"
            :default-sort="{ prop: 'date', order: 'descending' }"
          >
            <el-table-column prop="date" label="日期" width="120" sortable />
            <el-table-column prop="projectName" label="项目" width="150" />
            <el-table-column prop="firstCheckIn" label="首次签到" width="120">
              <template #default="{ row }">
                {{ row.firstCheckIn ? formatTime(row.firstCheckIn) : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="lastCheckOut" label="最后签退" width="120">
              <template #default="{ row }">
                {{ row.lastCheckOut ? formatTime(row.lastCheckOut) : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="checkInCount" label="签到次数" width="100" />
            <el-table-column prop="checkOutCount" label="签退次数" width="100" />
            <el-table-column prop="totalWorkMinutes" label="工作时长" width="120">
              <template #default="{ row }">
                {{ formatDuration(row.totalWorkMinutes) }}
              </template>
            </el-table-column>
            <el-table-column prop="isAbnormal" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.isAbnormal ? 'danger' : 'success'">
                  {{ row.isAbnormal ? '异常' : '正常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button text type="primary" @click="viewDayDetails(row)">
                  查看打卡
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-dialog>

    <!-- 员工项目详情对话框 -->
    <el-dialog
      v-model="showProjectDetailDialog"
      :title="`${currentEmployeeProject?.userName} - ${currentEmployeeProject?.projectName} 项目详细记录`"
      width="1200px"
      top="5vh"
    >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">{{ currentEmployeeProject?.userName }} - {{ currentEmployeeProject?.projectName }} 项目详细记录</span>
          <div class="dialog-actions">
            <el-dropdown @command="handleProjectExport">
              <el-button type="primary" :icon="Download">
                导出报表<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="excel">导出Excel</el-dropdown-item>
                  <el-dropdown-item command="pdf">导出PDF</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
      <div class="project-detail-content">
        <!-- 项目汇总信息 -->
        <el-card class="summary-card" shadow="never">
          <template #header>
            <span>项目汇总</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="4">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployeeProject?.attendanceDays || 0 }}</div>
                <div class="summary-label">出勤天数</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployeeProject?.totalWorkHours || 0 }}h</div>
                <div class="summary-label">总工时</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployeeProject?.avgWorkHours || 0 }}h</div>
                <div class="summary-label">平均工时</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployeeProject?.attendanceRate || 0 }}%</div>
                <div class="summary-label">出勤率</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployeeProject?.role || '员工' }}</div>
                <div class="summary-label">项目角色</div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="summary-item">
                <div class="summary-value">{{ currentEmployeeProject?.clientName || '未知' }}</div>
                <div class="summary-label">客户</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 项目考勤记录 -->
        <el-card class="detail-card">
          <template #header>
            <span>项目考勤记录</span>
          </template>
          <el-table
            v-loading="projectDetailLoading"
            :data="projectDailyRecords"
            style="width: 100%"
            :default-sort="{ prop: 'date', order: 'descending' }"
          >
            <el-table-column prop="date" label="日期" width="120" sortable />
            <el-table-column prop="firstCheckIn" label="首次签到" width="120">
              <template #default="{ row }">
                {{ row.firstCheckIn ? formatTime(row.firstCheckIn) : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="lastCheckOut" label="最后签退" width="120">
              <template #default="{ row }">
                {{ row.lastCheckOut ? formatTime(row.lastCheckOut) : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="checkInCount" label="签到次数" width="100" />
            <el-table-column prop="checkOutCount" label="签退次数" width="100" />
            <el-table-column prop="totalWorkMinutes" label="工作时长" width="120">
              <template #default="{ row }">
                {{ formatDuration(row.totalWorkMinutes) }}
              </template>
            </el-table-column>
            <el-table-column prop="isAbnormal" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.isAbnormal ? 'danger' : 'success'">
                  {{ row.isAbnormal ? '异常' : '正常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button text type="primary" @click="viewDayDetails(row)">
                  查看打卡
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-dialog>

    <!-- 自定义导出对话框 -->
    <el-dialog
      v-model="showExportDialog"
      title="自定义导出设置"
      width="600px"
    >
      <el-form :model="exportForm" label-width="120px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio value="excel">Excel (.xlsx)</el-radio>
            <el-radio value="csv">CSV (.csv)</el-radio>
            <el-radio value="pdf">PDF (.pdf)</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="导出内容">
          <el-radio-group v-model="exportForm.content">
            <el-radio value="current">当前页面数据</el-radio>
            <el-radio value="all">全部数据</el-radio>
            <el-radio value="filtered">筛选后的数据</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="包含字段" v-if="viewMode === 'daily'">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox value="date">日期</el-checkbox>
            <el-checkbox value="userName">姓名</el-checkbox>
            <el-checkbox value="projectName">工程</el-checkbox>
            <el-checkbox value="firstCheckIn">首次签到</el-checkbox>
            <el-checkbox value="lastCheckOut">最后签退</el-checkbox>
            <el-checkbox value="checkInCount">签到次数</el-checkbox>
            <el-checkbox value="checkOutCount">签退次数</el-checkbox>
            <el-checkbox value="totalWorkMinutes">工作时长</el-checkbox>
            <el-checkbox value="isAbnormal">状态</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="包含字段" v-if="viewMode === 'monthly'">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox value="userName">姓名</el-checkbox>
            <el-checkbox value="department">部门</el-checkbox>
            <el-checkbox value="attendanceDays">出勤天数</el-checkbox>
            <el-checkbox value="absentDays">缺勤天数</el-checkbox>
            <el-checkbox value="abnormalDays">异常天数</el-checkbox>
            <el-checkbox value="totalWorkHours">总工时</el-checkbox>
            <el-checkbox value="avgWorkHours">平均工时</el-checkbox>
            <el-checkbox value="attendanceRate">出勤率</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="包含字段" v-if="viewMode === 'employee-project'">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox value="userName">姓名</el-checkbox>
            <el-checkbox value="projectName">工程</el-checkbox>
            <el-checkbox value="clientName">客户</el-checkbox>
            <el-checkbox value="role">角色</el-checkbox>
            <el-checkbox value="attendanceDays">出勤天数</el-checkbox>
            <el-checkbox value="totalWorkHours">总工时</el-checkbox>
            <el-checkbox value="avgWorkHours">平均工时</el-checkbox>
            <el-checkbox value="attendanceRate">出勤率</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="文件名">
          <el-input
            v-model="exportForm.filename"
            placeholder="请输入文件名"
          >
            <template #append>.{{ exportForm.format === 'excel' ? 'xlsx' : exportForm.format }}</template>
          </el-input>
        </el-form-item>

        <el-form-item label="包含汇总">
          <el-switch v-model="exportForm.includeSummary" />
          <span class="form-tip">是否在导出文件中包含统计汇总信息</span>
        </el-form-item>

        <el-form-item label="包含图表" v-if="exportForm.format === 'excel'">
          <el-switch v-model="exportForm.includeCharts" />
          <span class="form-tip">是否在Excel中包含图表（仅Excel格式支持）</span>
        </el-form-item>

        <el-form-item label="PDF生成方式" v-if="exportForm.format === 'pdf'">
          <el-radio-group v-model="exportForm.pdfMethod">
            <el-radio value="html2canvas">HTML转图片（推荐，完美支持中文）</el-radio>
            <el-radio value="text">文本模式（英文/中文可选）</el-radio>
          </el-radio-group>
          <div class="form-tip">
            HTML转图片方式可以完美显示中文，但文件较大。文本模式文件小但中文可能乱码。
          </div>
        </el-form-item>

        <el-form-item label="PDF语言" v-if="exportForm.format === 'pdf' && exportForm.pdfMethod === 'text'">
          <el-radio-group v-model="exportForm.pdfLanguage">
            <el-radio value="english">英文（避免乱码）</el-radio>
            <el-radio value="chinese">中文（可能乱码）</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button type="primary" @click="executeExport" :loading="exportLoading">
            开始导出
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 卡片详情弹窗 -->
    <el-dialog
      v-model="showCardDetailDialog"
      :title="cardDetailDialogTitle"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-loading="cardDetailDialogLoading">
        <!-- 今日出勤员工列表 -->
        <div v-if="cardDetailDialogType === 'presentToday'">
          <el-table :data="cardDetailDialogData" style="width: 100%">
            <el-table-column prop="userName" label="员工姓名" width="120" />
            <el-table-column prop="projectName" label="项目" width="150" />
            <el-table-column prop="locationName" label="打卡地点" width="150" />
            <el-table-column prop="locationAddress" label="地点地址" min-width="200" />
            <el-table-column prop="firstCheckIn" label="首次签到时间" width="180">
              <template #default="{ row }">
                {{ row.firstCheckIn || '-' }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 今日缺勤员工列表 -->
        <div v-if="cardDetailDialogType === 'absentToday'">
          <el-table :data="cardDetailDialogData" style="width: 100%">
            <el-table-column prop="realName" label="员工姓名" width="120" />
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="role" label="角色" width="100">
              <template #default="{ row }">
                <el-tag :type="row.role === 'admin' ? 'danger' : row.role === 'manager' ? 'warning' : 'info'">
                  {{ row.role === 'admin' ? '管理员' : row.role === 'manager' ? '经理' : '员工' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话" width="130" />
            <el-table-column prop="email" label="邮箱" min-width="180" />
          </el-table>
        </div>

        <!-- 今日迟到员工列表 -->
        <div v-if="cardDetailDialogType === 'lateToday'">
          <el-table :data="cardDetailDialogData" style="width: 100%">
            <el-table-column prop="userName" label="员工姓名" width="120" />
            <el-table-column prop="projectName" label="项目" width="150" />
            <el-table-column prop="locationName" label="打卡地点" width="150" />
            <el-table-column prop="workTimeStart" label="应到时间" width="100" />
            <el-table-column prop="checkInTimeStr" label="实际到达时间" width="120" />
            <el-table-column prop="lateTime" label="迟到时长" width="120">
              <template #default="{ row }">
                <el-tag type="warning">{{ row.lateTime }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 本月工时统计详情 -->
        <div v-if="cardDetailDialogType === 'monthlyWorkHours'">
          <el-table :data="cardDetailDialogData" style="width: 100%">
            <el-table-column prop="userName" label="员工姓名" width="120" />
            <el-table-column prop="projectName" label="项目" width="150" />
            <el-table-column prop="workDays" label="工作天数" width="100" />
            <el-table-column prop="totalWorkHours" label="总工时" width="100">
              <template #default="{ row }">
                {{ row.totalWorkHours }}h
              </template>
            </el-table-column>
            <el-table-column prop="avgWorkHours" label="平均工时" width="100">
              <template #default="{ row }">
                {{ row.avgWorkHours }}h
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 今日异常打卡记录 -->
        <div v-if="cardDetailDialogType === 'abnormalRecords'">
          <el-table :data="cardDetailDialogData" style="width: 100%">
            <el-table-column prop="userName" label="员工姓名" width="120" />
            <el-table-column prop="projectName" label="项目" width="150" />
            <el-table-column prop="checkType" label="打卡类型" width="100">
              <template #default="{ row }">
                <el-tag :type="row.checkType === 'in' ? 'success' : 'warning'">
                  {{ row.checkType === 'in' ? '签到' : '签退' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="打卡时间" width="180">
              <template #default="{ row }">
                {{ row.createdAt }}
              </template>
            </el-table-column>
            <el-table-column prop="abnormalType" label="异常类型" width="100">
              <template #default="{ row }">
                <el-tag type="danger">{{ row.abnormalType }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="abnormalReason" label="异常原因" min-width="200" />
            <el-table-column prop="locationName" label="打卡地点" width="150" />
          </el-table>
        </div>

        <!-- 空数据提示 -->
        <div v-if="!cardDetailDialogLoading && cardDetailDialogData.length === 0" class="empty-data">
          <el-empty description="暂无数据" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download,
  ArrowDown,
  Document,
  Tickets,
  Printer,
  Setting
} from '@element-plus/icons-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  apiGetAttendanceReport,
  apiGetSummaryReport,
  apiGetDetailedReport,
  apiGetRealtimeData,
  apiGetMonthlyAttendance,
  apiGetEmployeeProjectAttendance,
  apiGetEmployeesWithRecords,
  apiGetEmployeeDailyRecords,
  apiExportReport,
  apiGetProjects,
  apiGetUsers,
  apiRecalculateAttendance,
  apiGetTodayPresentEmployees,
  apiGetTodayAbsentEmployees,
  apiGetTodayLateEmployees,
  apiGetTodayAbnormalRecords,
  apiGetMonthlyWorkHoursDetail
} from '@/utils/api'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const loading = ref(false)
const chartLoading = ref(false)
const recalculateLoading = ref(false)
const reportData = ref([])
const monthlyData = ref([])
const employeeProjectData = ref([])
const projects = ref([])
const users = ref([])
const showDetailDialog = ref(false)
const detailRecords = ref([])
const showEmployeeDetailDialog = ref(false)
const showProjectDetailDialog = ref(false)
const employeeDetailLoading = ref(false)
const projectDetailLoading = ref(false)
const employeeDailyRecords = ref([])
const projectDailyRecords = ref([])
const currentEmployee = ref(null)
const currentEmployeeProject = ref(null)
const viewMode = ref('daily')
const selectedMonth = ref('')

// 导出相关
const showExportDialog = ref(false)
const exportLoading = ref(false)
const exportForm = reactive({
  format: 'excel',
  content: 'filtered',
  fields: [],
  filename: '',
  includeSummary: true,
  includeCharts: false,
  pdfMethod: 'html2canvas',
  pdfLanguage: 'english'
})

// 实时数据
const realtimeData = reactive({
  totalEmployees: 0,
  presentToday: 0,
  absentToday: 0,
  lateToday: 0,
  monthlyWorkHours: 0,
  abnormalRecords: 0
})

// 卡片详情弹窗相关
const showCardDetailDialog = ref(false)
const cardDetailDialogTitle = ref('')
const cardDetailDialogLoading = ref(false)
const cardDetailDialogData = ref([])
const cardDetailDialogType = ref('')

const filterForm = reactive({
  projectId: null,
  userId: null,
  status: ''
})

const dateRange = ref([])

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const summary = reactive({
  totalDays: 0,
  normalDays: 0,
  abnormalDays: 0,
  totalHours: 0
})

// 图表配置
const trendChartOption = computed(() => ({
  title: {
    text: '出勤趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['出勤人数', '异常人数'],
    bottom: 0
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: trendData.value.dates
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '出勤人数',
      type: 'line',
      data: trendData.value.attendance,
      smooth: true,
      itemStyle: { color: '#67C23A' }
    },
    {
      name: '异常人数',
      type: 'line',
      data: trendData.value.abnormal,
      smooth: true,
      itemStyle: { color: '#F56C6C' }
    }
  ]
}))

const pieChartOption = computed(() => ({
  title: {
    text: '出勤分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '出勤统计',
      type: 'pie',
      radius: '50%',
      data: [
        { value: summary.normalDays, name: '正常出勤', itemStyle: { color: '#67C23A' } },
        { value: summary.abnormalDays, name: '异常记录', itemStyle: { color: '#F56C6C' } },
        { value: realtimeData.absentToday, name: '缺勤', itemStyle: { color: '#909399' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 趋势数据
const trendData = ref({
  dates: [],
  attendance: [],
  abnormal: []
})

// 方法
const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDateTime = (timeString: string) => {
  return new Date(timeString).toLocaleString('zh-CN')
}

const formatDuration = (minutes: number) => {
  if (!minutes) return '-'
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}h${mins}m`
}

const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    if (column.property === 'totalWorkMinutes') {
      const total = data.reduce((sum: number, row: any) => sum + (row.totalWorkMinutes || 0), 0)
      sums[index] = formatDuration(total)
    } else if (column.property === 'checkInCount' || column.property === 'checkOutCount') {
      const total = data.reduce((sum: number, row: any) => sum + (row[column.property] || 0), 0)
      sums[index] = total.toString()
    } else {
      sums[index] = ''
    }
  })
  return sums
}

// 加载实时数据
const loadRealtimeData = async () => {
  try {
    const response = await apiGetRealtimeData()
    if (response.success) {
      // 映射后端字段到前端字段
      const data = response.data
      console.log('实时数据API响应:', data)
      Object.assign(realtimeData, {
        totalEmployees: data.totalUsers || 0,
        presentToday: data.todayPresentUsers || 0,
        absentToday: data.todayAbsentUsers || 0,
        lateToday: data.todayLateUsers || 0,
        monthlyWorkHours: Math.round((data.monthlyTotalWorkMinutes || 0) / 60),
        abnormalRecords: data.todayAbnormalRecords || 0
      })
      console.log('更新后的实时数据:', realtimeData)
    }
  } catch (error) {
    console.error('Load realtime data error:', error)
  }
}

// 卡片点击处理
const handleCardClick = async (cardType: string) => {
  console.log('点击卡片:', cardType)
  cardDetailDialogType.value = cardType
  cardDetailDialogLoading.value = true
  showCardDetailDialog.value = true

  try {
    switch (cardType) {
      case 'presentToday':
        cardDetailDialogTitle.value = '今日出勤员工列表'
        const presentResponse = await apiGetTodayPresentEmployees()
        if (presentResponse.success) {
          cardDetailDialogData.value = presentResponse.data || []
        } else {
          ElMessage.error(presentResponse.message || '获取今日出勤员工列表失败')
        }
        break

      case 'absentToday':
        cardDetailDialogTitle.value = '今日缺勤员工列表'
        const absentResponse = await apiGetTodayAbsentEmployees()
        if (absentResponse.success) {
          cardDetailDialogData.value = absentResponse.data || []
        } else {
          ElMessage.error(absentResponse.message || '获取今日缺勤员工列表失败')
        }
        break

      case 'lateToday':
        cardDetailDialogTitle.value = '今日迟到员工列表'
        const lateResponse = await apiGetTodayLateEmployees()
        if (lateResponse.success) {
          cardDetailDialogData.value = lateResponse.data || []
        } else {
          ElMessage.error(lateResponse.message || '获取今日迟到员工列表失败')
        }
        break

      case 'monthlyWorkHours':
        cardDetailDialogTitle.value = '本月工时统计详情'
        const workHoursResponse = await apiGetMonthlyWorkHoursDetail()
        if (workHoursResponse.success) {
          cardDetailDialogData.value = workHoursResponse.data || []
        } else {
          ElMessage.error(workHoursResponse.message || '获取本月工时统计详情失败')
        }
        break

      case 'abnormalRecords':
        cardDetailDialogTitle.value = '今日异常打卡记录'
        const abnormalResponse = await apiGetTodayAbnormalRecords()
        if (abnormalResponse.success) {
          cardDetailDialogData.value = abnormalResponse.data || []
        } else {
          ElMessage.error(abnormalResponse.message || '获取今日异常打卡记录失败')
        }
        break

      default:
        // 总员工数跳转到用户管理页面
        if (cardType === 'totalEmployees') {
          window.open('/users', '_blank')
          showCardDetailDialog.value = false
        }
        break
    }
  } catch (error) {
    console.error('获取详情数据失败:', error)
    ElMessage.error('获取详情数据失败')
  } finally {
    cardDetailDialogLoading.value = false
  }
}

// 视图模式切换
const handleViewModeChange = () => {
  pagination.page = 1
  loadReports()
}

const loadReports = async () => {
  loading.value = true
  try {
    if (viewMode.value === 'daily') {
      await loadDailyReports()
    } else if (viewMode.value === 'monthly') {
      await loadMonthlyReports()
    } else if (viewMode.value === 'employee-project') {
      await loadEmployeeProjectReports()
    }
  } finally {
    loading.value = false
  }
}

// 加载按日统计
const loadDailyReports = async () => {
  const params = {
    page: pagination.page,
    limit: pagination.size,
    projectId: filterForm.projectId,
    userId: filterForm.userId,
    startDate: dateRange.value?.[0],
    endDate: dateRange.value?.[1]
  }

  // 添加状态筛选
  if (filterForm.status === 'normal') {
    params.isAbnormal = false
  } else if (filterForm.status === 'abnormal') {
    params.isAbnormal = true
  }

  const response = await apiGetAttendanceReport(params)
  if (response.success) {
    reportData.value = response.data.records || []
    pagination.total = response.data.pagination?.total || 0

    // 加载汇总数据
    const summaryResponse = await apiGetSummaryReport(params)
    if (summaryResponse.success) {
      const summaryData = summaryResponse.data.summary
      summary.totalDays = summaryData.totalDays || 0
      summary.normalDays = summaryData.normalDays || 0
      summary.abnormalDays = summaryData.abnormalDays || 0
      summary.totalHours = Math.round((summaryData.totalWorkMinutes || 0) / 60)
    }

    // 加载趋势数据
    await loadTrendData(params)
  } else {
    ElMessage.error(response.message || '加载报表数据失败')
  }
}

// 加载月度统计
const loadMonthlyReports = async () => {
  const [year, month] = selectedMonth.value ? selectedMonth.value.split('-') : [
    new Date().getFullYear().toString(),
    (new Date().getMonth() + 1).toString()
  ]

  const params = {
    year,
    month,
    projectId: filterForm.projectId,
    userId: filterForm.userId
  }

  const response = await apiGetMonthlyAttendance(params)
  if (response.success) {
    const data = response.data
    monthlyData.value = data.employees || []
    pagination.total = monthlyData.value.length

    // 映射字段名，确保与表格列匹配
    monthlyData.value = monthlyData.value.map(emp => ({
      ...emp,
      department: emp.userRole || '未分配', // 用角色代替部门
      totalWorkHours: parseFloat(emp.totalWorkHours) || 0,
      avgWorkHours: parseFloat(emp.avgWorkHours) || 0,
      attendanceRate: parseFloat(emp.attendanceRate) || 0
    }))

    // 使用后端返回的汇总数据
    if (data.summary) {
      summary.totalDays = data.summary.totalAttendanceDays || 0
      summary.normalDays = data.summary.totalAttendanceDays - data.summary.totalAbnormalDays || 0
      summary.abnormalDays = data.summary.totalAbnormalDays || 0
      summary.totalHours = Math.round(monthlyData.value.reduce((sum, emp) => sum + emp.totalWorkHours, 0))
    }
  } else {
    ElMessage.error(response.message || '加载月度数据失败')
  }
}

// 加载员工项目统计
const loadEmployeeProjectReports = async () => {
  const params = {
    page: pagination.page,
    limit: pagination.size,
    projectId: filterForm.projectId,
    userId: filterForm.userId,
    startDate: dateRange.value?.[0],
    endDate: dateRange.value?.[1]
  }

  const response = await apiGetEmployeeProjectAttendance(params)
  if (response.success) {
    const records = response.data.records || []
    pagination.total = response.data.pagination?.total || 0

    // 映射字段名，确保与表格列匹配
    employeeProjectData.value = records.map(record => ({
      ...record,
      role: record.userRole || '员工',
      attendanceDays: record.totalDays || 0,
      totalWorkHours: record.workHours || 0, // 后端返回的是 workHours
      avgWorkHours: record.avgWorkHours || 0,
      attendanceRate: parseFloat(record.attendanceRate) || 0
    }))

    // 计算汇总数据
    const totalDays = employeeProjectData.value.reduce((sum, emp) => sum + emp.attendanceDays, 0)
    const normalDays = employeeProjectData.value.reduce((sum, emp) => sum + emp.workDays, 0)
    const abnormalDays = employeeProjectData.value.reduce((sum, emp) => sum + emp.absentDays, 0)
    const totalHours = employeeProjectData.value.reduce((sum, emp) => sum + emp.totalWorkHours, 0)

    summary.totalDays = totalDays
    summary.normalDays = normalDays
    summary.abnormalDays = abnormalDays
    summary.totalHours = Math.round(totalHours)
  } else {
    ElMessage.error(response.message || '加载员工项目数据失败')
  }
}

// 加载趋势数据
const loadTrendData = async (params: any) => {
  chartLoading.value = true
  try {
    // 这里可以调用专门的趋势数据API，暂时用现有数据模拟
    const dates = []
    const attendance = []
    const abnormal = []

    // 生成最近7天的数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))

      // 模拟数据，实际应该从API获取
      attendance.push(Math.floor(Math.random() * 20) + 10)
      abnormal.push(Math.floor(Math.random() * 5))
    }

    trendData.value = { dates, attendance, abnormal }
  } finally {
    chartLoading.value = false
  }
}

const resetFilter = () => {
  filterForm.projectId = null
  filterForm.userId = null
  filterForm.status = ''
  dateRange.value = []
  selectedMonth.value = ''
  pagination.page = 1
  loadReports()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadReports()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadReports()
}

const viewDetails = async (row: any) => {
  try {
    const response = await apiGetDetailedReport({
      userId: row.userId,
      date: row.date
    })

    if (response.success) {
      detailRecords.value = response.data || []
      showDetailDialog.value = true
    } else {
      ElMessage.error(response.message || '加载详细记录失败')
    }
  } catch (error) {
    console.error('Load detail records error:', error)
    ElMessage.error('加载详细记录失败')
  }
}

// 查看员工月度详情
const viewEmployeeDetails = async (row: any) => {
  currentEmployee.value = row
  employeeDetailLoading.value = true
  showEmployeeDetailDialog.value = true

  try {
    const [year, month] = selectedMonth.value ? selectedMonth.value.split('-') : [
      new Date().getFullYear().toString(),
      (new Date().getMonth() + 1).toString()
    ]

    const response = await apiGetEmployeeDailyRecords({
      userId: row.userId,
      year,
      month
    })

    if (response.success) {
      const dailyRecords = response.data.dailyRecords || []

      // 转换数据格式以匹配表格显示
      employeeDailyRecords.value = dailyRecords.map(record => {
        // 获取首次签到时间：优先从pairs中获取，如果没有则从unpaired中获取
        let firstCheckIn = null
        let lastCheckOut = null

        if (record.pairs && record.pairs.length > 0) {
          // 从配对记录中获取首次签到和最后签退
          firstCheckIn = record.pairs[0]?.checkIn?.createdAt
          lastCheckOut = record.pairs[record.pairs.length - 1]?.checkOut?.createdAt
        }

        // 如果配对记录中没有，尝试从未配对记录中获取
        if (!firstCheckIn && record.unpaired && record.unpaired.length > 0) {
          const checkInRecords = record.unpaired.filter(u => u.type === 'in')
          if (checkInRecords.length > 0) {
            firstCheckIn = checkInRecords[0].record?.createdAt
          }
        }

        if (!lastCheckOut && record.unpaired && record.unpaired.length > 0) {
          const checkOutRecords = record.unpaired.filter(u => u.type === 'out')
          if (checkOutRecords.length > 0) {
            lastCheckOut = checkOutRecords[checkOutRecords.length - 1].record?.createdAt
          }
        }

        return {
          date: record.date,
          projectName: '多项目', // 后端数据没有项目信息，显示为多项目
          firstCheckIn: firstCheckIn,
          lastCheckOut: lastCheckOut,
          checkInCount: record.checkInCount || 0,
          checkOutCount: record.checkOutCount || 0,
          totalWorkMinutes: record.totalWorkMinutes || 0,
          isAbnormal: record.isAbnormal || false,
          userId: response.data.userId
        }
      })

      // 使用后端返回的汇总数据更新当前员工信息
      if (response.data.summary) {
        const summary = response.data.summary
        currentEmployee.value = {
          ...currentEmployee.value,
          attendanceDays: summary.totalDays || 0,
          normalDays: summary.normalDays || 0,
          abnormalDays: summary.abnormalDays || 0,
          totalWorkHours: summary.totalWorkHours || '0.0'
        }
      }
    } else {
      ElMessage.error(response.message || '加载员工详细记录失败')
      employeeDailyRecords.value = []
    }
  } catch (error) {
    console.error('Load employee daily records error:', error)
    ElMessage.error('加载员工详细记录失败')
    employeeDailyRecords.value = []
  } finally {
    employeeDetailLoading.value = false
  }
}

// 查看员工项目详情
const viewProjectDetails = async (row: any) => {
  currentEmployeeProject.value = row
  projectDetailLoading.value = true
  showProjectDetailDialog.value = true

  try {
    // 获取该员工在该项目的详细考勤记录
    const response = await apiGetAttendanceReport({
      userId: row.userId,
      projectId: row.projectId,
      startDate: dateRange.value?.[0],
      endDate: dateRange.value?.[1],
      page: 1,
      limit: 1000 // 获取所有记录
    })

    if (response.success) {
      projectDailyRecords.value = response.data.records || []
    } else {
      ElMessage.error(response.message || '加载项目详细记录失败')
      projectDailyRecords.value = []
    }
  } catch (error) {
    console.error('Load project daily records error:', error)
    ElMessage.error('加载项目详细记录失败')
    projectDailyRecords.value = []
  } finally {
    projectDetailLoading.value = false
  }
}

// 查看某天的详细打卡记录
const viewDayDetails = async (row: any) => {
  try {
    const response = await apiGetDetailedReport({
      userId: row.userId || currentEmployee.value?.userId || currentEmployeeProject.value?.userId,
      date: row.date
    })

    if (response.success) {
      detailRecords.value = response.data || []
      showDetailDialog.value = true
    } else {
      ElMessage.error(response.message || '加载详细打卡记录失败')
    }
  } catch (error) {
    console.error('Load day detail records error:', error)
    ElMessage.error('加载详细打卡记录失败')
  }
}

// 导出命令处理
const handleExportCommand = (command: string) => {
  if (command === 'custom') {
    openCustomExportDialog()
  } else {
    quickExport(command)
  }
}

// 员工详情导出处理
const handleEmployeeExport = async (command: string) => {
  if (!currentEmployee.value || !employeeDailyRecords.value.length) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  const [year, month] = selectedMonth.value ? selectedMonth.value.split('-') : [
    new Date().getFullYear().toString(),
    (new Date().getMonth() + 1).toString()
  ]

  const filename = `${currentEmployee.value.userName}_${year}年${month}月考勤详情_${new Date().toISOString().split('T')[0].replace(/-/g, '')}`

  exportLoading.value = true
  try {
    await performEmployeeExport({
      format: command,
      employee: currentEmployee.value,
      dailyRecords: employeeDailyRecords.value,
      year,
      month,
      filename
    })
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Export error:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 项目详情导出处理
const handleProjectExport = async (command: string) => {
  if (!currentEmployeeProject.value || !projectDailyRecords.value.length) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  const startDate = dateRange.value?.[0] || ''
  const endDate = dateRange.value?.[1] || ''
  const dateRangeStr = startDate && endDate ?
    `${startDate.replace(/-/g, '')}_${endDate.replace(/-/g, '')}` :
    new Date().toISOString().split('T')[0].replace(/-/g, '')

  const filename = `${currentEmployeeProject.value.userName}_${currentEmployeeProject.value.projectName}_项目考勤详情_${dateRangeStr}`

  exportLoading.value = true
  try {
    await performProjectExport({
      format: command,
      employeeProject: currentEmployeeProject.value,
      dailyRecords: projectDailyRecords.value,
      startDate,
      endDate,
      filename
    })
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Export error:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 快速导出
const quickExport = async (format: string) => {
  const filename = generateFilename(format)

  exportLoading.value = true
  try {
    await performExport({
      format,
      content: 'filtered',
      fields: getDefaultFields(),
      filename,
      includeSummary: true,
      includeCharts: format === 'excel',
      pdfMethod: 'html2canvas', // 快速导出默认使用HTML转图片
      pdfLanguage: 'english'
    })
  } finally {
    exportLoading.value = false
  }
}

// 打开自定义导出对话框
const openCustomExportDialog = () => {
  // 重置表单
  exportForm.format = 'excel'
  exportForm.content = 'filtered'
  exportForm.fields = getDefaultFields()
  exportForm.filename = generateFilename('excel')
  exportForm.includeSummary = true
  exportForm.includeCharts = false
  exportForm.pdfMethod = 'html2canvas'
  exportForm.pdfLanguage = 'english'

  showExportDialog.value = true
}

// 执行自定义导出
const executeExport = async () => {
  if (!exportForm.filename.trim()) {
    ElMessage.warning('请输入文件名')
    return
  }

  if (exportForm.fields.length === 0) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }

  exportLoading.value = true
  try {
    await performExport(exportForm)
    showExportDialog.value = false
  } finally {
    exportLoading.value = false
  }
}

// 执行导出
const performExport = async (options: any) => {
  try {
    // 准备导出数据
    const exportData = await prepareExportData(options)

    // 检查数据是否为空
    if (!exportData.records || exportData.records.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 优先使用前端导出（更可靠）
    await clientSideExport(exportData, options)

    // 可选：同时调用后端导出API记录日志
    try {
      await apiExportReport({
        type: viewMode.value,
        format: options.format,
        recordCount: exportData.records.length,
        filters: {
          startDate: dateRange.value?.[0],
          endDate: dateRange.value?.[1],
          selectedMonth: selectedMonth.value,
          projectId: filterForm.projectId,
          userId: filterForm.userId,
          status: filterForm.status
        }
      })
    } catch (apiError) {
      // 后端API调用失败不影响前端导出
      console.warn('Backend export API call failed:', apiError)
    }

  } catch (error) {
    console.error('Export error:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 执行员工详情导出
const performEmployeeExport = async (options: any) => {
  try {
    const { employee, dailyRecords, year, month, format, filename } = options

    // 准备员工详情导出数据
    const exportData = {
      records: dailyRecords,
      summary: {
        employeeName: employee.userName,
        year,
        month,
        attendanceDays: employee.attendanceDays || 0,
        normalDays: employee.normalDays || 0,
        abnormalDays: employee.abnormalDays || 0,
        totalWorkHours: employee.totalWorkHours || 0,
        avgWorkHours: employee.avgWorkHours || 0,
        attendanceRate: employee.attendanceRate || 0
      },
      metadata: {
        exportTime: new Date().toISOString(),
        viewMode: 'employee-detail',
        totalRecords: dailyRecords.length,
        filters: {
          employeeName: employee.userName,
          year,
          month
        }
      }
    }

    // 检查数据是否为空
    if (!exportData.records || exportData.records.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 执行前端导出
    await clientSideEmployeeExport(exportData, { format, filename })

  } catch (error) {
    console.error('Employee export error:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 执行项目详情导出
const performProjectExport = async (options: any) => {
  try {
    const { employeeProject, dailyRecords, startDate, endDate, format, filename } = options

    // 准备项目详情导出数据
    const exportData = {
      records: dailyRecords,
      summary: {
        employeeName: employeeProject.userName,
        projectName: employeeProject.projectName,
        clientName: employeeProject.clientName,
        role: employeeProject.role,
        startDate,
        endDate,
        attendanceDays: employeeProject.attendanceDays || 0,
        totalWorkHours: employeeProject.totalWorkHours || 0,
        avgWorkHours: employeeProject.avgWorkHours || 0,
        attendanceRate: employeeProject.attendanceRate || 0
      },
      metadata: {
        exportTime: new Date().toISOString(),
        viewMode: 'project-detail',
        totalRecords: dailyRecords.length,
        filters: {
          employeeName: employeeProject.userName,
          projectName: employeeProject.projectName,
          startDate,
          endDate
        }
      }
    }

    // 检查数据是否为空
    if (!exportData.records || exportData.records.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 执行前端导出
    await clientSideProjectExport(exportData, { format, filename })

  } catch (error) {
    console.error('Project export error:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 准备导出数据
const prepareExportData = async (options: any) => {
  let data = []
  let summaryData = null

  if (options.content === 'current') {
    // 当前页面数据
    if (viewMode.value === 'daily') {
      data = reportData.value
    } else if (viewMode.value === 'monthly') {
      data = monthlyData.value
    } else if (viewMode.value === 'employee-project') {
      data = employeeProjectData.value
    }
  } else if (options.content === 'all' || options.content === 'filtered') {
    // 获取全部数据或筛选后的数据
    if (viewMode.value === 'daily') {
      const response = await apiGetAttendanceReport({
        projectId: filterForm.projectId,
        userId: filterForm.userId,
        startDate: dateRange.value?.[0],
        endDate: dateRange.value?.[1],
        page: 1,
        limit: 10000 // 获取大量数据
      })
      data = response.success ? response.data.records || [] : []
    } else if (viewMode.value === 'monthly') {
      const [year, month] = selectedMonth.value ? selectedMonth.value.split('-') : [
        new Date().getFullYear().toString(),
        (new Date().getMonth() + 1).toString()
      ]
      const response = await apiGetMonthlyAttendance({
        year,
        month,
        projectId: filterForm.projectId,
        userId: filterForm.userId
      })
      data = response.success ? response.data.employees || [] : []
      summaryData = response.success ? response.data.summary : null
    } else if (viewMode.value === 'employee-project') {
      const response = await apiGetEmployeeProjectAttendance({
        projectId: filterForm.projectId,
        userId: filterForm.userId,
        startDate: dateRange.value?.[0],
        endDate: dateRange.value?.[1],
        page: 1,
        limit: 10000
      })
      data = response.success ? response.data.records || [] : []
    }
  }

  // 过滤字段
  const filteredData = data.map(item => {
    const filtered = {}
    options.fields.forEach(field => {
      filtered[field] = item[field]
    })
    return filtered
  })

  return {
    records: filteredData,
    summary: options.includeSummary ? (summaryData || summary) : null,
    metadata: {
      viewMode: viewMode.value,
      exportTime: new Date().toISOString(),
      totalRecords: filteredData.length,
      filters: {
        dateRange: dateRange.value,
        selectedMonth: selectedMonth.value,
        projectId: filterForm.projectId,
        userId: filterForm.userId,
        status: filterForm.status
      }
    }
  }
}

const loadProjects = async () => {
  try {
    const response = await apiGetProjects()
    if (response.success) {
      projects.value = response.data?.projects || []
    } else {
      ElMessage.error(response.message || '加载工程列表失败')
    }
  } catch (error) {
    console.error('Load projects error:', error)
    ElMessage.error('加载工程列表失败')
  }
}

const loadUsers = async () => {
  try {
    const response = await apiGetUsers()
    if (response.success) {
      users.value = response.data?.users || []
    } else {
      ElMessage.error(response.message || '加载用户列表失败')
    }
  } catch (error) {
    console.error('Load users error:', error)
    ElMessage.error('加载用户列表失败')
  }
}

// 获取默认导出字段
const getDefaultFields = () => {
  if (viewMode.value === 'daily') {
    return ['date', 'userName', 'projectName', 'firstCheckIn', 'lastCheckOut', 'totalWorkMinutes', 'isAbnormal']
  } else if (viewMode.value === 'monthly') {
    return ['userName', 'department', 'attendanceDays', 'totalWorkHours', 'attendanceRate']
  } else if (viewMode.value === 'employee-project') {
    return ['userName', 'projectName', 'clientName', 'attendanceDays', 'totalWorkHours', 'avgWorkHours', 'attendanceRate']
  }
  return []
}

// 生成文件名
const generateFilename = (format: string) => {
  const now = new Date()
  const dateStr = now.toISOString().split('T')[0]
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '')

  let prefix = ''
  if (viewMode.value === 'daily') {
    prefix = '考勤明细'
  } else if (viewMode.value === 'monthly') {
    prefix = '月度统计'
  } else if (viewMode.value === 'employee-project') {
    prefix = '员工项目统计'
  }

  return `${prefix}_${dateStr}_${timeStr}`
}

// 下载文件
const downloadFile = (url: string, filename: string) => {
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 前端导出
const clientSideExport = async (data: any, options: any) => {
  console.log('开始前端导出:', { format: options.format, recordCount: data.records?.length })

  try {
    if (options.format === 'csv') {
      exportToCSV(data, options)
    } else if (options.format === 'excel') {
      await exportToExcel(data, options)
    } else if (options.format === 'pdf') {
      await exportToPDF(data, options)
    } else {
      throw new Error(`不支持的导出格式: ${options.format}`)
    }
  } catch (error) {
    console.error('前端导出失败:', error)
    throw error
  }
}

// 员工详情前端导出
const clientSideEmployeeExport = async (data: any, options: any) => {
  console.log('开始员工详情导出:', { format: options.format, recordCount: data.records?.length })

  try {
    if (options.format === 'excel') {
      await exportEmployeeToExcel(data, options)
    } else if (options.format === 'pdf') {
      await exportEmployeeToPDF(data, options)
    } else {
      throw new Error(`不支持的导出格式: ${options.format}`)
    }
  } catch (error) {
    console.error('员工详情导出失败:', error)
    throw error
  }
}

// 项目详情前端导出
const clientSideProjectExport = async (data: any, options: any) => {
  console.log('开始项目详情导出:', { format: options.format, recordCount: data.records?.length })

  try {
    if (options.format === 'excel') {
      await exportProjectToExcel(data, options)
    } else if (options.format === 'pdf') {
      await exportProjectToPDF(data, options)
    } else {
      throw new Error(`不支持的导出格式: ${options.format}`)
    }
  } catch (error) {
    console.error('项目详情导出失败:', error)
    throw error
  }
}

// 导出CSV
const exportToCSV = (data: any, options: any) => {
  try {
    const records = data.records || []
    if (records.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    console.log('CSV导出开始:', { recordCount: records.length, fields: options.fields })

    // 生成CSV内容
    const headers = options.fields.map(field => getFieldLabel(field))
    const csvContent = [
      headers.join(','),
      ...records.map(record =>
        options.fields.map(field => {
          let value = record[field] || ''
          if (field === 'totalWorkMinutes') {
            value = formatDuration(value)
          } else if (field === 'isAbnormal') {
            value = value ? '异常' : '正常'
          } else if (field === 'attendanceRate') {
            value = `${value}%`
          } else if (field === 'firstCheckIn' || field === 'lastCheckOut') {
            // 格式化时间显示
            value = value ? formatTime(value) : '-'
          } else if (field === 'date') {
            // 格式化日期显示
            value = value ? new Date(value).toLocaleDateString('zh-CN') : ''
          }
          // 处理包含逗号和引号的值
          if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
            value = `"${value.replace(/"/g, '""')}"`
          } else {
            value = `"${value}"`
          }
          return value
        }).join(',')
      )
    ].join('\n')

    // 下载CSV
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    downloadFile(url, `${options.filename}.csv`)
    URL.revokeObjectURL(url)

    ElMessage.success(`CSV导出成功！共导出 ${records.length} 条记录`)

  } catch (error) {
    console.error('CSV导出失败:', error)
    ElMessage.error('CSV导出失败')
    throw error
  }
}

// 导出Excel
const exportToExcel = async (data: any, options: any) => {
  try {
    const XLSX = await import('xlsx')

    const records = data.records || []
    if (records.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 创建工作簿
    const workbook = XLSX.utils.book_new()

    // 准备数据
    const headers = options.fields.map(field => getFieldLabel(field))
    const excelData = [
      headers,
      ...records.map(record =>
        options.fields.map(field => {
          let value = record[field] || ''
          if (field === 'totalWorkMinutes') {
            value = formatDuration(value)
          } else if (field === 'isAbnormal') {
            value = value ? '异常' : '正常'
          } else if (field === 'attendanceRate') {
            value = `${value}%`
          } else if (field === 'firstCheckIn' || field === 'lastCheckOut') {
            // 格式化时间显示
            value = value ? formatTime(value) : '-'
          } else if (field === 'date') {
            // 格式化日期显示
            value = value ? new Date(value).toLocaleDateString('zh-CN') : ''
          }
          return value
        })
      )
    ]

    // 创建主数据工作表
    const worksheet = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = options.fields.map(field => {
      if (field === 'date') return { wch: 12 }
      if (field === 'userName') return { wch: 10 }
      if (field === 'projectName') return { wch: 20 }
      if (field === 'firstCheckIn' || field === 'lastCheckOut') return { wch: 15 }
      if (field === 'totalWorkMinutes') return { wch: 12 }
      return { wch: 10 }
    })
    worksheet['!cols'] = colWidths

    // 添加主数据工作表
    XLSX.utils.book_append_sheet(workbook, worksheet, '考勤数据')

    // 如果包含汇总，创建汇总工作表
    if (options.includeSummary && data.summary) {
      const summaryData = [
        ['统计项目', '数值'],
        ['总天数', data.summary.totalDays || 0],
        ['正常天数', data.summary.normalDays || 0],
        ['异常天数', data.summary.abnormalDays || 0],
        ['总工时', `${data.summary.totalHours || 0}小时`]
      ]

      if (viewMode.value === 'monthly' && data.summary.totalEmployees) {
        summaryData.push(['总员工数', data.summary.totalEmployees])
        summaryData.push(['平均出勤率', `${data.summary.avgAttendanceRate || 0}%`])
      }

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData)
      summarySheet['!cols'] = [{ wch: 15 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(workbook, summarySheet, '统计汇总')
    }

    // 添加元数据工作表
    if (data.metadata) {
      const metaData = [
        ['导出信息', ''],
        ['导出时间', new Date(data.metadata.exportTime).toLocaleString('zh-CN')],
        ['数据类型', data.metadata.viewMode === 'daily' ? '按日统计' :
                    data.metadata.viewMode === 'monthly' ? '按月统计' : '员工项目统计'],
        ['记录总数', data.metadata.totalRecords],
        ['', ''],
        ['筛选条件', ''],
        ['时间范围', data.metadata.filters.dateRange?.join(' 至 ') || '全部'],
        ['选择月份', data.metadata.filters.selectedMonth || '全部'],
        ['工程筛选', data.metadata.filters.projectId ? '已筛选' : '全部'],
        ['用户筛选', data.metadata.filters.userId ? '已筛选' : '全部'],
        ['状态筛选', data.metadata.filters.status || '全部']
      ]

      const metaSheet = XLSX.utils.aoa_to_sheet(metaData)
      metaSheet['!cols'] = [{ wch: 12 }, { wch: 25 }]
      XLSX.utils.book_append_sheet(workbook, metaSheet, '导出信息')
    }

    // 导出文件
    XLSX.writeFile(workbook, `${options.filename}.xlsx`)
    ElMessage.success('Excel导出成功！')

  } catch (error) {
    console.error('Excel export error:', error)
    ElMessage.error('Excel导出失败，已改为CSV格式')
    exportToCSV(data, options)
  }
}

// 导出PDF - 支持HTML转图片和文本两种方式
const exportToPDF = async (data: any, options: any) => {
  try {
    console.log('PDF导出开始:', { options, recordCount: data.records?.length })

    const records = data.records || []
    if (records.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 根据选择的方式导出
    if (options.pdfMethod === 'html2canvas') {
      await exportToPDFWithHTML2Canvas(data, options)
    } else {
      await exportToPDFWithText(data, options)
    }

  } catch (error) {
    console.error('PDF export error:', error)
    ElMessage.error('PDF导出失败，已改为CSV格式')
    exportToCSV(data, options)
  }
}

// 使用HTML2Canvas方式导出PDF（完美支持中文）
const exportToPDFWithHTML2Canvas = async (data: any, options: any) => {
  const html2canvas = (await import('html2canvas')).default
  const jsPDF = (await import('jspdf')).default

  // 创建临时HTML内容
  const htmlContent = createHTMLForPDF(data, options)

  // 创建临时DOM元素
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlContent
  tempDiv.style.position = 'absolute'
  tempDiv.style.left = '-9999px'
  tempDiv.style.top = '-9999px'
  tempDiv.style.width = '1200px' // 设置固定宽度
  tempDiv.style.backgroundColor = 'white'
  tempDiv.style.padding = '20px'
  tempDiv.style.fontFamily = 'Arial, "Microsoft YaHei", "SimHei", sans-serif'
  document.body.appendChild(tempDiv)

  try {
    // 使用html2canvas转换为图片
    const canvas = await html2canvas(tempDiv, {
      scale: 2, // 提高清晰度
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    })

    // 创建PDF
    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    })

    const imgData = canvas.toDataURL('image/png')
    const imgWidth = 277 // A4横向宽度 - 边距
    const imgHeight = (canvas.height * imgWidth) / canvas.width

    // 如果内容高度超过一页，需要分页
    const pageHeight = 190 // A4横向高度 - 边距
    let heightLeft = imgHeight
    let position = 10

    // 添加第一页
    pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight)
    heightLeft -= pageHeight

    // 如果需要分页
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight + 10
      pdf.addPage()
      pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight)
      heightLeft -= pageHeight
    }

    // 保存PDF
    pdf.save(`${options.filename}.pdf`)
    ElMessage.success('PDF导出成功！（HTML转图片方式，完美支持中文）')

  } finally {
    // 清理临时DOM元素
    document.body.removeChild(tempDiv)
  }
}

// 创建用于PDF的HTML内容
const createHTMLForPDF = (data: any, options: any) => {
  const records = data.records || []

  // 生成标题
  const titleMap = {
    'daily': '考勤明细报表',
    'monthly': '月度统计报表',
    'employee-project': '员工项目统计报表'
  }
  const title = titleMap[viewMode.value] || '考勤报表'

  // 生成表格头部
  const headers = options.fields.map(field => getFieldLabel(field))
  const headerRow = headers.map(header => `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: center;">${header}</th>`).join('')

  // 生成表格数据行
  const dataRows = records.map(record => {
    const cells = options.fields.map(field => {
      let value = record[field] || ''
      if (field === 'totalWorkMinutes') {
        value = formatDuration(value)
      } else if (field === 'isAbnormal') {
        value = value ? '异常' : '正常'
      } else if (field === 'attendanceRate') {
        value = `${value}%`
      } else if (field === 'firstCheckIn' || field === 'lastCheckOut') {
        // 格式化时间显示
        value = value ? formatTime(value) : '-'
      } else if (field === 'date') {
        // 格式化日期显示
        value = value ? new Date(value).toLocaleDateString('zh-CN') : ''
      }
      return `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${value}</td>`
    }).join('')
    return `<tr>${cells}</tr>`
  }).join('')

  // 生成汇总信息
  let summaryHTML = ''
  if (options.includeSummary && data.summary) {
    summaryHTML = `
      <div style="margin-top: 30px;">
        <h3 style="color: #333; margin-bottom: 15px;">统计汇总</h3>
        <table style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; width: 150px;"><strong>总天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${data.summary.totalDays || 0}</td>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; width: 150px;"><strong>正常天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${data.summary.normalDays || 0}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>异常天数</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${data.summary.abnormalDays || 0}</td>
            <td style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;"><strong>总工时</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${data.summary.totalHours || 0}小时</td>
          </tr>
        </table>
      </div>
    `
  }

  return `
    <div style="font-family: Arial, 'Microsoft YaHei', 'SimHei', sans-serif; color: #333;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333; margin: 0; font-size: 24px;">${title}</h1>
        <p style="color: #666; margin: 10px 0; font-size: 14px;">导出时间: ${new Date().toLocaleString('zh-CN')}</p>
      </div>

      <table style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
        <thead>
          <tr>${headerRow}</tr>
        </thead>
        <tbody>
          ${dataRows}
        </tbody>
      </table>

      ${summaryHTML}

      <div style="margin-top: 30px; text-align: center; color: #999; font-size: 12px;">
        <p>共 ${records.length} 条记录</p>
      </div>
    </div>
  `
}

// 使用文本方式导出PDF（原有方式）
const exportToPDFWithText = async (data: any, options: any) => {
  const jsPDF = (await import('jspdf')).default

  const records = data.records || []

  // 创建PDF文档
  const doc = new jsPDF({
    orientation: 'landscape', // 横向
    unit: 'mm',
    format: 'a4'
  })

  // 使用默认字体
  doc.setFont('helvetica')

  // 根据用户选择的语言设置标题和内容
  const useEnglish = options.pdfLanguage === 'english'

  // 添加标题
  doc.setFontSize(16)
  let title = ''
  if (useEnglish) {
    const titleMap = {
      'daily': 'Daily Attendance Report',
      'monthly': 'Monthly Attendance Report',
      'employee-project': 'Employee Project Report'
    }
    title = titleMap[viewMode.value] || 'Attendance Report'
  } else {
    title = viewMode.value === 'daily' ? '考勤明细报表' :
            viewMode.value === 'monthly' ? '月度统计报表' : '员工项目统计报表'
  }
  doc.text(title, 20, 20)

  // 添加导出时间
  doc.setFontSize(10)
  const exportTimeText = useEnglish ?
    `Export Time: ${new Date().toISOString().replace('T', ' ').substring(0, 19)}` :
    `导出时间: ${new Date().toLocaleString('zh-CN')}`
  doc.text(exportTimeText, 20, 30)

  // 准备表格数据
  const headers = useEnglish ?
    options.fields.map(field => getEnglishFieldLabel(field)) :
    options.fields.map(field => getFieldLabel(field))

  const tableData = records.map(record =>
    options.fields.map(field => {
      let value = record[field] || ''
      if (field === 'totalWorkMinutes') {
        value = formatDuration(value)
      } else if (field === 'isAbnormal') {
        value = useEnglish ? (value ? 'Abnormal' : 'Normal') : (value ? '异常' : '正常')
      } else if (field === 'attendanceRate') {
        value = `${value}%`
      } else if (field === 'firstCheckIn' || field === 'lastCheckOut') {
        // 格式化时间显示
        value = value ? formatTime(value) : '-'
      } else if (field === 'date') {
        // 格式化日期显示
        value = value ? new Date(value).toLocaleDateString(useEnglish ? 'en-US' : 'zh-CN') : ''
      }

      // 如果选择英文，转换中文内容
      if (useEnglish && typeof value === 'string') {
        value = convertChineseToEnglish(value)
      }

      return String(value)
    })
  )

  // 添加主表格
  const autoTableModule = (await import('jspdf-autotable')).default
  autoTableModule(doc, {
    head: [headers],
    body: tableData,
    startY: 40,
    styles: {
      fontSize: 8,
      cellPadding: 2,
      font: 'helvetica'
    },
    headStyles: {
      fillColor: [64, 158, 255],
      textColor: 255,
      font: 'helvetica',
      fontStyle: 'bold'
    },
    alternateRowStyles: {
      fillColor: [245, 245, 245]
    },
    margin: { top: 40, left: 20, right: 20 }
  })

  // 如果包含汇总，添加汇总表格
  if (options.includeSummary && data.summary) {
    const finalY = (doc as any).lastAutoTable.finalY + 20

    doc.setFontSize(12)
    const summaryTitle = useEnglish ? 'Summary Statistics' : '统计汇总'
    doc.text(summaryTitle, 20, finalY)

    const summaryData = useEnglish ? [
      ['Total Days', String(data.summary.totalDays || 0)],
      ['Normal Days', String(data.summary.normalDays || 0)],
      ['Abnormal Days', String(data.summary.abnormalDays || 0)],
      ['Total Hours', `${data.summary.totalHours || 0}h`]
    ] : [
      ['总天数', String(data.summary.totalDays || 0)],
      ['正常天数', String(data.summary.normalDays || 0)],
      ['异常天数', String(data.summary.abnormalDays || 0)],
      ['总工时', `${data.summary.totalHours || 0}小时`]
    ]

    if (viewMode.value === 'monthly' && data.summary.totalEmployees) {
      if (useEnglish) {
        summaryData.push(['Total Employees', String(data.summary.totalEmployees)])
        summaryData.push(['Avg Attendance Rate', `${data.summary.avgAttendanceRate || 0}%`])
      } else {
        summaryData.push(['总员工数', String(data.summary.totalEmployees)])
        summaryData.push(['平均出勤率', `${data.summary.avgAttendanceRate || 0}%`])
      }
    }

    const summaryHeaders = useEnglish ? ['Item', 'Value'] : ['统计项目', '数值']

    autoTableModule(doc, {
      head: [summaryHeaders],
      body: summaryData,
      startY: finalY + 10,
      styles: {
        fontSize: 10,
        cellPadding: 3,
        font: 'helvetica'
      },
      headStyles: {
        fillColor: [67, 194, 58],
        textColor: 255,
        font: 'helvetica',
        fontStyle: 'bold'
      },
      margin: { left: 20 }
    })
  }

  // 保存PDF
  doc.save(`${options.filename}.pdf`)
  const successMsg = useEnglish ?
    'PDF导出成功！（文本模式-英文版本）' :
    'PDF导出成功！（文本模式-中文版本，可能存在字体显示问题）'
  ElMessage.success(successMsg)
}

// 获取字段标签
const getFieldLabel = (field: string) => {
  const labels = {
    date: '日期',
    userName: '姓名',
    projectName: '工程',
    firstCheckIn: '首次签到',
    lastCheckOut: '最后签退',
    checkInCount: '签到次数',
    checkOutCount: '签退次数',
    totalWorkMinutes: '工作时长',
    isAbnormal: '状态',
    department: '部门',
    attendanceDays: '出勤天数',
    absentDays: '缺勤天数',
    abnormalDays: '异常天数',
    totalWorkHours: '总工时',
    avgWorkHours: '平均工时',
    attendanceRate: '出勤率',
    clientName: '客户',
    role: '角色'
  }
  return labels[field] || field
}

// 获取英文字段标签（用于PDF导出）
const getEnglishFieldLabel = (field: string) => {
  const labels = {
    date: 'Date',
    userName: 'Name',
    projectName: 'Project',
    firstCheckIn: 'First Check-in',
    lastCheckOut: 'Last Check-out',
    checkInCount: 'Check-in Count',
    checkOutCount: 'Check-out Count',
    totalWorkMinutes: 'Work Duration',
    isAbnormal: 'Status',
    department: 'Department',
    attendanceDays: 'Attendance Days',
    absentDays: 'Absent Days',
    abnormalDays: 'Abnormal Days',
    totalWorkHours: 'Total Hours',
    avgWorkHours: 'Avg Hours',
    attendanceRate: 'Attendance Rate',
    clientName: 'Client',
    role: 'Role'
  }
  return labels[field] || field
}

// 将中文内容转换为英文或拼音（用于PDF导出）
const convertChineseToEnglish = (text: string) => {
  if (!text || typeof text !== 'string') return text

  // 常见中文词汇映射
  const chineseToEnglish = {
    // 人名映射（可以根据实际情况扩展）
    '张经理': 'Zhang Manager',
    '李工程师': 'Li Engineer',
    '王主管': 'Wang Supervisor',

    // 项目名称映射
    'CBD商业中心建设': 'CBD Commercial Center Construction',
    '住宅小区项目': 'Residential Community Project',
    '办公楼建设': 'Office Building Construction',

    // 部门映射
    '工程部': 'Engineering Dept',
    '管理部': 'Management Dept',
    '财务部': 'Finance Dept',
    '人事部': 'HR Dept',

    // 角色映射
    '项目经理': 'Project Manager',
    '工程师': 'Engineer',
    '技术员': 'Technician',
    '管理员': 'Administrator',
    '员工': 'Employee',

    // 客户映射
    '恒大集团': 'Evergrande Group',
    '万科集团': 'Vanke Group',
    '碧桂园': 'Country Garden',

    // 状态映射
    '正常': 'Normal',
    '异常': 'Abnormal',
    '迟到': 'Late',
    '早退': 'Early Leave',
    '缺勤': 'Absent'
  }

  // 先尝试直接映射
  if (chineseToEnglish[text]) {
    return chineseToEnglish[text]
  }

  // 如果没有直接映射，尝试部分替换
  let result = text
  Object.entries(chineseToEnglish).forEach(([chinese, english]) => {
    result = result.replace(new RegExp(chinese, 'g'), english)
  })

  // 如果仍然包含中文，使用简单的编码方式
  if (/[\u4e00-\u9fa5]/.test(result)) {
    // 将剩余的中文字符替换为拼音首字母或编码
    result = result.replace(/[\u4e00-\u9fa5]/g, (char) => {
      // 这里可以集成拼音库，暂时使用Unicode编码
      return `U${char.charCodeAt(0).toString(16).toUpperCase()}`
    })
  }

  return result
}

// 初始化默认月份
const initializeDefaultMonth = () => {
  const now = new Date()
  selectedMonth.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
}

onMounted(() => {
  initializeDefaultMonth()
  loadRealtimeData()
  loadProjects()
  loadUsers()
  loadReports()

  // 每30秒刷新实时数据
  setInterval(loadRealtimeData, 30000)
})

// 重新计算当前月份数据
const recalculateCurrentMonthData = async () => {
  recalculateLoading.value = true
  try {
    // 获取当前选择的月份，如果没有选择则使用当前月份
    const [year, month] = selectedMonth.value ? selectedMonth.value.split('-') : [
      new Date().getFullYear().toString(),
      (new Date().getMonth() + 1).toString()
    ]

    // 构建该月的开始和结束日期
    const startDate = `${year}-${String(month).padStart(2, '0')}-01`
    const endDate = new Date(parseInt(year), parseInt(month), 0).toISOString().split('T')[0] // 月末日期

    const response = await apiRecalculateAttendance({
      startDate,
      endDate
    })

    if (response.success) {
      ElMessage.success(`${year}年${month}月考勤数据重新计算完成！`)
      // 重新加载当前页面数据
      await loadReports()
    } else {
      ElMessage.error(response.message || '重新计算失败')
    }
  } catch (error) {
    console.error('Recalculate error:', error)
    ElMessage.error('重新计算失败，请重试')
  } finally {
    recalculateLoading.value = false
  }
}

// 员工详情导出到Excel
const exportEmployeeToExcel = async (data: any, options: any) => {
  try {
    const XLSX = await import('xlsx')
    const { summary, records } = data

    // 创建工作簿
    const workbook = XLSX.utils.book_new()

    // 创建主工作表 - 包含汇总和详细记录
    const sheetData = []

    // 标题行
    sheetData.push(['员工考勤详情报表'])
    sheetData.push([]) // 空行

    // 员工基本信息
    sheetData.push(['员工姓名', summary.employeeName])
    sheetData.push(['统计月份', `${summary.year}年${summary.month}月`])
    sheetData.push(['出勤天数', summary.attendanceDays])
    sheetData.push(['正常天数', summary.normalDays])
    sheetData.push(['异常天数', summary.abnormalDays])
    sheetData.push(['总工时', `${summary.totalWorkHours}小时`])
    sheetData.push(['平均工时', `${summary.avgWorkHours}小时/天`])
    sheetData.push(['出勤率', `${summary.attendanceRate}%`])
    sheetData.push([]) // 空行
    sheetData.push(['导出时间', new Date().toLocaleString('zh-CN')])
    sheetData.push([]) // 空行
    sheetData.push([]) // 空行

    // 每日详细记录表格
    if (records && records.length > 0) {
      sheetData.push(['每日详细考勤记录'])
      sheetData.push([]) // 空行

      // 表头
      const headers = ['日期', '项目名称', '首次签到', '最后签退', '签到次数', '签退次数', '工作时长', '状态']
      sheetData.push(headers)

      // 数据行
      records.forEach(record => {
        sheetData.push([
          record.date || '',
          record.projectName || '未分配项目',
          record.firstCheckIn ? formatTime(record.firstCheckIn) : '-',
          record.lastCheckOut ? formatTime(record.lastCheckOut) : '-',
          record.checkInCount || 0,
          record.checkOutCount || 0,
          formatDuration(record.totalWorkMinutes || 0),
          record.isAbnormal ? '异常' : '正常'
        ])
      })
    }

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(sheetData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 20 }, // 项目名称
      { wch: 12 }, // 首次签到
      { wch: 12 }, // 最后签退
      { wch: 10 }, // 签到次数
      { wch: 10 }, // 签退次数
      { wch: 12 }, // 工作时长
      { wch: 8 }   // 状态
    ]
    worksheet['!cols'] = colWidths

    // 设置单元格样式（标题行）
    if (worksheet['A1']) {
      worksheet['A1'].s = {
        font: { bold: true, sz: 16 },
        alignment: { horizontal: 'center' }
      }
    }

    // 合并标题单元格
    worksheet['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } } // 合并标题行
    ]

    XLSX.utils.book_append_sheet(workbook, worksheet, '考勤详情')

    // 导出文件
    XLSX.writeFile(workbook, `${options.filename}.xlsx`)
    console.log('员工Excel导出完成')

  } catch (error) {
    console.error('员工Excel导出失败:', error)
    throw error
  }
}

// 员工详情导出到PDF
const exportEmployeeToPDF = async (data: any, options: any) => {
  try {
    const jsPDF = await import('jspdf')
    const doc = new jsPDF.default('p', 'mm', 'a4')

    const { summary, records } = data
    let yPos = 20

    // 标题
    doc.setFontSize(18)
    doc.setFont('helvetica', 'bold')
    const title = `${summary.employeeName} - ${summary.year}年${summary.month}月考勤详情`
    const titleWidth = doc.getTextWidth(title)
    doc.text(title, (210 - titleWidth) / 2, yPos) // 居中显示
    yPos += 20

    // 分割线
    doc.setLineWidth(0.5)
    doc.line(20, yPos, 190, yPos)
    yPos += 15

    // 员工基本信息 - 使用表格布局
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text('考勤汇总信息', 20, yPos)
    yPos += 10

    doc.setFont('helvetica', 'normal')
    doc.setFontSize(10)

    // 创建信息表格
    const infoData = [
      ['出勤天数', `${summary.attendanceDays}天`, '正常天数', `${summary.normalDays}天`],
      ['异常天数', `${summary.abnormalDays}天`, '出勤率', `${summary.attendanceRate}%`],
      ['总工时', `${summary.totalWorkHours}小时`, '平均工时', `${summary.avgWorkHours}小时/天`]
    ]

    // 绘制信息表格
    const cellWidth = 42
    const cellHeight = 8
    let startX = 20

    infoData.forEach((row, rowIndex) => {
      let currentX = startX
      row.forEach((cell, cellIndex) => {
        // 绘制边框
        doc.rect(currentX, yPos, cellWidth, cellHeight)

        // 填充背景色（标签列）
        if (cellIndex % 2 === 0) {
          doc.setFillColor(240, 240, 240)
          doc.rect(currentX, yPos, cellWidth, cellHeight, 'F')
        }

        // 添加文本
        doc.text(cell, currentX + 2, yPos + 5)
        currentX += cellWidth
      })
      yPos += cellHeight
    })

    yPos += 15

    // 每日详细记录表格
    if (records && records.length > 0) {
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.text('每日详细考勤记录', 20, yPos)
      yPos += 10

      // 表格头
      doc.setFontSize(9)
      doc.setFont('helvetica', 'bold')

      const headers = ['日期', '项目', '首次签到', '最后签退', '签到次数', '签退次数', '工作时长', '状态']
      const colWidths = [22, 30, 20, 20, 16, 16, 20, 16]
      let currentX = 20

      // 绘制表头
      doc.setFillColor(220, 220, 220)
      headers.forEach((header, index) => {
        doc.rect(currentX, yPos, colWidths[index], 8, 'F')
        doc.rect(currentX, yPos, colWidths[index], 8)
        doc.text(header, currentX + 1, yPos + 5)
        currentX += colWidths[index]
      })
      yPos += 8

      // 表格数据
      doc.setFont('helvetica', 'normal')
      doc.setFontSize(8)

      records.forEach((record, index) => {
        // 检查是否需要换页
        if (yPos > 270) {
          doc.addPage()
          yPos = 20

          // 重新绘制表头
          doc.setFontSize(9)
          doc.setFont('helvetica', 'bold')
          currentX = 20
          doc.setFillColor(220, 220, 220)
          headers.forEach((header, index) => {
            doc.rect(currentX, yPos, colWidths[index], 8, 'F')
            doc.rect(currentX, yPos, colWidths[index], 8)
            doc.text(header, currentX + 1, yPos + 5)
            currentX += colWidths[index]
          })
          yPos += 8
          doc.setFont('helvetica', 'normal')
          doc.setFontSize(8)
        }

        currentX = 20
        const rowData = [
          record.date || '',
          (record.projectName || '未分配').substring(0, 12),
          record.firstCheckIn ? formatTime(record.firstCheckIn) : '-',
          record.lastCheckOut ? formatTime(record.lastCheckOut) : '-',
          (record.checkInCount || 0).toString(),
          (record.checkOutCount || 0).toString(),
          formatDuration(record.totalWorkMinutes || 0),
          record.isAbnormal ? '异常' : '正常'
        ]

        // 交替行背景色
        if (index % 2 === 1) {
          doc.setFillColor(248, 248, 248)
          doc.rect(20, yPos, 160, 6, 'F')
        }

        // 绘制数据行
        rowData.forEach((cell, cellIndex) => {
          doc.rect(currentX, yPos, colWidths[cellIndex], 6)

          // 状态列特殊颜色
          if (cellIndex === 7 && cell === '异常') {
            doc.setTextColor(255, 0, 0) // 红色
          } else if (cellIndex === 7 && cell === '正常') {
            doc.setTextColor(0, 128, 0) // 绿色
          } else {
            doc.setTextColor(0, 0, 0) // 黑色
          }

          doc.text(cell, currentX + 1, yPos + 4)
          currentX += colWidths[cellIndex]
        })

        doc.setTextColor(0, 0, 0) // 重置颜色
        yPos += 6
      })
    }

    // 页脚信息
    const pageCount = doc.internal.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i)
      doc.setFontSize(8)
      doc.setTextColor(128, 128, 128)
      doc.text(`导出时间: ${new Date().toLocaleString('zh-CN')}`, 20, 290)
      doc.text(`第 ${i} 页，共 ${pageCount} 页`, 160, 290)
    }

    // 保存文件
    doc.save(`${options.filename}.pdf`)
    console.log('员工PDF导出完成')

  } catch (error) {
    console.error('员工PDF导出失败:', error)
    throw error
  }
}

// 项目详情导出到Excel
const exportProjectToExcel = async (data: any, options: any) => {
  try {
    const XLSX = await import('xlsx')
    const { summary, records } = data

    // 创建工作簿
    const workbook = XLSX.utils.book_new()

    // 创建主工作表 - 包含汇总和详细记录
    const sheetData = []

    // 标题行
    sheetData.push(['员工项目考勤详情报表'])
    sheetData.push([]) // 空行

    // 项目基本信息
    sheetData.push(['员工姓名', summary.employeeName])
    sheetData.push(['项目名称', summary.projectName])
    sheetData.push(['客户名称', summary.clientName])
    sheetData.push(['项目角色', summary.role])
    sheetData.push(['统计时间', `${summary.startDate} 至 ${summary.endDate}`])
    sheetData.push(['出勤天数', summary.attendanceDays])
    sheetData.push(['总工时', `${summary.totalWorkHours}小时`])
    sheetData.push(['平均工时', `${summary.avgWorkHours}小时/天`])
    sheetData.push(['出勤率', `${summary.attendanceRate}%`])
    sheetData.push([]) // 空行
    sheetData.push(['导出时间', new Date().toLocaleString('zh-CN')])
    sheetData.push([]) // 空行
    sheetData.push([]) // 空行

    // 每日详细记录表格
    if (records && records.length > 0) {
      sheetData.push(['每日详细考勤记录'])
      sheetData.push([]) // 空行

      // 表头
      const headers = ['日期', '项目名称', '首次签到', '最后签退', '签到次数', '签退次数', '工作时长', '状态']
      sheetData.push(headers)

      // 数据行
      records.forEach(record => {
        sheetData.push([
          record.date || '',
          record.projectName || '未分配项目',
          record.firstCheckIn ? formatTime(record.firstCheckIn) : '-',
          record.lastCheckOut ? formatTime(record.lastCheckOut) : '-',
          record.checkInCount || 0,
          record.checkOutCount || 0,
          formatDuration(record.totalWorkMinutes || 0),
          record.isAbnormal ? '异常' : '正常'
        ])
      })
    }

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(sheetData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 20 }, // 项目名称
      { wch: 12 }, // 首次签到
      { wch: 12 }, // 最后签退
      { wch: 10 }, // 签到次数
      { wch: 10 }, // 签退次数
      { wch: 12 }, // 工作时长
      { wch: 8 }   // 状态
    ]
    worksheet['!cols'] = colWidths

    // 设置单元格样式（标题行）
    if (worksheet['A1']) {
      worksheet['A1'].s = {
        font: { bold: true, sz: 16 },
        alignment: { horizontal: 'center' }
      }
    }

    // 合并标题单元格
    worksheet['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } } // 合并标题行
    ]

    XLSX.utils.book_append_sheet(workbook, worksheet, '项目考勤详情')

    // 导出文件
    XLSX.writeFile(workbook, `${options.filename}.xlsx`)
    console.log('项目Excel导出完成')

  } catch (error) {
    console.error('项目Excel导出失败:', error)
    throw error
  }
}

// 项目详情导出到PDF
const exportProjectToPDF = async (data: any, options: any) => {
  try {
    const jsPDF = await import('jspdf')
    const doc = new jsPDF.default('p', 'mm', 'a4')

    const { summary, records } = data
    let yPos = 20

    // 标题
    doc.setFontSize(18)
    doc.setFont('helvetica', 'bold')
    const title = `${summary.employeeName} - ${summary.projectName} 项目考勤详情`
    const titleWidth = doc.getTextWidth(title)
    doc.text(title, (210 - titleWidth) / 2, yPos) // 居中显示
    yPos += 20

    // 分割线
    doc.setLineWidth(0.5)
    doc.line(20, yPos, 190, yPos)
    yPos += 15

    // 项目基本信息 - 使用表格布局
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text('项目汇总信息', 20, yPos)
    yPos += 10

    doc.setFont('helvetica', 'normal')
    doc.setFontSize(10)

    // 创建信息表格
    const infoData = [
      ['员工姓名', summary.employeeName, '项目名称', summary.projectName],
      ['客户名称', summary.clientName, '项目角色', summary.role],
      ['统计时间', `${summary.startDate} 至 ${summary.endDate}`, '', ''],
      ['出勤天数', `${summary.attendanceDays}天`, '出勤率', `${summary.attendanceRate}%`],
      ['总工时', `${summary.totalWorkHours}小时`, '平均工时', `${summary.avgWorkHours}小时/天`]
    ]

    // 绘制信息表格
    const cellWidth = 42
    const cellHeight = 8
    let startX = 20

    infoData.forEach((row, rowIndex) => {
      let currentX = startX
      row.forEach((cell, cellIndex) => {
        if (cell) { // 只绘制非空单元格
          // 绘制边框
          doc.rect(currentX, yPos, cellWidth, cellHeight)

          // 填充背景色（标签列）
          if (cellIndex % 2 === 0) {
            doc.setFillColor(240, 240, 240)
            doc.rect(currentX, yPos, cellWidth, cellHeight, 'F')
          }

          // 添加文本
          doc.text(cell, currentX + 2, yPos + 5)
        }
        currentX += cellWidth
      })
      yPos += cellHeight
    })

    yPos += 15

    // 每日详细记录表格（与员工详情导出相同的逻辑）
    if (records && records.length > 0) {
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.text('每日详细考勤记录', 20, yPos)
      yPos += 10

      // 表格头
      doc.setFontSize(9)
      doc.setFont('helvetica', 'bold')

      const headers = ['日期', '项目', '首次签到', '最后签退', '签到次数', '签退次数', '工作时长', '状态']
      const colWidths = [22, 30, 20, 20, 16, 16, 20, 16]
      let currentX = 20

      // 绘制表头
      doc.setFillColor(220, 220, 220)
      headers.forEach((header, index) => {
        doc.rect(currentX, yPos, colWidths[index], 8, 'F')
        doc.rect(currentX, yPos, colWidths[index], 8)
        doc.text(header, currentX + 1, yPos + 5)
        currentX += colWidths[index]
      })
      yPos += 8

      // 表格数据
      doc.setFont('helvetica', 'normal')
      doc.setFontSize(8)

      records.forEach((record, index) => {
        // 检查是否需要换页
        if (yPos > 270) {
          doc.addPage()
          yPos = 20

          // 重新绘制表头
          doc.setFontSize(9)
          doc.setFont('helvetica', 'bold')
          currentX = 20
          doc.setFillColor(220, 220, 220)
          headers.forEach((header, index) => {
            doc.rect(currentX, yPos, colWidths[index], 8, 'F')
            doc.rect(currentX, yPos, colWidths[index], 8)
            doc.text(header, currentX + 1, yPos + 5)
            currentX += colWidths[index]
          })
          yPos += 8
          doc.setFont('helvetica', 'normal')
          doc.setFontSize(8)
        }

        currentX = 20
        const rowData = [
          record.date || '',
          (record.projectName || '未分配').substring(0, 12),
          record.firstCheckIn ? formatTime(record.firstCheckIn) : '-',
          record.lastCheckOut ? formatTime(record.lastCheckOut) : '-',
          (record.checkInCount || 0).toString(),
          (record.checkOutCount || 0).toString(),
          formatDuration(record.totalWorkMinutes || 0),
          record.isAbnormal ? '异常' : '正常'
        ]

        // 交替行背景色
        if (index % 2 === 1) {
          doc.setFillColor(248, 248, 248)
          doc.rect(20, yPos, 160, 6, 'F')
        }

        // 绘制数据行
        rowData.forEach((cell, cellIndex) => {
          doc.rect(currentX, yPos, colWidths[cellIndex], 6)

          // 状态列特殊颜色
          if (cellIndex === 7 && cell === '异常') {
            doc.setTextColor(255, 0, 0) // 红色
          } else if (cellIndex === 7 && cell === '正常') {
            doc.setTextColor(0, 128, 0) // 绿色
          } else {
            doc.setTextColor(0, 0, 0) // 黑色
          }

          doc.text(cell, currentX + 1, yPos + 4)
          currentX += colWidths[cellIndex]
        })

        doc.setTextColor(0, 0, 0) // 重置颜色
        yPos += 6
      })
    }

    // 页脚信息
    const pageCount = doc.internal.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i)
      doc.setFontSize(8)
      doc.setTextColor(128, 128, 128)
      doc.text(`导出时间: ${new Date().toLocaleString('zh-CN')}`, 20, 290)
      doc.text(`第 ${i} 页，共 ${pageCount} 页`, 160, 290)
    }

    // 保存文件
    doc.save(`${options.filename}.pdf`)
    console.log('项目PDF导出完成')

  } catch (error) {
    console.error('项目PDF导出失败:', error)
    throw error
  }
}
</script>

<style scoped>
.reports-page {
  padding: 0;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title {
  font-size: 16px;
  font-weight: 500;
}

.dialog-actions {
  display: flex;
  gap: 10px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.realtime-stats {
  margin-bottom: 20px;
}

.stat-card.realtime {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.stat-card.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.empty-data {
  text-align: center;
  padding: 40px 0;
}

.stat-card.realtime .stat-content {
  padding: 15px;
}

.stat-card.realtime .stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.stat-card.realtime .stat-value {
  font-size: 28px;
  font-weight: bold;
  color: white;
  line-height: 1;
}

.stat-card.realtime .stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5px;
}

.filter-card {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.chart {
  height: 300px;
  width: 100%;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px 0;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-count {
  font-size: 14px;
  color: #909399;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 详情对话框样式 */
.employee-detail-content,
.project-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.summary-card {
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 10px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  line-height: 1;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 12px;
  color: #909399;
}

.detail-card {
  margin-bottom: 20px;
}

/* 导出对话框样式 */
.form-tip {
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.el-checkbox-group .el-checkbox {
  margin-right: 0;
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .realtime-stats .el-col {
    margin-bottom: 10px;
  }

  .charts-row .el-col {
    margin-bottom: 20px;
  }

  .summary-item {
    padding: 8px;
  }

  .summary-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .stat-card.realtime .stat-value {
    font-size: 24px;
  }

  .stat-value {
    font-size: 28px;
  }

  .chart {
    height: 250px;
  }

  .summary-value {
    font-size: 18px;
  }

  .summary-label {
    font-size: 11px;
  }
}
</style>
